<?php

namespace App\Traits;

use <PERSON><PERSON>\Sanctum\HasApiTokens as SanctumHasApiTokens;
use <PERSON><PERSON>\Sanctum\NewAccessToken;

trait HasApiTokens
{
    use SanctumHasApiTokens;

    /**
     * 创建新的令牌
     *
     * @param string $name
     * @param array $abilities
     * @param array $extraData
     * @return \Laravel\Sanctum\NewAccessToken
     */
    public function createToken(string $name, array $abilities = ['*'], array $extraData = [])
    {
        $plainTextToken = $this->generateTokenString();

        $token = $this->tokens()->create([
            'name' => $name,
            'token' => hash('sha256', $plainTextToken),
            'abilities' => $abilities,
            'last_used_at' => now(),
            'expires_at' => now()->addMinutes(config('sanctum.expiration')),
            'platform' => $extraData['platform'],
        ]);

        return new NewAccessToken($token, $plainTextToken);
    }
}
