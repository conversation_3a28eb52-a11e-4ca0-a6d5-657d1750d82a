<?php

namespace App\Observers;

use App\Models\Ers\ServiceOrder;
use App\Services\Ers\ModuleService;

class ServiceOrderObserver
{
    /**
     * Handle the ServiceOrder "updated" event.
     */
    public function updated(ServiceOrder $serviceOrder): void
    {
        if ($serviceOrder->wasChanged(['status']) && $serviceOrder->getOriginal('status') != $serviceOrder->status) {
            if (in_array($serviceOrder->status, [ServiceOrder::STATUS_USER_PENDING, ServiceOrder::STATUS_ADMIN_PENDING])) {
                $currentStep = $serviceOrder->steps
                    ->where('status', $serviceOrder->status)
                    ->sortBy('step_id')
                    ->first();

                // 消息通知
                $module = ModuleService::getModule($currentStep->module);
                $module::messageNotice($serviceOrder, $currentStep);
            }
        }
    }
}
