<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Admin\Controller;
use App\Libs\Sms\SmsTemplate;
use App\Models\SmsRecord;
use App\Rules\PhoneNumber;
use Carbon\Carbon;
use Illuminate\Http\Request;

class SmsController extends Controller
{

    public function index(Request $request)
    {
        $params = $request->validate([
            'phone' => [new PhoneNumber()],
            'token' => []
        ]);

        $recordsQuery = SmsRecord::query()
            ->whereIn('type', [SmsTemplate::LOGIN, SmsTemplate::BIND])
            ->where('created_at', '>', Carbon::now()->subHours(24))
            ->when(isset($params['phone']), fn($query) => $query->where('phone', $params['phone']))
            ->select(['phone', 'type', 'content', 'created_at'])
            ->orderByDesc('id');

        return $this->apiPaginate($request, $recordsQuery);
    }

}
