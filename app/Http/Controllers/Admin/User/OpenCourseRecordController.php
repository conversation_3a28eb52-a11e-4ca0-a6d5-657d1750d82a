<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Admin\Controller;
use App\Models\OpenCourseBatch;
use App\Models\OpenCourseRecord;
use App\Services\Import\CourseImportService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class OpenCourseRecordController extends Controller
{
    public function index(Request $request)
    {
        $params = $request->validate([
            'batch_id' => 'integer',
            'user_id' => 'integer',
            'org_id' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = OpenCourseRecord::query()->with(['user', 'org']);
        $this->builderWhere($builder, $params, ['user_id', 'batch_id', 'org_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        $data = $this->apiPaginate($request, $builder);

        if (!empty($data['records'])) {
            foreach ($data['records'] as &$item) {
                $item->courses = $item->courses()->get();
                $item->topics = $item->topics()->get();
                $item->packs = $item->packs()->get();
            }
        }

        return $data;
    }

    public function batch(Request $request)
    {
        $params = $request->validate([
            'created_at' => 'array',
        ]);

        $builder = OpenCourseBatch::query()->with('admin');
        $this->builderWhere($builder, $params, ['created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }

    public function import(Request $request)
    {
        $file = $request->file('file');
        Excel::import(new CourseImportService, $file);

        return ['message' => 'success'];
    }

    public function template()
    {
        return config('app.url') . '/static/excel/安管合规注安课程及题库学员名单开课统计表.xlsx';
    }
}
