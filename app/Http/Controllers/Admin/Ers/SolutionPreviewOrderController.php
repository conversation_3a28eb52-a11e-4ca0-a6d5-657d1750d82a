<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Models\Ers\SolutionOrderPreview;
use App\Services\Common\AttachmentService;
use App\Services\Ers\Modules\SolutionPreviewModule;
use App\Services\Ers\ServiceOrderStepService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 方案预览&工单控制器
 */
class SolutionPreviewOrderController extends Controller
{

    /**
     * 获取上传用的 Token
     */
    public function uploadForm(int $orderId, int $orderStepId)
    {
        $disk = config('heguibao.storage.disk.priv');
        return AttachmentService::getUploadForm($orderStepId, ['image', 'doc'], 1024*100, $disk);
    }

    /**
     * 上传预览方案
     *
     * files_add: ['abc/def.jpg', 'pub:aaa/bbb.jpg'] - 新添加的文件的 Key 列表
     * files_remove: ['abc/def.jpg', 'aaa/bbb.jpg'] - 删除的文件路径
     */
    public function store(Request $request, int $orderId, int $stepId)
    {
        $params = $request->validate([
            'files_add' => 'nullable|array',
            'files_remove' => 'nullable|array'
        ]);

        $addFiles = $params['files_add'] ?? [];
        $removeFiles = $params['files_remove'] ?? [];

        /** @var ServiceOrderStep $step */
        $step = ServiceOrderStep::query()
            ->where('order_id', $orderId)
            ->where('step_id', $stepId)
            ->where('module', SolutionPreviewModule::configure()->t)
            ->firstOrFail();

        if ($step->status == ServiceOrderStep::STATUS_FINISH) {
            throw new BadRequestHttpException('方案预览已经确认完成，无法继续修改。');
        }

        $preview = $step->data;

        if (!$preview) {
            $preview = new SolutionOrderPreview();
            $preview->order_id = $orderId;
            $preview->order_step_id = $step->id;
            $preview->files = [];
        }

        $files = $preview->files;

        //新增的文件
        foreach ($addFiles as $path) {
            $file = AttachmentService::store($path, 'ers-solutions', BusinessType::ErsOrder, $orderId);
            $files[] = [
                'path' => $file->path,
                'mime' => $file->mime,
                'filename' => $file->filename,
                'filesize' => $file->filesize,
                'id' => $file->id
            ];
        }

        //移除的文件
        foreach ($removeFiles as $path) {
            AttachmentService::removeRelations(BusinessType::ErsOrder, $orderId, $path);
            $files = array_filter($files, fn($row) => $row['path'] != $path);
        }

        if ($files != $preview->files) {
            $files = array_values($files);
            $preview->files = $files;
        }

        $preview->save();

        $step->data_id = $preview->id;
        $step->status = ServiceOrderStep::STATUS_USER_PENDING;
        $step->last_admin_handled_at = now();
        $step->save();

        ServiceOrderStepService::forward($step->order, $step);

        return $step;
    }

}
