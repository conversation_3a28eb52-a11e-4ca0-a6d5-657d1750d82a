<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\FormOrderData;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Ers\Modules\FormModule;
use App\Services\Ers\ServiceOrderStepService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 表单模块&工单控制器
 */
class FormOrderController extends Controller
{

    /**
     * 审核表单，如果全部审核通过，则推进至下一步
     *
     *  audits: [
     *     1 => ['pass'=>true],
     *     表单项数据 ID => ['pass'=>false, 'reject_reason'=>'填写不符合需要'],
     * ]
     */
    public function store(Request $request, int $orderId, int $stepId)
    {
        $params = $request->validate([
            'audits' => 'array|required'
        ]);

        $audits = $params['audits'];

        /** @var ServiceOrder $order */
        $order = ServiceOrder::query()->findOrFail($orderId);

        /** @var ServiceOrderStep $step */
        $step = ServiceOrderStep::query()
            ->where('order_id', $orderId)
            ->where('step_id', $stepId)
            ->where('module', FormModule::configure()->t)
            ->with(['data', 'data.data', 'data.data.input'])
            ->firstOrFail();

        if ($step->status != ServiceOrderStep::STATUS_ADMIN_PENDING) {
            throw new BadRequestHttpException('该步骤尚不能审核。');
        }

        /** @var ?Collection<FormOrderData> $formData */
        $formData = $step->data?->data;

        DB::beginTransaction();

        try {
            //是否全部通过了
            $allPass = true;

            foreach ($formData as $row) {
                if ($row->status == FormOrderData::STATUS_SUCCESS) {
                    continue;
                }
                if (!isset($audits[$row->input->id])) {
                    throw new BadRequestHttpException("字段{$row->input->title}未审核");
                }

                if ($audits[$row->input->id]['pass']) {
                    $row->status = FormOrderData::STATUS_SUCCESS;
                    $row->reject_reason = '';
                } else {
                    $row->status = FormOrderData::STATUS_REJECT;
                    $row->reject_reason = $audits[$row->input->id]['reject_reason'];
                    $allPass = false;
                }

                $row->save();
            }

            $step->last_admin_handled_at = now();

            if ($allPass) {
                $step->status = ServiceOrderStep::STATUS_FINISH;
                $step->finished_by = ServiceOrderStep::FINISHED_BY_ADMIN;
                $step->save();
            } else {
                $step->status = ServiceOrderStep::STATUS_USER_PENDING;
                $step->save();
            }

            ServiceOrderStepService::forward($order, $step);

            DB::commit();

            return $step;

        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

}
