<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\FormLibrary;
use App\Services\Admin\OperateLogService;
use App\Services\Ers\FormLibraryService;
use Illuminate\Http\Request;

class FormLibraryController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'title' => 'string',
            'type' => 'string',
            'created_at' => 'array',
        ]);

        $builder = FormLibrary::query();
        $this->builderWhere($builder, $params, ['type', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (isset($params['title']) && $params['title']) {
            $builder->where('title', 'like', "%{$params['title']}%");
        }

        return $this->apiPaginate($request, $builder);
    }

    public function search(): array
    {
        return FormLibrary::query()->get()->toArray();
    }

    public function store(Request $request): FormLibrary
    {
        $typeMap = FormLibrary::getTypeLabels();

        $params = $request->validate([
            'title' => 'required|string|min:2',
            'type' => 'string|in:' . implode(',', array_keys($typeMap)),
            'is_required' => 'boolean',
            'desc' => 'string',
            'options' => 'array|nullable'
        ]);

        $info = FormLibraryService::create($params);

        OperateLogService::create(auth('admin')->id(),'创建基础表单组件', $info->id);

        return $info;
    }

    public function update(Request $request, int $id): FormLibrary
    {
        $params = $request->validate([
            'title' => 'string|min:2',
            'is_required' => 'boolean',
            'desc' => 'string',
            'options' => 'array'
        ]);

        $info = FormLibraryService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改基础表单组件', $info->id);

        return $info;
    }

    public function destroy(int $id): array
    {
        FormLibraryService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除基础表单组件', ['id' => $id]);

        return $this->success();
    }
}
