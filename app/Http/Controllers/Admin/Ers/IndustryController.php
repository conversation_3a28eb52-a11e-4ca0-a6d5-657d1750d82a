<?php

namespace App\Http\Controllers\Admin\Ers;

use App\Http\Controllers\Admin\Controller;
use App\Models\Ers\Industry;
use App\Services\Admin\OperateLogService;
use App\Services\Ers\IndustryService;
use Illuminate\Http\Request;

class IndustryController extends Controller
{
    public function search(): array
    {
        return Industry::query()->orderBy('sort')->get()->toArray();
    }

    public function store(Request $request): Industry
    {
        $params = $request->validate([
            'name' => 'required|string|min:2',
        ]);

        $info = IndustryService::create($params);

        OperateLogService::create(auth('admin')->id(),'创建行业类别', $info->id);

        return $info;
    }

    public function update(Request $request, int $id): Industry
    {
        $params = $request->validate([
            'name' => 'string|min:2',
        ]);

        $info = IndustryService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改行业类别', $info->id);

        return $info;
    }

    public function move(Request $request): array
    {
        $params = $request->validate([
            'from_id' => 'required|integer',
            'to_id' => 'required|integer',
        ]);

        IndustryService::move($params['from_id'], $params['to_id']);

        return $this->success();
    }

    public function destroy(int $id): array
    {
        IndustryService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除行业类别', ['id' => $id]);

        return $this->success();
    }
}
