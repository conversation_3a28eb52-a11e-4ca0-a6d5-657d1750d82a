<?php

namespace App\Http\Controllers\Admin\Org;

use App\Http\Controllers\Admin\Controller;
use App\Services\Org\TopicService;
use Illuminate\Http\Request;
use App\Models\Org\Topic;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
class TopicController extends Controller
{
    /**
     * 获取机构题库列表
     */
    public function index(Request $request, int $oid)
    {
        $params = $request->validate([
            'keywords' => 'string',
        ]);

        $trainTopicTable = (new \App\Models\Train\Topic)->getTable();
        $topicTable = (new Topic)->getTable();
        $builder = Topic::query()
            ->where('org_id', $oid)
            ->leftJoin($trainTopicTable, $topicTable . '.topic_id', '=', $trainTopicTable . '.id')
            ->select(
                $topicTable . '.*',
                $trainTopicTable . '.name as topic_name'
            );

        if (!empty($params['keywords'])) {
            if (is_numeric($params['keywords'])) {
                $builder->where($topicTable . '.topic_id', $params['keywords']);
            } else {
                $builder->where($trainTopicTable . '.name', 'like', "%{$params['keywords']}%");
            }
        }

        $this->builderOrderBy($request, $builder);
        $data = $this->apiPaginate($request, $builder);
        $data['selected_topic_ids'] = Topic::query()
            ->select('topic_id')
            ->where('org_id', $oid)
            ->pluck('topic_id')
            ->toArray();

        return $data;
    }

    /**
     * 更新机构题库
     */
    public function update(Request $request, $oid, int $topicId)
    {
        $params = $request->validate([
            'price' => 'required|numeric',
            'field' => 'required|string',
        ]);

        $topic = $this->getTopic($oid, $topicId);
        if ($params['field'] == 'price_original_30') {
            $topic->price_original_30 = $params['price'];
        } else {
            $topic->price_original_60 = $params['price'];
        }
        $topic->save();

        return $this->success();
    }

    /**
     * 删除机构题库
     */
    public function destroy(int $oid, int $topicId)
    {
        $topic = $this->getTopic($oid, $topicId);
        // todo 判断是否有人学习了
        $topic->delete();

        return $this->success();
    }

    /**
     * 添加机构题库
     */
    public function store(Request $request, int $oid)
    {
        $params = $request->validate([
            'topics' => 'required|array',
            'topics.*.id' => 'required|integer',
            'topics.*.price30' => 'required|numeric',
            'topics.*.price60' => 'required|numeric',
        ]);

        $topicIds = array_column($params['topics'], 'id');
        $topicsData = collect($params['topics'])->keyBy('id')->map(function($topic) {
            return [
                'price30' => $topic['price30'],
                'price60' => $topic['price60'],
            ];
        })->all();

        // 验证题库是否已添加
        $isAdded = Topic::query()->where('org_id', $oid)->whereIn('topic_id', $topicIds)->exists();
        if ($isAdded) {
            throw new BadRequestHttpException('添加题库中存在已添加的题库，请检查后重试');
        }

        TopicService::add($oid, $topicIds, $topicsData);

        return $this->success();
    }


    /**
     * 获取机构题库
     *
     * @param $oid
     * @param $topicId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|Topic
     */
    protected function getTopic($oid, $topicId)
    {
        $topic = Topic::query()
            ->where('org_id', $oid)
            ->where('topic_id', $topicId)
            ->first();

        if (!$topic) {
            throw new NotFoundHttpException('机构题库不存在或已删除');
        }

        return $topic;
    }
}
