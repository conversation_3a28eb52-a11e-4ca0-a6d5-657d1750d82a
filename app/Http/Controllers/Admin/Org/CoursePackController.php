<?php
/**
 * CoursePackController.php class file.
 *
 * <AUTHOR>
 * @time 2025/4/21 16:02
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Http\Controllers\Admin\Org;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\Content;
use App\Models\Org\CoursePack;
use App\Services\Org\CoursePackService;
use App\Services\Org\CourseService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CoursePackController extends Controller
{
    /**
     * 获取机构课程列表
     */
    public function index(Request $request, int $oid)
    {
        $params = $request->validate([
            'keywords' => 'string',
        ]);

        $contentTable = (new Content)->getTable();
        $coursePackTable = (new CoursePack)->getTable();
        $builder = CoursePack::query()
            ->where('org_id', $oid)
            ->leftJoin($contentTable, $coursePackTable . '.course_pack_id', '=', $contentTable . '.id')
            ->select([
                $coursePackTable . '.*',
                $contentTable . '.title as course_pack_name'
            ]);

        $this->builderWhere($builder, $params);

        if (!empty($params['keywords'])) {
            if (is_numeric($params['keywords'])) {
                $builder->where($coursePackTable . '.course_pack_id', $params['keywords']);
            } else {
                $builder->where($contentTable . '.name', 'like', "%{$params['keywords']}%");
            }
        }

        $this->builderOrderBy($request, $builder);
        $data = $this->apiPaginate($request, $builder);
        $data['selected_ids'] = CoursePack::query()->where('org_id', $oid)->pluck('course_pack_id')->toArray();

        return $data;
    }

    /**
     * 更新机构课程
     */
    public function update(Request $request, int $oid, int $coursePackId)
    {
        $params = $request->validate([
            'price' => 'required|numeric',
        ]);

        $course = $this->getCoursePack($oid, $coursePackId);
        $course->price_original = $params['price'];
        $course->save();

        return $this->success();
    }

    /**
     * 删除机构课程
     */
    public function destroy(int $oid, int $coursePackId)
    {
        $course = $this->getCoursePack($oid, $coursePackId);
        // todo 判断是否有人学习了
        $course->delete();

        return $this->success();
    }

    /**
     * 添加机构课程
     */
    public function store(Request $request, int $oid)
    {
        $params = $request->validate([
            'course_packs' => 'required|array',
            'course_packs.*.id' => 'required|integer',
            'course_packs.*.price' => 'required|numeric',
        ]);

        $coursePackIds = array_column($params['course_packs'], 'id');
        $coursesData = collect($params['course_packs'])->keyBy('id')->map(function($course) {
            return [
                'price' => $course['price'],
            ];
        })->all();

        // 验证课程包是否已添加
        $isAdded = CoursePack::query()->where('org_id', $oid)->whereIn('course_pack_id', $coursePackIds)->exists();

        if ($isAdded) {
            throw new BadRequestHttpException('添加课程包中存在已添加的课程包，请检查后重试');
        }

        CoursePackService::add($oid, $coursesData);

        return $this->success();
    }

    /**
     * 获取机构课程包
     *
     * @param int $oid
     * @param int $coursePackId
     * @return Builder|Model|object|CoursePack
     */
    protected function getCoursePack(int $oid, int $coursePackId)
    {
        $course = CoursePack::query()
            ->where('org_id', $oid)
            ->where('course_pack_id', $coursePackId)
            ->first();

        if (!$course) {
            throw new NotFoundHttpException('机构课程包不存在或已删除');
        }

        return $course;
    }
}
