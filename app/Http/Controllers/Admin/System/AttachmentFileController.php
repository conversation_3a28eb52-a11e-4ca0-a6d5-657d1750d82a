<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Admin\Controller;
use App\Models\Attachment\AttachmentFile;
use App\Services\Common\AttachmentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AttachmentFileController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'etag' => 'string',
            'created_at' => 'array',
        ]);

        $builder = AttachmentFile::query();

        $this->builderWhere($builder, $params, ['etag', 'created_at']);
        $this->builderOrderBy($request, $builder);

        $data = $this->apiPaginate($request, $builder);

        $data['records']->map(function ($file) {
            $file->path = AttachmentService::url(Storage::disk($file->disk ?: null), $file->path);
        });

        return $data;
    }
}
