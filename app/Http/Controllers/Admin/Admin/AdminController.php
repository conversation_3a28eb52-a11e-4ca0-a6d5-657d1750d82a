<?php

namespace App\Http\Controllers\Admin\Admin;

use App\Http\Controllers\Admin\Controller;
use App\Models\Admin\Admin;
use App\Services\Admin\OperateLogService;
use App\Services\Admin\AdminService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class AdminController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'username' => 'string',
            'status' => 'integer'
        ]);

        $builder = Admin::query()->with(['roles', 'roles.role']);
        $this->builderWhere($builder, $params, ['username', 'status']);
        $this->builderOrderBy($request, $builder, 'id asc');

        return $this->apiPaginate($request, $builder, hidden: ['password']);
    }

    public function show(int $id): ?Admin
    {
        /** @var Admin $detail */
        $detail = Admin::query()->where('id', $id)->first();

        if (!$detail) {
            throw new BadRequestHttpException("账号不存在");
        }

        return $detail;
    }

    public function store(Request $request): Admin
    {
        $params = $request->validate([
            'username' => 'required|string|min:3',
            'password' => 'required|string|min:6|confirmed',
            'real_name' => 'string|min:2',
            'phone' => 'regex:/^1[3-9]\d{9}$/',
            'email' => 'email',
            'status' => 'integer',
            'role_ids' => 'required|array|min:1'
        ]);

        $append = [
            'real_name' => $params['real_name'],
            'phone' => $params['phone'],
            'email' => $params['email'],
            'status' => $params['status'],
            'role_ids' => $params['role_ids'],
        ];

        $admin = AdminService::create($params['username'], $params['password'], $append);

        OperateLogService::create(auth('admin')->id(),'创建管理员', $admin->id);

        return $admin;
    }

    public function update(Request $request, int $id): Admin
    {
        $params = $request->validate([
            'real_name' => 'string|min:2',
            'phone' => 'regex:/^1[3-9]\d{9}$/',
            'email' => 'email',
            'status' => 'integer',
            'role_ids' => 'array|min:1'
        ]);

        $admin = AdminService::update($id, $params);

        OperateLogService::create(auth('admin')->id(), '修改管理员信息', ['id' => $admin->id, 'params' => $params]);

        return $admin;
    }

    public function updatePassword(Request $request, int $id): Admin
    {
        $params = $request->validate([
            'password' => 'required|string|min:6|confirmed'
        ]);

        $admin = AdminService::updatePassword($id, $params['password']);

        OperateLogService::create(auth('admin')->id(), '修改管理员密码', $id);

        return $admin;
    }
}
