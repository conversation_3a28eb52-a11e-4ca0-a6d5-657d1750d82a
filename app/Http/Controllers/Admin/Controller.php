<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

abstract class Controller extends \Illuminate\Routing\Controller
{
    protected string $ip = '';

    protected string $page = 'current';

    protected string $preGage = 'size';

    public function __construct(Request $request)
    {
        $this->ip = $request->getClientIp() ?: '127.0.0.1';
    }

    protected function success(): array
    {
        return ['success' => true];
    }

    protected function builderWhere(Builder $builder, array $params, array $filters = [], string $table = ''): Builder
    {
        foreach ($params as $key => $val) {
            if ($filters && in_array($key, $filters) && $val !== '') {

                if ($table) {
                    $key = $table . '.' . $key;
                }

                if (is_array($val)) {
                    if (count($val) == 2 && (str_ends_with($key, '_at') || str_ends_with($key, '_time') || str_ends_with($key, '_date'))) {
                        $builder->whereBetween($key, $val);
                    } else {
                        $builder->whereIn($key, $val);
                    }
                } else {
                    $builder->where($key, $val);
                }
            }
        }

        return $builder;
    }

    protected function builderOrderBy(Request $request, Builder $builder, string $orderBy = 'id desc'): Builder
    {
        $sorting = $request->input('sorting', '');

        if ($sorting !== '') {
            $orderBy = str_replace('-', ' ', $sorting);
            $orderBy = str_replace('ending', '', $orderBy);
        }

        $builder->orderByRaw($orderBy);

        return $builder;
    }

    /**
     * @return array{current: int, size: int, last: int, total: int, records: Collection}
     */
    protected function apiPaginate(Request $request, Builder $builder, int $perPage = 20, array $hidden= [], array $append = []): array
    {
        $page = $request->input($this->page, 1);
        $perPage = $request->input($this->preGage, $perPage);
        $perPage = max(min($perPage, 1000), 5);
        $offset = ($page - 1) * $perPage;

        $total = $builder->count();
        $list = $builder->offset($offset)->limit($perPage)->get()->setHidden($hidden)->append($append);

        return [
            'current' => intval($page),
            'size' => intval($perPage),
            'last' => ceil($total / $perPage),
            'total' => $total,
            'records' => $list,
        ];
    }
}
