<?php

namespace App\Http\Controllers\Admin\Punish;

use App\Http\Controllers\Admin\Controller;
use App\Models\Punish\PunishTestSubjects;
use Illuminate\Http\Request;

class TestSubjectController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'test_id' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = PunishTestSubjects::query()->with(['subject', 'option']);
        $this->builderWhere($builder, $params, ['test_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }
}
