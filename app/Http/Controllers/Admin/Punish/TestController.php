<?php

namespace App\Http\Controllers\Admin\Punish;

use App\Http\Controllers\Admin\Controller;
use App\Models\Punish\PunishTest;
use Illuminate\Http\Request;

class TestController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'user_id' => 'integer',
            'topic_id' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = PunishTest::query()->with(['user', 'topic']);
        $this->builderWhere($builder, $params, ['user_id', 'topic_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        return $this->apiPaginate($request, $builder);
    }
}
