<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\ContentCoursePack;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentCourseService;
use Illuminate\Http\Request;

class ContentCoursePackageController extends Controller
{
    public function show(int $id)
    {
        /** @var ContentCoursePack $pack */
        $pack = ContentCoursePack::query()
            ->where('content_id', $id)
            ->with('courses')
            ->with('topic')
            ->first();

        $pack?->setHidden([])
            ->courses->each(fn($item) => $item->setHidden([]));

        return $pack;
    }

    public function update(Request $request, int $id)
    {
        $params = $request->validate([
            'course_ids' => 'array|min:1',
            'is_send' => 'boolean',
            'topic_id' => 'integer',
        ]);

        OperateLogService::create(auth('admin')->id(), '修改课程包', $params);

        $topicId = $params['topic_id'] ?? 0;
        $isSend = $params['is_send'] ?? false;
        $pack = ContentCourseService::updatePack($id, $params['course_ids'], $topicId, (bool)$isSend)->setHidden([]);

        $pack?->setHidden([])
            ->courses->each(fn($item) => $item->setHidden([]));

        return $pack;
    }

}
