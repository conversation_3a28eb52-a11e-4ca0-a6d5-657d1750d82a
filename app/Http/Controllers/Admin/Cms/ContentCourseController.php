<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\ContentCourse;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentCourseService;
use Illuminate\Http\Request;

class ContentCourseController extends Controller
{
    public function show(int $id): ?ContentCourse
    {
        /** @var ContentCourse $course */
        $course = ContentCourse::query()->with('topic')->where('content_id', $id)->first();

        if ($course) {
            $course->learning_count = $course->getRawOriginal('learning_count');
            $course->setHidden([]);
        }

        return $course;
    }

    public function update(Request $request, int $id): ContentCourse
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'teacher_name' => 'string',
            'try_view_count' => 'required|integer',
            'learning_count_add' => 'required|integer',
            'is_send' => 'required|integer',
            'hour_per_minutes' => 'required|integer|min:1',
            'topic_id' => 'required|integer',
        ]);

        OperateLogService::create(auth('admin')->id(), '修改课程', $params);

        $course = ContentCourseService::update($id, $params, boolval($params['is_send']))->setHidden([]);
        $course->learning_count = $course->getRawOriginal('learning_count');

        return $course;
    }
}
