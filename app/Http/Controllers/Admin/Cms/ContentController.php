<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Admin\Controller;
use App\Models\Admin\Role;
use App\Models\Cms\Category;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseSection;
use App\Models\Order\Order;
use App\Rules\ArrayOrNumericCase;
use App\Services\Admin\OperateLogService;
use App\Services\Admin\RoleService;
use App\Services\Cms\ContentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class ContentController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'classify' => 'string',
            'id' => 'integer',
            'category_id' => 'integer',
            'keyword' => 'string',
            'is_destroy' => 'integer',
            'recommend' => 'integer',
            'status' => [new ArrayOrNumericCase()],
            'created_at' => 'array',
            'filter_ids' => 'array',
        ]);

        if (isset($params['is_destroy']) && $params['is_destroy']) {
            $builder = Content::onlyTrashed()->with(['resource', 'category']);
        } else {
            $builder = Content::query()->with(['resource', 'category']);
        }

        if (isset($params['filter_ids'])) {
            $builder->whereNotIn('id', $params['filter_ids']);
        }

        $adminId = auth('admin')->id();

        // 内容权限判断
        if (!RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_EDITOR)) {
            $params['admin_id'] = $adminId;
        }

        $this->builderWhere($builder, $params, ['id', 'category_id', 'status', 'admin_id', 'created_at']);
        $this->builderOrderBy($request, $builder);

        if (!empty($params['keyword'])) {
            $builder->where('title', 'like', "%{$params['keyword']}%");
        }

        if (isset($params['classify']) && empty($params['category_id'])) {
            $builder->whereIn('category_id', function ($query) use ($params) {
                $query->select('id')->from((new Category())->getTable())->where('classify', $params['classify']);
            });
        }

        if (isset($params['recommend'])) {
            if ($params['recommend']) {
                $builder->whereNotNull('recommend_at');
            } else {
                $builder->whereNull('recommend_at');
            }
        }

        $data = $this->apiPaginate($request, $builder);

        $courseIds = $data['records']
            ->filter(fn($item) => $item?->resource instanceof ContentCourse)
            ->pluck('id')->toArray();

        $courseDurations = $data['records']
            ->filter(fn($item) => $item->resource instanceof ContentCourse)
            ->whenNotEmpty(
                fn($items) => ContentCourseChapter::query()
                    ->from((new ContentCourseChapter())->getTable(), 'c')
                    ->leftJoin((new ContentCourseSection())->getTable() . ' as s', 's.chapter_id', '=', 'c.id')
                    ->whereIn('c.content_id', $items->pluck('content_id'))
                    ->where('c.status', ContentCourseChapter::STATUS_SHOW)
                    ->where('s.status', ContentCourseSection::STATUS_SHOW)
                    ->groupBy('c.content_id')
                    ->selectRaw('SUM(s.duration) AS duration, c.content_id')
                    ->get()
                    ->pluck('duration', 'content_id'),
                fn() => []
            );

        $data['records']->each(function ($content) use ($courseDurations) {
            $content->resource?->setHidden([]);
            if ($content->type instanceof ContentCourse) {
                $content->resource->hour = $content->resource?->studyHour($courseDurations[$content->id] ?? 0);
            }
        });


        // 查询的状态，支持数组及数字
        $status = [];
        if (isset($params['status'])) {
            if (is_array($params['status'])) {
                $status = $params['status'];
            } else {
                $status = [$params['status']];
            }
        }

        // 购买课程的人数
        if (isset($params['classify']) && $params['classify'] == Category::CLASSIFY_COURSE && !empty($status) && in_array(Content::STATUS_NORMAL, $status)) {
            $contentIds = $data['records']->pluck('id')->toArray();

            $subQuery = Order::query()
                ->selectRaw('business_id, user_id')
                ->where('business_type', BusinessType::CmsCourse)
                ->whereIn('business_id', $contentIds)
                ->where('status', Order::STATUS_PAID)
                ->groupBy(['business_id', 'user_id']);

            $query = DB::table(DB::raw("({$subQuery->toSql()}) as t"));
            $list = $query->mergeBindings($subQuery->getQuery())->selectRaw('business_id,count(*) num')->groupBy('business_id')->get()->toArray();
            $list = array_column($list, null, 'business_id');

            $data['records']->map(function ($content) use ($list) {
                if ($content->resource) {
                    $content->resource->buy_course_users = isset($list[$content->id]) ? $list[$content->id]->num : 0;
                }
            });
        }

        return $data;
    }

    public function search(Request $request)
    {
        $params = $request->validate([
            'admin_id' => 'integer',
            'classify' => 'string',
            'ids' => 'array|min:1',
            'keyword' => 'string'
        ]);

        $builder = Content::query()->with(['category']);

        $adminId = auth('admin')->id();

        // 有管理权限的用户，才能查看他人内容
        if (!empty($params['admin_id'])) {
            $builder->where('admin_id', $params['admin_id']);
        } else {
            if (!RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_EDITOR)) {
                $builder->where('admin_id', $adminId);
            }
        }

        if (!empty($params['keyword'])) {
            $builder->where('title', 'like', "%{$params['keyword']}%");
        }

        if (!empty($params['ids'])) {
            $builder->whereIn('id', $params['ids']);
        } else {
            $builder->where('status', Content::STATUS_NORMAL);
        }

        if (!empty($params['classify'])) {
            $builder->whereIn('category_id', function ($query) use ($params) {
                $query->select('id')->from((new Category())->getTable())->where('classify', $params['classify']);
            });
        }

        return $builder->limit(20)->get()->setHidden([]);
    }

    public function show(int $id): Content
    {
        /** @var Content $content */
        $content = Content::query()
            ->with(['category', 'relationList'])
            ->find($id);

        if (!$content) {
            throw new BadRequestHttpException('内容不存在');
        }

        $content->views = $content->getRawOriginal('views');

        return $content->setHidden([]);
    }

    public function store(Request $request): Content
    {
        $typeMap = array_keys(Content::$typeLabels);
        $viewLimitMap = [Content::VIEW_LIMIT_FREE, Content::VIEW_LIMIT_CREDIT, Content::VIEW_LIMIT_AMOUNT, Content::VIEW_LIMIT_CREDIT_AMOUNT];

        $params = $request->validate([
            'title' => 'required|string|max:128',
            'category_id' => 'required|integer',
            'type' => 'required|integer|in:' . implode(',', $typeMap),
            'cover' => 'string',
            'intro' => 'string|max:255',
            'view_limit' => 'required|integer|in:' . implode(',', $viewLimitMap),
            'charge_credit' => 'integer',
            'charge_amount' => 'numeric',
            'source' => 'string',
            'recommend' => 'integer',
            'views_add' => 'integer',
            'relation_ids' => 'array'
        ]);

        $adminId = auth('admin')->id();

        $params['admin_id'] = $adminId;

        $content = ContentService::create($params['title'], intval($params['category_id']), intval($params['type']), $params)->setHidden([]);
        $content->views = $content->getRawOriginal('views');

        OperateLogService::create($adminId, '添加内容', ['id' => $content->id, ...$params]);

        return $content;
    }

    public function update(Request $request, int $id): Content
    {
        $viewLimitMap = [Content::VIEW_LIMIT_FREE, Content::VIEW_LIMIT_CREDIT, Content::VIEW_LIMIT_AMOUNT, Content::VIEW_LIMIT_CREDIT_AMOUNT];

        $params = $request->validate([
            'title' => 'string|max:128',
            'category_id' => 'integer',
            'cover' => 'string',
            'intro' => 'string|max:255',
            'view_limit' => 'integer|in:' . implode(',', $viewLimitMap),
            'charge_credit' => 'integer',
            'charge_amount' => 'numeric',
            'source' => 'string',
            'recommend' => 'integer',
            'status' => 'integer',
            'views_add' => 'integer',
            'relation_ids' => 'array'
        ]);

        $content = ContentService::update($id, $params)->setHidden([]);
        $content->views = $content->getRawOriginal('views');

        OperateLogService::create(auth('admin')->id(), '修改内容', ['id' => $content->id, ...$params]);

        return $content;
    }

    public function destroy(int $id): array
    {
        ContentService::batchDelete([$id]);

        OperateLogService::create(auth('admin')->id(), '删除内容', ['id' => $id]);

        return $this->success();
    }

    public function batchDestroy(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'force' => 'in:0,1'
        ]);

        ContentService::batchDelete($params['ids'], boolval($params['force'] ?? 0));

        OperateLogService::create(auth('admin')->id(), '删除内容', $params);

        return $this->success();
    }
}
