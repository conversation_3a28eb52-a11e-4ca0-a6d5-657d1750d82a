<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Models\Cms\Content;
use App\Models\Cms\ContentVideo;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentVideoService;
use Illuminate\Http\Request;

class ContentVideoController extends Controller
{
    public function show(int $id): ?ContentVideo
    {
        /** @var ContentVideo $video */
        $video = ContentVideo::query()->where('content_id', $id)->first();

        $video?->setHidden([]);

        return $video;
    }

    public function update(Request $request, int $id): ContentVideo
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'filepath' => 'required|string',
            'is_send' => 'required|integer',
        ]);

        OperateLogService::create(auth('admin')->id(), '修改视频资讯', $params);

        return ContentVideoService::update($id, $params, boolval($params['is_send']))->setHidden([]);
    }

    public function search(Request $request)
    {
        $params = $request->validate([
            'ids' => 'array|min:1',
            'keyword' => 'string',
            'limit' => 'integer'
        ]);

        $params['limit'] = $params['limit'] ?? 20;

        $builder = Content::query()->with(['resource']);

        if (!empty($params['keyword'])) {
            $builder->where('title', 'like', "%{$params['keyword']}%");
        }

        if (!empty($params['ids'])) {
            $builder->whereIn('id', $params['ids']);
        }

        return $builder
            ->where('type', Content::TYPE_VIDEO)
            ->where('status', Content::STATUS_NORMAL)
            ->orderByDesc('views')
            ->limit($params['limit'])
            ->get()
            ->setHidden([]);
    }
}
