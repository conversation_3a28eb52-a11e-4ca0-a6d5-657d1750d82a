<?php

namespace App\Http\Controllers\Admin\Cms;

use App\Http\Controllers\Admin\Controller;
use App\Libs\Filesystem\LocalUploadAdapter;
use App\Models\Attachment\AttachmentFile;
use App\Models\Cms\ContentRichText;
use App\Services\Admin\OperateLogService;
use App\Services\Cms\ContentRichTextService;
use Illuminate\Http\Request;

class ContentRichTextController extends Controller
{
    public function show(int $id): ?ContentRichText
    {
        /** @var ContentRichText $richText */
        $richText = ContentRichText::query()->where('content_id', $id)->first();

        $richText?->setHidden([]);

        return $richText;
    }

    public function update(Request $request, int $id): ContentRichText
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'content' => 'required|string',
            'is_send' => 'required|integer',
        ]);

        OperateLogService::create(auth('admin')->id(), '修改文章资讯', [
            'content_id' => $params['content_id'],
            'is_send' => $params['is_send']
        ]);

        return ContentRichTextService::update($id, $params, boolval($params['is_send']))->setHidden([]);
    }

    public function saveAttachment(Request $request): AttachmentFile
    {
        $params = $request->validate([
            'content_id' => 'required|integer',
            'filepath' => 'required|string',
        ]);

        return ContentRichTextService::saveAttachment(intval($params['content_id']), $params['filepath']);
    }
}
