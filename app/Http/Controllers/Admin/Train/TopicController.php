<?php

namespace App\Http\Controllers\Admin\Train;

use App\Http\Controllers\Admin\Controller;
use App\Models\Train\Topic;
use App\Services\Admin\OperateLogService;
use App\Services\Topic\ImportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class TopicController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'name' => 'string',
            'created_at' => 'array',
            'filter_ids' => 'array',
        ]);

        $builder = Topic::query()->withCount(['subjects']);
        $this->builderWhere($builder, $params, ['id', 'created_at']);
        $this->builderOrderBy($request, $builder, 'sort desc,id asc');

        if (!empty($params['name'])) {
            $builder->where('name', 'like', "%{$params['name']}%");
        }

        if (isset($params['filter_ids'])) {
            $builder->whereNotIn('id', $params['filter_ids']);
        }

        return $this->apiPaginate($request, $builder);
    }

    public function store(Request $request): Topic
    {
        $params = $request->validate([
            'name' => 'required|string',
            'course_category_id' => 'integer',
            'course_content_id' => 'integer',
            'sort' => 'integer',
            'exam_time' => 'integer|min:60|max:180',
            'pass_score' => 'integer|min:60|max:100',
            'exam_config' => 'array'
        ], [], [
            'exam_time' => '考试时间',
            'pass_score' => '通过分数',
            'exam_config' => '考试配置',
        ]);

        if (!empty($params['course_content_id'])) {
            if (Topic::query()->where('course_content_id', $params['course_content_id'])->exists()) {
                throw new BadRequestHttpException('该课程已关联题库');
            }
        }

        $topic = new Topic();

        foreach ($params as $key => $val) {
            if (in_array($key, $topic->getFillable())) {
                $topic->{$key} = $val;
            }
        }

        $topic->save();

        OperateLogService::create(auth('admin')->id(), '添加题库', ['id' => $topic->id, ...$params]);

        return $topic->setHidden([]);
    }

    public function update(Request $request, int $id): Topic
    {
        $params = $request->validate([
            'name' => 'string|min:2',
            'course_category_id' => 'integer',
            'course_content_id' => 'integer',
            'sort' => 'integer',
            'exam_time' => 'integer|min:60|max:180',
            'pass_score' => 'integer|min:60|max:100',
            'exam_config' => 'array'
        ],[], [
            'exam_time' => '考试时间',
            'pass_score' => '通过分数',
            'exam_config' => '考试配置'
        ]);

        /** @var Topic $topic */
        $topic = Topic::query()->find($id);

        if (!empty($params['course_content_id']) && $topic->course_content_id != $params['course_content_id']) {
            if (Topic::query()->where('course_content_id', $params['course_content_id'])->exists()) {
                throw new BadRequestHttpException('该课程已关联题库');
            }
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $topic->getFillable())) {
                $topic->{$key} = $val;
            }
        }

        $topic->save();

        OperateLogService::create(auth('admin')->id(), '修改题库', ['id' => $topic->id, ...$params]);

        return $topic->setHidden([]);
    }

    public function search(Request $request): array
    {
        $params = $request->validate([
            'name' => 'string',
        ]);

        $builder = Topic::query();
        $this->builderOrderBy($request, $builder, 'sort desc,id asc');

        if (!empty($params['name'])) {
            $builder->where('name', 'like', "%{$params['name']}%");
        }

        return $builder->get()->setHidden([])->toArray();
    }

    public function import(Request $request, int $id): array
    {
        $params = $request->validate([
            'filepath' => 'required|string',
        ]);

        [, $tmp] = explode(':', $params['filepath'], 2);

        $filepath = Storage::disk(config('heguibao.storage.local'))->path($tmp);

        app(ImportService::class)->import($id, $filepath);

        OperateLogService::create(auth('admin')->id(), '导入题库', $id);

        return $this->success();
    }
}
