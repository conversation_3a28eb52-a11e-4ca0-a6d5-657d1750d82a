<?php

namespace App\Http\Controllers\Admin\Train;

use App\Http\Controllers\Admin\Controller;
use App\Models\Train\Chapter;
use App\Services\Admin\OperateLogService;
use App\Services\Topic\ChapterService;
use Illuminate\Http\Request;

class ChapterController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'id' => 'integer',
            'topic_id' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = Chapter::query()->with(['topic'])->withCount(['sections']);
        $this->builderWhere($builder, $params, ['id', 'topic_id', 'created_at']);
        $this->builderOrderBy($request, $builder, 'id asc');

        return $this->apiPaginate($request, $builder);
    }

    public function search(Request $request): array
    {
        $params = $request->validate([
            'topic_id' => 'required|integer',
        ]);

        return Chapter::query()->with(['sections'])->where('topic_id', $params['topic_id'])->get()->setHidden([])->toArray();
    }

    public function store(Request $request): Chapter
    {
        $params = $request->validate([
            'topic_id' => 'required|integer',
            'name' => 'required|string',
            'example' => 'required|integer|in:0,1'
        ]);

        $object = ChapterService::create(intval($params['topic_id']), $params['name'], intval($params['example']));

        OperateLogService::create(auth('admin')->id(), '添加题库章', ['id' => $object->id, ...$params]);

        return $object->setHidden([]);
    }

    public function update(Request $request, int $id): Chapter
    {
        $params = $request->validate([
            'name' => 'required|string',
        ]);

        $object = ChapterService::update($id, $params['name']);

        OperateLogService::create(auth('admin')->id(), '修改题库章', ['id' => $object->id, ...$params]);

        return $object->setHidden([]);
    }

    public function destroy(int $id): array
    {
        ChapterService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除题库章', $id);

        return $this->success();
    }
}
