<?php

namespace App\Http\Controllers\Admin\Train;

use App\Http\Controllers\Admin\Controller;
use App\Models\Train\Section;
use App\Services\Admin\OperateLogService;
use App\Services\Topic\SectionService;
use Illuminate\Http\Request;

class SectionController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'topic_id' => 'integer',
            'chapter_id' => 'integer',
            'created_at' => 'array',
        ]);

        $builder = Section::query()->with(['example', 'chapter'])->withCount(['subjects']);
        $this->builderWhere($builder, $params, ['topic_id', 'chapter_id', 'created_at']);
        $this->builderOrderBy($request, $builder, 'id asc');

        return $this->apiPaginate($request, $builder);
    }

    public function store(Request $request): Section
    {
        $params = $request->validate([
            'chapter_id' => 'required|integer',
            'name' => 'required|string',
            'example_content' => 'string',
            'upload_files' => 'array'
        ]);

        $object = SectionService::create(intval($params['chapter_id']), $params['name'], $params);

        OperateLogService::create(auth('admin')->id(), '添加题库节', ['id' => $object->id, ...$params]);

        return $object->setHidden([]);
    }

    public function update(Request $request, int $id): Section
    {
        $params = $request->validate([
            'name' => 'required|string',
            'example_content' => 'string',
            'upload_files' => 'array'
        ]);

        $object = SectionService::update($id, $params['name'], $params);

        OperateLogService::create(auth('admin')->id(), '修改题库节', ['id' => $object->id, ...$params]);

        return $object->setHidden([]);
    }

    public function destroy(int $id): array
    {
        SectionService::remove($id);

        OperateLogService::create(auth('admin')->id(), '删除题库节', $id);

        return $this->success();
    }
}
