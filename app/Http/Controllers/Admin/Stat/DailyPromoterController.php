<?php

namespace App\Http\Controllers\Admin\Stat;

use App\Http\Controllers\Admin\Controller;
use App\Models\Admin\Admin;
use App\Models\Admin\Role;
use App\Models\Admin\UserRole;
use App\Models\Cms\ContentDoc;
use App\Models\Stat\DailyPromoter;
use App\Models\Stat\SearchStat;
use App\Services\Admin\RoleService;
use App\Services\Stat\DailyPromoterService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class DailyPromoterController extends Controller
{
    public function promoter(Request $request): array
    {
        $params = $request->validate([
            'username' => 'string',
            'status' => 'integer',
            'role_id' => 'integer',
            'date' => 'array',
        ]);

        $builder = Admin::query()->with(['roles', 'roles.role', 'stats']);
        $this->builderWhere($builder, $params, ['username', 'status']);
        $this->builderOrderBy($request, $builder, 'id asc');

        if (!empty($params['date'])) {
            $builder->with('stats', function ($query) use ($params){
                $startDate = Carbon::createFromTimestamp($params['date'][0] / 1000);
                $endDate = Carbon::createFromTimestamp($params['date'][1] / 1000);
                $query->whereBetween('created_at', [$startDate, $endDate ]);
            });
        }
        if (!empty($params['role_id'])) {
            $adminIds = UserRole::query()->where('role_id', $params['role_id'])->pluck('admin_id')->toArray();
            if ($adminIds) {
                $builder->whereIn('id', $adminIds);
            } else {
                $builder->where('id', -1);
            }
        }

        return $this->apiPaginate($request, $builder, hidden: ['password']);
    }

    public function getSubtotalData(Request $request): array
    {
        $params = $request->validate([
            'admin_id' => 'integer'
        ]);

        $adminId = auth('admin')->id();

        // 权限判断
        if (RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_PROMOTER)) {
            $params['admin_id'] = $params['admin_id'] ?? $adminId;
        } else {
            $params['admin_id'] = $adminId;
        }

        $today = DailyPromoterService::getSubtotalData($params['admin_id'], Carbon::now()->toDateString());
        $yesterday = DailyPromoterService::getSubtotalData($params['admin_id'], Carbon::yesterday()->toDateString());
        $subtotal = DailyPromoterService::getSubtotalData($params['admin_id']);
        $contentRank = DailyPromoterService::contentDocRank($params['admin_id']);

        return [
            'today' => $today,
            'yesterday' => $yesterday,
            'subtotal' => $subtotal,
            'content_rank' => $contentRank
        ];
    }

    public function getDateList(Request $request): array
    {
        $params = $request->validate([
            'date_range' => 'array|size:2',
            'admin_id' => 'integer'
        ]);

        $adminId = auth('admin')->id();

        // 权限判断
        if (RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_PROMOTER)) {
            $params['admin_id'] = $params['admin_id'] ?? $adminId;
        } else {
            $params['admin_id'] = $adminId;
        }

        if (!isset($params['date_range'])) {
            $endDate = Carbon::yesterday()->toDateString();
            $startDate = Carbon::parse($endDate)->subDays(14)->toDateString();
        } else {
            $startDate = $params['date_range'][0];
            $endDate = $params['date_range'][1];
        }

        $dateRange = DailyPromoterService::getDateRange($startDate, $endDate);

        return DailyPromoter::query()->where('admin_id', $params['admin_id'])->whereBetween('date', $dateRange)->orderBy('date')->get()->toArray();
    }

    /**
     * 统计排行榜
     *
     * @param Request $request
     * @return Collection
     */
    public function rankList(Request $request): Collection
    {
        $params = $request->validate([
            'type' => 'string|in:download,search'
        ]);

        $adminId = auth('admin')->id();

//        // 权限判断
//        if (!RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_PROMOTER)) {
//            throw new AccessDeniedHttpException("暂无权限");
//        }

        switch ($params['type']) {
            case "download":
                $rankList = ContentDoc::publicFields()
                    ->with('content')
                    ->orderByDesc('download_count')
                    ->limit(50)
                    ->get();
                foreach ($rankList as &$rank) {
                    $rank->download_count = $rank->getRawOriginal('download_count');
                }
                unset($rank);
                break;
            case "search":
                $rankList = SearchStat::query()
                    ->select(['keyword', DB::raw("sum(search_count) as search_count")])
                    ->groupBy('keyword')
                    ->orderByDesc('search_count')
                    ->limit(50)
                    ->get();
                break;
            default:
                throw new NotFoundHttpException("该类型不存在");
        }

        return $rankList;
    }

    /**
     * 管理员资料下载排行
     *
     * @param Request $request
     * @return Collection
     */
    public function adminContentRank(Request $request): Collection
    {
        $params = $request->validate([
            'admin_id' => 'integer'
        ]);

        $adminId = auth('admin')->id();

        // 权限判断
        if (RoleService::checkPermission($adminId, Role::PERMISSION_MANAGE_PROMOTER)) {
            $params['admin_id'] = $params['admin_id'] ?? $adminId;
        } else {
            $params['admin_id'] = $adminId;
        }

        $rankList = DailyPromoterService::contentDocRank($params['admin_id']);
        foreach ($rankList as &$rank) {
            $rank->download_count = $rank->getRawOriginal('download_count');
        }
        unset($rank);

        return $rankList;
    }
}
