<?php

namespace App\Http\Controllers\Api\Ers;

use App\Core\Enums\BusinessType;
use App\Models\Ers\PaymentOrderPayment;
use App\Models\Ers\ServiceOrderStep;
use App\Models\Order\Order;
use App\Services\Common\OrderService;
use App\Services\Ers\ModuleService;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 付款模块工单相关控制器
 */
class ServiceOrderPaymentController extends ServiceOrderStepController
{

    /**
     * 付款、预付款、付尾款共享控制器，所以模块名要多个进行验证
     * @var string[]
     */
    private $sharedModules = ['payment', 'payment_stage', 'payment_final'];

    /**
     * 获取付款流程状态数据
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=16b788d8f6c07a
     */
    public function show($sid, $flowStepId)
    {
        $order = $this->getOrder($sid);
        $orderStep = $this->getStep($order, $flowStepId, $this->sharedModules);

        //发起支付时，必须已经有支付数据
        if (!$orderStep->data_id || !$orderStep->data) {
            throw new BadRequestHttpException('尚不可支付，还未到该流程。');
        }

        $module = ModuleService::getModule($orderStep->module);

        return $orderStep->toStepArray() + [
            'payment' => $module::convertOrderStepData($order, $orderStep)
        ];
    }

    /**
     * 创建付款订单
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=16c17e7076c0bf
     */
    public function store($sid, $flowStepId)
    {
        $order = $this->getOrder($sid);
        $orderStep = $this->getStep($order, $flowStepId, $this->sharedModules);

        if ($orderStep->status != ServiceOrderStep::STATUS_USER_PENDING) {
            throw new BadRequestHttpException('还不能支付。');
        }

        $userId = auth()->id();

        /** @var PaymentOrderPayment $payment */
        $payment = $orderStep->data;

        if (!$payment) {
            throw new BadRequestHttpException('貌似有点问题');
        }

        $payOrder = Order::query()
            ->where('user_id', $userId)
            ->where('business_type', BusinessType::ErsPayment)
            ->where('business_id', $payment->id)
            ->where('total_amount', $payment->pay_amount)
            ->where('status', Order::STATUS_UNPAID)
            ->first();

        if (!$payOrder) {
            $payOrder = OrderService::create($userId, $payment, $orderStep->data->pay_amount, businessType: BusinessType::ErsPayment);
        }

        return $payOrder;
    }

}
