<?php

namespace App\Http\Controllers\Api\Ers;

use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Services\Ers\ProjectService;
use App\Services\Ers\ServiceOrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PageController extends ServiceOrderStepController
{
    /**
     * 大厅页面
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=18eef52afe2031
     *
     * @return array
     */
    public function hall(): array
    {
        $userId = Auth::id();
        // 进度
        $orderProgress = ServiceOrderService::orderProgress($userId);
        // 服务列表
        $projectList = ProjectService::getProjectList();

        return ['order_progress' => $orderProgress, 'project_list' => $projectList];
    }

    /**
     * 服务页面
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=18f2e1ff3e204a
     *
     * @param Request $request
     * @return array
     */
    public function order(Request $request): array
    {
        $params = $this->validate($request, [
            'status' => 'integer|min:1'
        ]);

        $status = $params['status'] ?? 0;

        $builder = ServiceOrder::query()
            ->where('user_id', Auth::id())
            ->where('status', '>', ServiceOrder::STATUS_DRAFT);
        if (!empty($status)) {
            $builder->where('status', $status)->orderByDesc('created_at');
        } else {
            $builder->orderByDesc('created_at');
        }

        $orders = $builder->get();
        if ($orders->isEmpty()) {
            return [];
        }

        $orderProgressList = [];
        /** @var ServiceOrder $order */
        foreach ($orders as $order) {
            $orderProgressList[] = [
                'sid' => $order->sid,
                'status' => $order->status,
                'project' => [
                    'id' => $order->project_id,
                    'title' => $order->project->title,
                ],
                'user' => $order->user->nickname,
                'created_at' => $order->created_at
            ];
        }

        return $orderProgressList;
    }
}
