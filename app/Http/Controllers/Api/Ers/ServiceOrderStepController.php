<?php

namespace App\Http\Controllers\Api\Ers;

use App\Http\Controllers\Api\Controller;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

abstract class ServiceOrderStepController extends Controller
{

    protected function getOrder($sid): ServiceOrder
    {
        $userId = auth()->id();

        $order = ServiceOrder::whereSid($sid)->where('user_id', $userId)->first();

        if (!$order) {
            throw new BadRequestHttpException('工单不存在或无权访问');
        }

        return $order;
    }

    /**
     * @param ServiceOrder $order 工单
     * @param int $flowStepId 流程步骤 ID
     * @param string|array $verifyModule 要验证的模块标识，如果多个模块共享则传递多个标识的数组
     * @return ServiceOrderStep
     */
    protected function getStep(ServiceOrder $order, $flowStepId, $verifyModule): ServiceOrderStep
    {
        $query = ServiceOrderStep::where('order_id', $order->id)
            ->where('step_id', $flowStepId);


        if ($verifyModule) {
            if (is_array($verifyModule)) {
                $query = $query->whereIn('module', $verifyModule);
            } else {
                $query = $query->where('module', $verifyModule);
            }
        }

        $orderStep = $query->first();

        if (!$orderStep) {
            throw new BadRequestHttpException('流程不存在或访问错误');
        }

        return $orderStep;
    }

}
