<?php

namespace App\Http\Controllers\Api\Ers;

use App\Http\Controllers\Api\Controller;
use App\Models\Ers\EnterpriseCategory;
use App\Models\Ers\FormProjectForm;
use App\Services\Ers\ProjectService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class EnterPriseController extends Controller
{
    /**
     * 企业列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $this->validate($request, [
            'project_id' => 'required|integer',
            'industry_id' => 'required|integer'
        ]);

        $project = ProjectService::getProject($params['project_id']);

        if (!$project) {
            throw new NotFoundHttpException("项目不存在");
        }

        $builder = FormProjectForm::query()->where('project_id', $project->id);
        if ($params['industry_id'] > 0) {
            $builder->where('industry_id', $params['industry_id']);
        }
        $enterpriseIds = $builder->get()->pluck('enterprise_id')->unique()->toArray();
        if (empty($enterpriseIds)) {
            return [];
        }

        return EnterpriseCategory::publicFields()
            ->whereIn('id', $enterpriseIds)
            ->orderBy('sort')
            ->get()
            ->toArray();
    }
}
