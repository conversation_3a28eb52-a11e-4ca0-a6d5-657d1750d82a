<?php

namespace App\Http\Controllers\Api\Ers;

use App\Http\Controllers\Api\Controller;
use App\Models\Ers\FlowStep;
use App\Services\Ers\ProjectService;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ProjectController extends Controller
{
    /**
     * 项目列表
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=29b69af8b32001
     *
     * @return array
     */
    public function index(): array
    {
        return ProjectService::getProjectList();
    }

    /**
     * 项目详情
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=20fbba117e224e
     */
    public function show(string $id)
    {
        $project = ProjectService::getProject($id);

        if (!$project) {
            throw new NotFoundHttpException("项目不存在");
        }

        $steps = FlowStep::query()
            ->where('flow_id', $project->flow_id)
            ->orderBy('step')
            ->publicFields()
            ->get();

        $project = $project->toArray();
        $project['steps'] = $steps;

        return $project;
    }
}
