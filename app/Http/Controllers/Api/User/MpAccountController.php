<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Api\Controller;
use App\Models\User\UserMpAccount;
use Illuminate\Http\Request;

class MpAccountController extends Controller
{
    /**
     * 获取用户小程序授权信息
     *
     * @return array
     */
    public function show(): array
    {
        $mpAccount = UserMpAccount::publicFields()
            ->where('user_id', auth()->id())
            ->where('platform', UserMpAccount::PLATFORM_WECHAT_MP)
            ->first();

        return ['mp_account' => $mpAccount];
    }

    /**
     * 增加微信小程序授权数据
     *
     * @param Request $request
     * @return UserMpAccount
     */
    public function store(Request $request): UserMpAccount
    {
        $params = $this->validate($request, [
            'open_id' => 'required|string'
        ]);

        $mpAccount = new UserMpAccount();
        $mpAccount->user_id = auth()->id();
        $mpAccount->open_id = $params['open_id'];
        $mpAccount->platform = UserMpAccount::PLATFORM_WECHAT_MP;
        $mpAccount->save();

        return $mpAccount->publicFields()->first();
    }
}
