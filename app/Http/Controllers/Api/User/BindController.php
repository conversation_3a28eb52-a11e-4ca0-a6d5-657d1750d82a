<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Api\Controller;
use App\Models\User\UserBind;

class BindController extends Controller
{
    /**
     * 绑定列表
     * @return void
     */
    public function index()
    {
        $uid = auth()->id();
        $bind = UserBind::query()->where('user_id', $uid)->select(['user_id', 'platform', 'nickname', 'avatar', 'created_at'])
            ->get();

        $bind->append('labels');
        return $bind;
    }

}
