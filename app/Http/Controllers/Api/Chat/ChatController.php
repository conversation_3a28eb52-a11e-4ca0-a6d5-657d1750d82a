<?php

namespace App\Http\Controllers\Api\Chat;

use App\Http\Controllers\Api\Controller;
use App\Services\Chat\AI\BaiduAIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Throwable;

class ChatController extends Controller
{
    public function chat(Request $request)
    {
        $params = $request->validate([
            'prompt' => ['required', 'string'],
            'messages' => ['array'],
            'stream' => ['integer'],
            'mode' => ['in:web,applet']
        ]);

        $params['stream'] = $params['stream'] ?? 0;
        $params['mode'] = $params['mode'] ?? 'web';
        $params['messages'] = $params['messages'] ?? [];

        if ($params['stream']) {
            return response()->stream(function () use ($params) {
                app(BaiduAIService::class)->chat($params['prompt'], $params['messages'], function ($data, $length) use ($params) {
                    if ($params['mode'] == 'web') {
                        // 网页
                        if (empty($data)) {
                            echo "event: close\n";

                            return 0;
                        } else {
                            foreach ($data as $item) {
                                if ($item['is_end']) {
                                    echo "event: close\n";
                                }

                                echo "data: ". json_encode($item). "\n\n";
                            }

                            ob_flush();
                            flush();

                            return $length;
                        }
                    } else {
                        // 微信小程序
                        if ($length > 1) {
                            echo json_encode($data) . "\n\n";
                        } else {
                            echo json_encode([]) . "\n\n";
                        }

                        ob_flush();
                        flush();

                        return $length;
                    }
                });

            }, 200, ['Content-Type' => 'text/event-stream', 'X-Accel-Buffering' => 'no', 'Cache-Control' => 'no-cache']);
        } else {
            return app(BaiduAIService::class)->chat($params['prompt']);
        }
    }


    /**
     * 创建流式数据
     *
     * @param Request $request
     * @return array
     * @throws Throwable
     */
    public function streamCreate(Request $request): array
    {
        $params = $request->validate([
            'prompt' => ['required', 'string'],
            'messages' => ['array']
        ]);

        $params['messages'] = $params['messages'] ?? [];

        $userId = auth()->id();
        $lock = Cache::lock('chat:lock-' . $userId, 5);

        if (!$lock->get()) {
            throw new BadRequestHttpException('请求频繁，请稍后重试');
        }

        $hash = md5($userId . microtime(true));

        register_shutdown_function(function () use ($params, $hash) {
            app(BaiduAIService::class)->chat($params['prompt'], $params['messages'], function ($data, $length) use ($hash) {
                $redis = Redis::connection()->client();

                if ($length > 1) {
                    $redis->rPush($hash, json_encode($data));
                }

                $ttl = $redis->ttl($hash);
                if ($ttl < 0) {
                    $redis->expire($hash, 1800);
                }

                return $length;
            });
        });

        return [
            'success' => true,
            'data' => $hash,
            'messages' => $params['messages']
        ];
    }

    /**
     * 接收流式数据
     *
     * @param Request $request
     * @return array
     * @throws Throwable
     */
    public function streamCallback(Request $request): array
    {
        $params = $this->validate($request, [
            'hash' => ['required', 'string'],
        ]);

        $redis = Redis::connection()->client();
        $max = 60;
        $hasKey = false;

        // 判断key是否存在
        while ($max) {
            if ($redis->exists($params['hash'])) {
                $hasKey = true;
                break;
            }

            $max--;

            sleep(1);
        }

        if (!$hasKey) {
            throw new BadRequestHttpException('请求超时，请稍后重试');
        }

        $data = [];
        $max = 10;

        // 等待数据
        while ($max) {
            if ($redis->lLen($params['hash']) > 0) {
                $temp = $redis->lPop($params['hash']);
                $temp = json_decode($temp, true);

                if (!is_array($temp)) {
                    break;
                }

                foreach ($temp as $item) {
                    // 队列数据取完，删除缓存数据
                    if (!empty($item['is_end'])) {
                        $redis->del($params['hash']);
                    }
                }

                $data = array_merge($data, $temp);
            } else {
                break;
            }

            $max--;
        }

        return [
            'success' => true,
            'data' => $data
        ];
    }
}
