<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\ServiceException;
use App\Services\Common\PhoneCodeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

class PhoneCodeController extends Controller
{

    /**
     * 发送手机验证码
     * @see https://console-docs.apipost.cn/preview/d3eb2472001c4907/37feea0f756c6e80?target_id=2decd981-9824-45f5-8acd-95e41fc7fd7a
     */
    public function store(Request $request)
    {
        $data = $this->validate($request, [
            'phone' => 'required',
            'type' => 'required|in:'.join(',', PhoneCodeService::$codeTemplates)
        ], [
            'type' => [
                'required' => '验证码类型不能为空',
                'in' => '验证码类型错误'
            ]
        ]);

        $phone = $data['phone'];
        $ip = $request->getClientIp();

        $minuteLimitKey = 'phone-code-rate:'.$phone;
        $ipLimitKey = 'phone-code-rate-ip:'.$ip;

        // 限流
        if (RateLimiter::tooManyAttempts($minuteLimitKey, 1)) {
            $leftSeconds = RateLimiter::availableIn($minuteLimitKey);
            throw new TooManyRequestsHttpException($leftSeconds, '您发送的太快了，请稍候再试。');
        }

        // 验证码IP频率限制, 5个小时 / 20 条
        if (RateLimiter::tooManyAttempts($ipLimitKey, 20)) {
            $left = RateLimiter::availableIn($ipLimitKey);
            $hour = ceil($left / 3600);
            $tip = $hour < 1 ? '稍候' : $hour.'小时后';
            throw new TooManyRequestsHttpException($left, "您好，短信验证码获取达到上限，请{$tip}再试~");
        }

        try {
            PhoneCodeService::send($phone, $data['type']);
            RateLimiter::hit($minuteLimitKey);
            RateLimiter::hit($ipLimitKey, 3600*5);
        } catch (\InvalidArgumentException) {
            throw new BadRequestHttpException('手机号码格式有误');
        } catch (ServiceException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        return $this->ok();
    }

}
