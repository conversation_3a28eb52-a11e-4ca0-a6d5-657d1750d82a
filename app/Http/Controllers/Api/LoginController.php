<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\ServiceException;
use App\Exceptions\UnboundUserException;
use App\Libs\Sms\SmsTemplate;
use App\Libs\Wechat\EasyWechatFactory;
use App\Models\Org;
use App\Models\User;
use App\Models\User\UserBind;
use App\Rules\PhoneNumber;
use App\Services\Common\PhoneCodeService;
use App\Services\User\LoginService;
use App\Services\WechatService;
use EasyWeChat\Kernel\Exceptions\HttpException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class LoginController extends Controller
{

    /**
     * 手机号验证码登录
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=727e82a0-d5ce-473d-92f2-217b795c55c1
     */
    public function phoneCode(Request $request)
    {
        $params = $this->validate($request, [
            'phone' => ['required', new PhoneNumber()],
            'code' => 'required',
            'platform' => 'required|string',
            'system' => 'required|string',
            'referral' => 'string'
        ]);
        $referral = $params['referral'] ?? null;
        Log::info("referral: $referral");

        if (!PhoneCodeService::verify($params['phone'], $params['code'], SmsTemplate::LOGIN)) {
            throw new BadRequestHttpException('验证码不正确或已过期');
        }

        return LoginService::loginByPhone($params['phone'], $request->getClientIp(), $params['platform'], $params['system'], $params['referral'] ?? null);
    }

    /**
     * 小程序授权登录
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=c8a530e1-0de4-42c5-b8cf-0b8c5468c729
     */
    public function wechatMiniApp(Request $request)
    {
        $params = $this->validate($request, [
            'code' => 'required|string',
            'platform' => 'required|string',
            'system' => 'required|string',
            'referral' => 'string'
        ]);

        $referral = $params['referral'] ?? null;
        Log::info("referral: $referral");

        try {
            $data = EasyWechatFactory::mp()
                ->getUtils()
                ->codeToSession($params['code']);
        } catch (\Exception $e) {
            Log::warning("微信小程序授权登录失败：{$e->getMessage()}");
            throw new BadRequestHttpException('登录失败，请稍候再试。');
        }

        $openId = $data['openid'];
        $unionId = $data['unionid'] ?? null;

        //$openId = 'asjd0fh89023a43eg';
        //$unionId = 'ang0bh90awtged';

        return $this->wrapBindLogin(fn() => LoginService::loginByBind(UserBind::PLATFORM_WECHAT_MP, $openId, $request->getClientIp(), $params['platform'], $params['system'], $unionId, $params['referral'] ?? null));
    }

    /**
     * 小程序 OpenID 获取
     * @see
     */
    public function wechatMiniAppOpenId(Request $request)
    {
        $params = $this->validate($request, [
            'code' => 'required|string'
        ]);

        try {
            $data = EasyWechatFactory::mp()
                ->getUtils()
                ->codeToSession($params['code']);
        } catch (\Exception $e) {
            Log::warning("微信小程序授权登录失败：{$e->getMessage()}");
            throw new BadRequestHttpException('登录失败，请稍候再试。');
        }

        return [
            'openid' => $data['openid']
        ];
    }

    /**
     * 绑定账号（通过手机号验证码或者微信快捷授权手机号）
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=cc705557-1da5-4863-94e6-9375153ad14e
     */
    public function bind(Request $request)
    {
        $params = $this->validate($request, [
            'bindCode' => 'required|string',
            'from' => 'required|string',
            'phone' => 'required_if:from,phoneCode',
            'code' => 'required|string',
            'platform' => 'required|string',
            'system' => 'required|string',
            'referral' => 'string'
        ]);

        //验证绑定信息
        $bind = LoginService::getBindData($params['bindCode']);

        if (!$bind) {
            throw new BadRequestHttpException('微信登录异常，请返回重新登录。');
        }

        $phone = '';

        switch ($params['from']) {
            case 'phoneCode':
                if (!PhoneCodeService::verify($params['phone'], $params['code'], SmsTemplate::BIND)) {
                    throw new BadRequestHttpException('验证码错误或已失效。');
                }
                $phone= $params['phone'];
                break;

            case 'wechatMiniAppPhone':
                try {
                    $client = EasyWechatFactory::mp()->getClient();
                    $response = $client->postJson('/wxa/business/getuserphonenumber', [
                        'code' => $params['code'],
                        'openid' => $bind['openId']
                    ]);
                    if ($response->isSuccessful()) {
                        $phone = $response->toArray()['phone_info']['purePhoneNumber'];
                    } else {
                        $data = $response->toArray();
                        throw new \Exception('['.$data['errcode'].'] '.$data['errmsg']);
                    }
                } catch (\Exception $e) {
                    Log::warning("微信小程序授权登录失败：{$e->getMessage()}");
                    throw new BadRequestHttpException('登录失败，请稍候再试。');
                }
                break;

            default:
                throw new BadRequestHttpException('参数错误');
        }

        $login = LoginService::loginByPhone($phone, $request->getClientIp(), $params['platform'], $params['system'], $params['referral'] ?? null);

        UserBind::bind($login['user']->id, $bind['platform'], $bind['openId'], $bind['unionId'] ?: '');
        LoginService::removeBindData($params['bindCode']);

        return $login;
    }

    /**
     * 通过身份证登录(机构学员)
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=36a2380b30d002
     */
    public function idCard(Request $request)
    {
        $params = $this->validate($request, [
            'id_number' => 'required|string',
            'password' => 'required|string',
            'platform' => 'required|string',
            'system' => 'required|string',
            'org_sid' => 'required|string'
        ]);

        $org = Org::whereSid($params['org_sid'])
            ->first();

        if (!$org) {
            throw new BadRequestHttpException('机构不存在。');
        }

        $student = Org\Student::query()
            ->where('org_id', $org->id)
            ->where('id_card_number', $params['id_number'])
            ->first();

        if (!$student) {
            throw new BadRequestHttpException('您未在该机构报名或账号不存在。');
        }

        $user = User::find($student->user_id);

        return LoginService::login($user, $request->getClientIp(), $params['platform'], $params['system']);
    }

    /**
     * 扫码登录-获取二维码
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1973c6930d002
     */
    public function wechatQrcode()
    {
        $scene = WechatService::makeScene('qrlogin', [
            'status' => 'pending',
            'desc' => '待扫码..'
        ]);

        $result = WechatService::getWxaCodeUnlimit($scene, 'pages/login/authorize');

        return [
            'scene' => $scene,
            'type' => $result['type'],
            'image' => base64_encode($result['image'])
        ];
    }

    /**
     * 扫码登录-获取二维码状态
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1d550f430d008
     */
    public function wechatQrcodeCheck(Request $request, string $scene)
    {
        $params = $this->validate($request, [
            'platform' => 'required|string',
            'system' => 'required|string'
        ]);

        $session = WechatService::getScene($scene);

        if (!$session) {
            throw new BadRequestHttpException('请求错误或扫码超时，请重试。');
        }

        $data = $session['data'];

        switch ($session['data']['status']) {
            case 'pending':
                return [
                    'status' => $data['status'],
                    'desc' => $data['desc']
                ];
            case 'bind':
                return [
                    'status' => $data['status'],
                    'desc' => $data['desc'],
                    'bind_code' => $data['bind_code']
                ];

            case 'success':
                $user = User::find($data['user_id']);
                $login = LoginService::login($user, $request->getClientIp(), $params['platform'], $params['system']);
                return [
                    'status' => $data['status'],
                    'desc' => $data['desc'],
                    'login' => $login
                ];
        }
    }

    /**
     * 扫码登录-小程序授权
     *
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1d5b576b0d00e
     */
    public function wechatQrcodeAuthorize(Request $request, string $scene)
    {
        $params = $this->validate($request, [
            'code' => 'required|string',
            'platform' => 'required|string',
            'system' => 'required|string',
            'referral' => 'string'
        ]);

        try {
            $data = EasyWechatFactory::mp()
                ->getUtils()
                ->codeToSession($params['code']);
        } catch (\Exception $e) {
            Log::warning("微信小程序授权登录失败：{$e->getMessage()}");
            throw new BadRequestHttpException('登录失败，请稍候再试。');
        }

        $openId = $data['openid'];
        $unionId = $data['unionid'] ?? null;

        //$openId = 'asjd0fh89023a43eg';
        //$unionId = 'ang0bh90awtged';

        $session = WechatService::getScene($scene);

        if (!$session) {
            throw new BadRequestHttpException('请求错误或扫码超时，请重试。');
        }

        return $this->wrapBindLogin(function() use ($openId, $unionId, $request, $params, $scene) {
            try {
                $result = LoginService::loginByBind(UserBind::PLATFORM_WECHAT_MP, $openId, $request->getClientIp(), $params['platform'], $params['system'], $unionId, $params['referral'] ?? null);

                WechatService::updateScene($scene, [
                    'status' => 'success',
                    'desc' => '授权成功',
                    'user_id' => $result['user']->id
                ]);
            } catch (UnboundUserException $e) {
                WechatService::updateScene($scene, [
                    'status' => 'bind',
                    'desc' => '您需要绑定账号才能登录',
                    'bind_code' => $e->bindCode
                ]);
            }

            return $result;
        });
    }

    /**
     * 退出登录
     *
     * @param Request $request
     * @return string[]
     */
    public function logout(Request $request): array
    {
        $token = $request->bearerToken();

        /** @var User $user */
        $user = auth()->user();
        $user->tokens()->where('token', hash('sha256', $token))->delete();

        return ['message' => 'success'];
    }

    /**
     * 处理第三方授权登录的回调时的未绑定异常，在未绑定用户时统一输出
     *
     * @param \Closure $callback
     * @return array
     * @throws HttpException
     */
    private function wrapBindLogin($callback)
    {
        try {
            return $callback();
        } catch (UnboundUserException $e) {
            throw new NotFoundHttpException('您需要绑定账号才能登录。', headers: [
                'X-Bind-Code' => $e->bindCode
            ]);
        } catch (ServiceException $e) {
            if ($e->getCode() == 403) {
                throw new AccessDeniedHttpException($e->getMessage());
            } else {
                throw new BadRequestHttpException($e->getCode());
            }
        }
    }

}
