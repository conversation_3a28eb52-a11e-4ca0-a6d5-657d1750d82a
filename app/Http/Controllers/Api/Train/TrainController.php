<?php

namespace App\Http\Controllers\Api\Train;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Org;
use App\Models\Train\Chapter;
use App\Models\Train\Subject;
use App\Models\Train\SubjectOption;
use App\Models\Train\Test;
use App\Models\Train\TestSubject;
use App\Models\Train\Topic;
use App\Models\User\UserFavorite;
use App\Models\User\UserOwnTopic;
use App\Models\User\UserProperty;
use App\Services\Common\OrderService;
use App\Services\Topic\TopicService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TrainController extends Controller
{


    /**
     * 做题首页
     * @return []
     */
    public function pages()
    {

    }

    /**
     * 题库列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=a28de083-7f0d-40e1-815c-2137948e6301
     * @return void
     */
    public function topicLibrariesIndex()
    {
        $list = Topic::query()->select(['id', 'name'])->orderByDesc('sort')->get();
        $list->append('sid');
        return $list;
    }


    /**
     * 章节
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public function chapterList($topicId)
    {
        $uid = Auth::id();
        $topicId = Topic::decodeSid($topicId);
        $list = Chapter::query()
            ->withCount("subjects")
            ->with("test", function ($query) use ($uid) {
                $query->where('user_id', $uid);
            })
            ->where('topic_id', $topicId)->get();
        $list->append('sid');

        return $list;
    }

    /**
     * 选择题库
     * @return void
     */
    public function chooseTopic($topicId)
    {
        $topic = Topic::whereSid($topicId)->first();

        if (!$topic) {
            throw new NotFoundHttpException('题库不存在');
        }
        $uid = auth()->id();
        if (TopicService::isBuy($uid, $topic->id)) {
            $userProperty = UserProperty::get($uid, ['current_topic_id']);
            $userProperty->current_topic_id = $topic->id;
            $userProperty->save();
        }
        return [];
    }

    /**
     * 题库详情
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=46267c46-71d3-441c-b88a-78d001f021c2
     * @return array
     */
    public function topicShow($topicId, Request $request)
    {
        $params = $request->validate([
            'org_sid' => 'string'
        ]);

        $orgId = isset($params['org_sid']) ? Org::decodeSid($params['org_sid']) : null;

        $uid = \Auth::id();
        $topicId = Topic::decodeSid($topicId);

        if ($orgId) {
            $topicExists = Org\Topic::query()->where('org_id', $orgId)->where('topic_id', $topicId)->exists();
            if (!$topicExists) {
                $courseIds = ContentCourse::query()->where('topic_id', $topicId)->pluck('content_id')->toArray();
                if (empty($courseIds)) {
                    throw new NotFoundHttpException('该机构下该题库不存在');
                }
                $courseExists = Org\Course::query()->where('org_id', $orgId)->whereIn('course_id', $courseIds)->exists();
                if (!$courseExists) {
                    throw new NotFoundHttpException('该机构下该题库不存在');
                }
            }
        }

        $isPermanent = UserOwnTopic::query()
            ->where('user_id', $uid)
            ->where('topic_id', $topicId)
            ->where('expired_at', null)
            ->exists();

        $userTopic = UserOwnTopic::query()
            ->where('user_id', $uid)
            ->where('topic_id', $topicId)
            ->orderByDesc('expired_at')
            ->where('expired_at', '>', now())
            ->first();
        $expiredAt = '';
        $isPurchased = false;
        if ($userTopic) {
            $isPurchased = true;
            $expiredAt = $userTopic->expired_at;
        }
        $exam = TopicService::examConfig($topicId, $orgId);
        $topic = Topic::query()->find($topicId);
        $topic->topic_count = $exam->topic_count;
        $topic->limit_time = $exam->limit_time;
        $topic->total_score = $exam->total_score;
        $topic->exam_config = $exam->exam_config;
        $topic->pass_score = $exam->pass_score;
        $single = Subject::query()->where('topic_id', $topicId)->where('type', Subject::TYPE_SC)->count();
        $multi = Subject::query()->where('topic_id', $topicId)->where('type', Subject::TYPE_MC)->count();
        $judge = Subject::query()->where('topic_id', $topicId)->where('type', Subject::TYPE_TF)->count();
        if ($topic->course_content_id) {
            $courseContentId = Content::encodeId($topic->course_content_id);
        }

        if ($topic->course_category_id) {
            $courseCategoryId = Content::encodeId($topic->course_category_id);
        }

        $topicCount = Subject::query()->where('topic_id', $topic->id)->count();

        $topicRecordCount = TestSubject::query()
            ->where('user_id', $uid)
            ->where('topic_id', $topic->id)->groupBy('topic_id')->count();

        $topicRecordErrCount = TestSubject::query()->where('topic_id', $topic->id)
            ->where('option_id', '<>', 0)
            ->where('correct', 2)
            ->where('user_id', $uid)
            ->groupBy('topic_id')->count();

        $favoriteIds = UserFavorite::query()->where('user_id', $uid)->where('business_type', BusinessType::Subject)->get('business_id')->pluck('business_id');
        $favoriteCount = Subject::query()->where('topic_id', $topic->id)->whereIn('id', $favoriteIds)->count();
        $chapterCount = Chapter::query()->where('topic_id', $topicId)->count();
        return [
            'topic_id' => Topic::encodeId($topicId),
            'name' => $topic->name,
            'topic' => $topic,
            'is_purchased' => $isPurchased,
            'is_permanent' => $isPermanent,
            'isBuy' => TopicService::isBuy($uid, $topicId),
            'expired_at' => $expiredAt,
            'judge' => $judge,
            'multi' => $multi,
            'single' => $single,
            'topic_count' => $topicCount,
            'topic_record_err_count' => $topicRecordErrCount,
            'topic_record_count' => $topicRecordCount,
            'topic_favorite_count' => $favoriteCount,
            'chapter_count' => $chapterCount,
            'course_content_id' => $courseContentId ?? 0,
            'course_category_id' => $courseCategoryId ?? 0,

        ];
    }


    /**
     * 题目搜索
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=6f9624ca-43b5-483c-b120-3ab92841ad99
     * @return void
     */
    public function searchIndex($topicId)
    {
        $params = request()->validate([
            'intro' => ['required', 'string'],
        ]);
        $topic = Topic::whereSid($topicId)->first();

        if (!$topic) {
            throw new NotFoundHttpException('题库不存在');
        }

        $topicId = Topic::decodeSid($topicId);
        $uid = \Auth::id();
        if (!TopicService::isBuy($uid, $topicId)) {
            throw new NotFoundHttpException("该课程未购买");
        }

        $page = Subject::query()->where('topic_id', $topic->id)
            ->with('option')
            ->orderByDesc('id')
            ->where('intro', 'like', '%' . $params['intro'] . '%')
            ->cursorPaginate();

        foreach ($page as $k => &$v) {
            $v->append('sid');
            $v->option->append('sid');
            foreach ($v->option as $k1 => &$v1) {
                $v1->makeHidden(['subject_id']);
                $v1->subject_sid = SubjectOption::encodeId($v1->subject_id);
            }
            $v->makeHidden(['topic_id']);

        }
        $page = $this->cursorPaginateToArray($page);
        return $page;
    }

    /**
     * 题库订单生成
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=c6e3a77d-49b2-4517-8c34-007d67ee4796
     * @return Model
     */
    public function orderStore($topicId)
    {
        $params = request()->validate([
            'amount_index' => ['required', 'string'],
            'org_sid' => 'string'
        ]);

        if (!isset(Topic::amount()[$params['amount_index']]) && empty($params['org_sid'])) {
            throw new NotFoundHttpException('日期不存在');
        }

        if (!empty($params['org_sid'])) {
            $setAmount = [];
        } else {
            $setAmount = Topic::amount()[$params['amount_index']];
        }

        $uid = \Auth::id();
        $topic = Topic::whereSid($topicId)->first();

        if (!$topic) {
            throw new NotFoundHttpException('题库不存在');
        }

        $extend = [];

        if (!empty($params['org_sid'])) {
            $org = Org::whereSid($params['org_sid'])->first();

            if (!$org) {
                throw new NotFoundHttpException('机构不存在');
            }

            $orgTopic = Org\Topic::query()->where('org_id', $org->id)->where('topic_id', $topic->id)->first();

            if (!$orgTopic) {
                throw new NotFoundHttpException('机构题库不存在');
            }

            $extend['org_id'] = $org->id;
            $extend['valid_days'] = $params['amount_index'] == 'month' ? 30 : 60;
            $setAmount['amount'] = $extend['valid_days'] == 30 ? $orgTopic->price_sell_30 : $orgTopic->price_sell_60;
        } else {
            $extend['valid_days'] = $setAmount['bays'];
        }

        //$isPurchased = UserOwnTopic::query()->where('user_id', $uid)->where('topic_id', $topicId)->exists();
        $order = OrderService::create($uid, $topic, $setAmount['amount'], $extend);

        $order->makeHidden(['user_id', 'business_id', 'payment_id']);
        return $order;
    }


}
