<?php

namespace App\Http\Controllers\Api\Train;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Content;
use App\Models\Org;
use App\Models\Org\Enrollment;
use App\Models\Org\OrgClass;
use App\Models\Train\Chapter;
use App\Models\Train\Subject;
use App\Models\Train\SubjectOption;
use App\Models\Train\Test;
use App\Models\Train\TestSubject;
use App\Models\Train\Topic;
use App\Services\Org\OrgClassService;
use App\Services\Topic\TopicService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TrainTestController extends Controller
{
    /**
     * 获取考试测试id
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=720c02eb-1c18-4345-a27c-cd56b68f06cd
     * @return void
     */
    public function test($topicId)
    {
        $params = request()->validate([
            'type' => ['string'],
            'chapter_id' => ['string'],
        ]);
        $chapterSid = null;
        if (isset($params['chapter_id'])) {
            $chapterSid = Chapter::decodeSid($params['chapter_id']);
        }
        $uid = \Auth::id();
        $topicId = Topic::decodeSid($topicId);

        if (!TopicService::isBuy($uid, $topicId)) {
           // throw new PreconditionFailedHttpException("该课程未购买");
        }
        $test = Test::query()
            ->orderByDesc('id')
            ->where('user_id', $uid)
            ->where('topic_id', $topicId)
            ->where('status', Test::STATUS_TO)
            ->where('type', $params['type'])
            ->where('type', '<>', Test::TYPE_EMU);
        if ($chapterSid)  {
            $test->where("chapter_id", $chapterSid);
        }
        $test = $test->first();
        if (!$test){
            throw new NotFoundHttpException('考试不存在');
        }
        if ($test->type == Test::TYPE_EXAM) {
            throw new NotFoundHttpException('考试不存在');
        }
        $test->append('sid');
        if ($test && $params['type'] == Test::TYPE_EMU) {
            $end_at = Carbon::parse($test->end_at);
            if (now()->gt($end_at)) { //如果考试大于当前时间表示考试已经结束
                $test = "";
            }
        }
        $test->makeHidden(['user_id', 'topic_id', 'current_subject_id']);

        return ['test' => $test];
    }

    /**
     * 创建考试或者练习
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=e5c8ea8a-c913-4b76-b51b-33f209125e2d
     * @return void
     */
    public function createTest($topicId)
    {
        $params = request()->validate([
            'type' => ['required'],
            'chapter_id' => ['string'],
            'org_sid' => ['string'],
            'enroll_id' => ['string'],
        ]);
        $orgId = 0;
        $enrollId = 0;

        if (isset($params['enroll_id'])) {
            $enrollId = Enrollment::decodeSid($params['enroll_id']);
        }
        if (isset($params['org_sid'])) {
            $orgId = Org::whereSid($params['org_sid'])->publicFields()->value('id');
        }

        $chapterSid = 0;
        if (isset($params['chapter_id'])) {
            $chapterSid = Chapter::decodeSid($params['chapter_id']);
        }
        $user = \Auth::user();
        $uid = $user->id;
        $topicId = Topic::decodeSid($topicId);

        if (!TopicService::isBuy($uid, $topicId)) {
            //throw new NotFoundHttpException("该课程未购买");
        }
        $test = new Test();

        $end_at = $params['type'] == Test::TYPE_ERR ? now()->addHours(2) : null;

        $subject_count = Subject::query()->where('topic_id', $topicId)->count();

        $test->user_id = $uid;
        $test->topic_id = $topicId;
        $test->type = $params['type'];
        $test->end_at = $end_at;
        $test->score = 0;
        $test->chapter_id = $chapterSid;
        $test->subject_count = $params['type'] == Test::TYPE_EMU ? 100 : $subject_count;
        $exam = "";
        if ($test->type == Test::TYPE_EXAM) {
            $exam =  TopicService::getExam($user, $orgId, $topicId, $enrollId);
            if (!$exam) {
                throw new NotFoundHttpException('暂时没有权限进行考试');
            }
        }
        $test->enroll_id = $enrollId;
        if ($orgId) {
            $test->org_id = $orgId;
        }
        $test->save();

        if ($test->type == Test::TYPE_EXAM && $exam) {

            $enrollment = Enrollment::query()->where('id', $enrollId)->first();

            if ($enrollment->exam_retake == 1) {
                $enrollment->exam_retake = 2;
            }

            $enrollment->exam_count += 1;
            $enrollment->exam_latest_id = $test->id;
            $enrollment->save();
        }
        //判断是否考试
        if ($test->type == Test::TYPE_EMU || $test->type == Test::TYPE_EXAM) {
            TopicService::createExam($uid, $topicId, $test);

            $test->subject_count = TestSubject::query()->where('test_id', $test->id)->count();
            $test->save();
        }
        $test->append('sid');
        $test->makeHidden(['user_id', 'topic_id', 'current_subject_id']);

        return $test;
    }


    /**
     * 测试试卷跟新
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=349f5a69-66f2-4d2f-a7ee-cc50c679a57f
     * @return void
     */
    public function testPut($testId)
    {
        $params = request()->validate([
            'test_subject_id' => ['required'],
        ]);
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);
        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }

        $subject = Subject::whereSid($params['test_subject_id'])->first();
        if (!$subject) {
            throw new NotFoundHttpException("题目不存在不存在");
        }

        $test->current_subject_id = $subject->id;
        $test->save();

        return [];
    }

    /**
     * 考试详情
     * @param $testId
     * @return array
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=78da4caf-1e3b-4d30-b878-6540fac0697e
     */
    public function subjectTextShow($testId)
    {
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);
        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }
        $noProdsDone = 0;
        $topicList = [];
        if ($test->current_subject_id) {

            if ($test->type == Test::TYPE_EMU || $test->type == Test::TYPE_EXAM) {

                $testSubjectId = TestSubject::query()
                    ->where('test_id', $testId)
                    ->where('subject_id', $test->current_subject_id)
                    ->value('id');

                $TestSubjectCount = TestSubject::query()
                    ->where('test_id', $testId)
                    ->orderBy('id')
                    ->where('id', '<=', $testSubjectId)
                    ->count();
                $noProdsDone = $TestSubjectCount;

            } else {
                $noProdsDone = Subject::query()
                    ->where('topic_id', $test->topic_id)
                    ->where('id', '<=', $test->current_subject_id);

                if ($test->chapter_id) {
                    $noProdsDone->where('chapter_id', $test->chapter_id);
                }
                if ($test->type == Test::TYPE_SINGLE || $test->type == Test::TYPE_MULTI || $test->type ==
                    Test::TYPE_JUDGE) {
                    $type =match($test->type) {
                        Test::TYPE_SINGLE => Subject::TYPE_SC,
                        Test::TYPE_MULTI => Subject::TYPE_MC,
                        Test::TYPE_JUDGE => Subject::TYPE_TF,
                        default => Subject::TYPE_SC
                    };
                    $noProdsDone->where('type', $type);
                } else if ($test->type == Test::TYPE_COLL) { //收藏练习
                    $noProdsDone->whereIn('id', function ($query) use ($test, $uid) {
                        $query->from('user_favorites')
                            ->where('user_id', $uid)
                            ->where('business_type', BusinessType::Subject)
                            ->select('business_id');
                    });
                } else if ($test->type == Test::TYPE_ERR) { //错误练习
                    $noProdsDone->whereIn('id', function ($query) use ($test, $uid) {
                        $query->from('train_test_subjects')
                            ->where('topic_id', $test->topic_id)
                            ->where('option_id', '<>', 0)
                            ->where('user_id', $uid)
                            ->where('correct', Test::CORRECT_ERR)
                            ->where('wrong_removed', Test::WRONG_REMOVED_NOT)
                            ->select('subject_id');
                    });
                }
                $noProdsDone = $noProdsDone->count(); //已经做过的题目数量
                $perPage = 100;
                $currentPage = ceil($noProdsDone  / $perPage);

                $noProdsDone = ($noProdsDone - 1) % $perPage;
            }
        }
        if ($test->type == Test::TYPE_ERR) {
            $wrongTopic = Subject::query()->whereIn('id', function ($query) use ($test) {
                $query->from('train_test_subjects')
                    ->where('option_id', '<>', 0)
                    ->where('correct', Test::CORRECT_ERR)
                    ->where('wrong_removed', Test::WRONG_REMOVED_NOT)
                    ->select('subject_id');
            })->groupBy('topic_id')->get(['topic_id'])->pluck('topic_id');
            $topicList = Topic::query()->whereIn('id', $wrongTopic)->get(['id', 'name']);
        }
        $test->topic_sid = Subject::encodeId($test->topic_id);
        $test->makeHidden(['user_id', 'topic_id','current_subject_id']);

        $test->append('sid');
        if ($test->chapter_id) {
            $test->chapter_sid = Chapter::encodeId($test->chapter_id);
        }
        if ($test->typy == Test::TYPE_EXAM) {
            $enrollment = Enrollment::query()->where('exam_latest_id', $test->id)->first();;
            if ($enrollment) {
                $user = Auth::user();
                if ($enrollment) {
                  $exam = OrgClassService::currentExam($user, $enrollment, $this->platform ?? 'miniProgram');
                  if (!$exam['can_exam']) {
                      throw new ServiceException($exam['reason']);
                  }
                }
            }
        }
        $exam = TopicService::examConfig($test->topic_id, $test->org_id);
        $countdownSeconds = $exam->limit_time;
        return [
            'answers_total' => $test->subject_completed_count ?? 1,
            'no_prods_done' => $noProdsDone ?? 0,
            'current_id' => $test->current_subject_id ? Subject::encodeId($test->current_subject_id) : 0,
            'current_page' => $currentPage ?? 1,
            'start_time' => Carbon::parse($test->created_at)->getTimestampMs(),
            'right' => $test->subject_correct_count,
            'error' => $test->subject_completed_count - $test->subject_correct_count,
            'test' => $test,
            'exam' => $exam,
            'topic_list' => $topicList,
            'topicSid' => Topic::encodeId($test->topic_id),
            'countdown_seconds' => $countdownSeconds * 60 * 1000
        ];
    }

    /**
     * 获取练习/考试的列表
     * @param $testId
     * @return array
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=2a92ed0c-193d-433b-b973-a936f9dc6110
     */
    public function subjectIndex($testId)
    {

        $params = $this->validate(request(), [
            'per_page' => ['integer'],
        ]);
        $params['per_page'] = $params['per_page'] ?? 100;
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);

        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }
        $headTail = [];
        if ($test->type == Test::TYPE_EMU || $test->type == Test::TYPE_EXAM) {
            $topicList = Subject::query()
                ->with('option')
                ->with('favorite')
                ->with('example')
                ->with('testOption', function ($query) use ($testId) {
                    $query->where('test_id', $testId)->select(['test_id', 'subject_id', 'option_id', 'topic_id', 'id', 'correct', 'answer']);
                })->whereIn('id', function ($query) use ($testId) {
                    $query->from('train_test_subjects')->where('test_id', $testId)->select('subject_id');
                })->paginate($params['per_page']);
        } else {

            $topicList = Subject::query()
                ->with('option')
                ->with('favorite')
                ->with('example')
                ->orderBy('id')
                ->with('testOption', function ($query) use ($testId) {
                    $query->where('test_id', $testId)->select(['test_id', 'subject_id', 'option_id', 'topic_id', 'id', 'correct', 'answer']);
                })->where('topic_id', $test->topic_id);
            if ($test->chapter_id) {
                $topicList->where('chapter_id', $test->chapter_id);
            }
            if ($test->type == Test::TYPE_SINGLE || $test->type == Test::TYPE_MULTI || $test->type ==
                Test::TYPE_JUDGE) {

                $type =match($test->type) {
                    Test::TYPE_SINGLE => Subject::TYPE_SC,
                    Test::TYPE_MULTI => Subject::TYPE_MC,
                    Test::TYPE_JUDGE => Subject::TYPE_TF,
                    default => Subject::TYPE_SC
                };
                $topicList->where('type', $type);
            } else if ($test->type == Test::TYPE_CHAPTER) {
                $topicList->where("chapter_id", $test->chapter_id);
            } else if ($test->type == Test::TYPE_COLL) { //收藏练习
                $topicList->whereIn('id', function ($query) use ($test, $uid) {
                    $query->from('user_favorites')
                        ->where('user_id', $uid)
                        ->where('business_type', BusinessType::Subject)
                        ->select('business_id');
                });
            } else if ($test->type == Test::TYPE_ERR) { //错误练习
                $topicList->whereIn('id', function ($query) use ($test, $uid) {
                    $query->from('train_test_subjects')->where('topic_id', $test->topic_id)
                        ->where('option_id', '<>', 0)
                        ->where('user_id', $uid)
                        ->where('correct', Test::CORRECT_ERR)
                        ->where('wrong_removed', Test::WRONG_REMOVED_NOT)
                        ->select('subject_id');
                });
            }
            $topicList = $topicList->paginate($params['per_page']);
            if ($topicList->count()){
                $headTail = TopicService::headTail($topicList[0]->id, $topicList->last()->id, $test->id, $test, $uid);
            }
        }


        $topicList->appends('sid');


        foreach ($topicList as $k => &$v){
            $v->append('sid');
            $v->option->append('sid');
            foreach ($v->option as $k1 => &$v1){
                $v1->makeHidden(['subject_id']);
                $v1->subject_sid = SubjectOption::encodeId($v1->subject_id);
            }
            $v->makeHidden(['topic_id']);

            if ($v->testOption){
                $v->testOption->append('sid');
                $v->testOption->makeHidden(['subject_id']);
                if ($v->testOption){
                    $v->testOption->test_id = TestSubject::encodeId($v->testOption->test_id);
                    if ($v->testOption->option_id && $v->type != Subject::TYPE_TF){
                        $optionId = explode(',', $v->testOption->option_id);
                        $sidArr = [];
                        foreach ($optionId as $item){
                            $sidArr[] = SubjectOption::encodeId($item);
                        }
                        $v->testOption->option_id = collect($sidArr)->join(",");
                    }
                }
            }
        }

        $topicList = $topicList->toArray();

        return [
            'topicList' => $topicList['data'],
            'head_tail' => $headTail,
            'page' => [
                'total' => $topicList['total'],
                'last_page' => $topicList['last_page'],
                'per_page' => $topicList['per_page'],
            ],
        ];
    }

    /**
     * 回答题目
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=49da9a57-9587-4619-a058-dddc3c1d6ff9
     * @return void
     */
    public function answerQuestion($testId)
    {
        $params = $this->validate(request(), [
            'subject_id' => ['required', 'string',],
            'correct' => ['required', 'integer',],
            'option_id' => ['required',],
            'answer' => ['string',],
        ]);
        $answer = $params['answer'] ?? '';
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);

        $params['subject_id'] = Subject::decodeSid($params['subject_id']);

        TopicService::processing($uid, $testId, $params['subject_id'], explode(',', $params['option_id']), $answer);

        return [];
    }

    /**
     * 删除错误记录
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=a39aafd8-bee5-4942-b616-54059de8f013
     * @return void
     */
    public function wrongErrTestDelete($testId,$recordId)
    {
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);
        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }

        $recordId = Subject::decodeSid($recordId);
        TestSubject::query()
            ->where('user_id', $uid)
            ->where('subject_id', $recordId)
            ->update(['wrong_removed' => TestSubject::WRONG_REMOVED]);
        return [];
    }

    /**
     * 完成测试
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=e0241586-6f72-4905-a0cf-8f1f4d6c81e1
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     */
    public function finishTest($testId)
    {
        $params = request()->validate([
            'testList' => ['string'],
        ]);
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);
        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }

        if ($test->type == Test::TYPE_EMU || $test->type == Test::TYPE_EXAM){
            $testList = json_decode($params['testList'], true);
            foreach ($testList as $v){
                if (!empty($v['multiple'])){
                    $answer = "";
                    $v['answer'] ?? $answer = $v['answer'];
                    TopicService::processing($uid, $testId, Subject::decodeSid($v['sid']), $v['multiple'], $answer);
                } elseif (isset($v['answer']) && $v['answer']) {
                    TopicService::processing($uid, $testId, Subject::decodeSid($v['sid']), [], $v['answer']);
                }
            }
        }
        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();

        $topic = TopicService::examConfig($test->topic_id, $test->org_id);
        $passScore = $topic->pass_score;
        if ($test->score >= $passScore) {
            $test->passed = Test::PASSED;
        }
        if ($test->type == Test::TYPE_EXAM) {
            /** @var Enrollment $enroll */
            $enroll = Enrollment::query()->where('exam_latest_id', $test->id)->first();
            if ($enroll){
                /** @var OrgClass $class */
                $class = OrgClass::query()->where('id', $enroll->class_id)->first();
                $maxScore = $enroll->exam_score;

                if ($class && $enroll->exam_taken == 0) { //班级考试人数加1
                    $class->total_examined += 1;
                }

                if ($test->score >= $passScore) {
                    $enroll->exam_passed = Test::PASSED;
                    $enroll->exam_pass_at = Carbon::now();  
                    
                    // 学时完成，考试通过，学员状态完成
                    if ($enroll->learn_finished) {
                        $enroll->status = Enrollment::STATUS_COMPLETED;
                    }
                }
                if ($class && $enroll->exam_score < $passScore && $test->score >= $passScore) { //班级考试通过人数加1
                    $class->total_passed += 1;
                }

                if ($class) {
                    $class->save();
                }

                if ($enroll->exam_retake == Enrollment::RETAKE_CAN) {
                    $enroll->exam_retake == Enrollment::RETAKE_ALR;
                }

                $enroll->exam_taken = 1;
                if ($test->score > $maxScore) {
                    $enroll->exam_score = $test->score;
                }
                $enroll->save();
            }
        }

        $test->status = Test::STATUS_STOP;
        $test->end_at = now();
        $test->save();

        return $test;
    }

    /**
     * 考试记录
     */
    public function examRecord($topicId)
    {
        $uid = \Auth::id();
        $topic = Topic::whereSid($topicId)->first();
        if (!$topic) {
            throw new NotFoundHttpException('题库不存在');
        }
        $page = Test::query()
            ->where('user_id', $uid)
            ->orderByDesc('id')
            ->where('type', Test::TYPE_EMU);
        $page = $page->cursorPaginate();

        foreach ($page as $k => &$v) {
            $v->append('sid');
            $v->makeHidden(['user_id', 'topic_id', 'current_subject_id']);
            if (\Str::contains($v->score, '.0')) {
                $v->score = (int)$v->score;
            }
        }
        $page = $this->cursorPaginateToArray($page);

        return $page;

    }

    /**
     * 考试记录
     */
    public function orgTests($org_sid)
    {
        $uid = \Auth::id();

        $orgId = Org::whereSid($org_sid)->publicFields()->value('id');
        if (!$orgId) {
            throw new NotFoundHttpException('机构不存在');
        }

        $page = Test::query()
            ->where('user_id', $uid)
            ->where('org_id', $orgId)
            ->where('status', Test::STATUS_STOP)
            ->orderByDesc('id')
            ->with('topic', function ($query) {
                $query->select('id', 'name');
            })
            ->where('type', Test::TYPE_EMU);
        $page = $page->get();

        foreach ($page as $k => &$v) {
            $v->append('sid');
            $v->makeHidden(['user_id', 'topic_id', 'current_subject_id', 'test_time', 'enroll_id']);
            if (\Str::contains($v->score, '.0')) {
                $v->score = (int)$v->score;
            }
            $v->testTime =  $v->test_time;
        }
        //$page = $this->cursorPaginateToArray($page);

        return $page->toArray();

    }
    /**
     * 考试结果
     * @return []
     */
    public function examResults($testId)
    {
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);
        $test = Test::query()->with('topic')->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }
        $testList = [];


            $testList = Subject::query()
                ->with('option')
                ->with('favorite')
                ->with('example')
                ->with('testOption', function ($query) use ($testId) {
                    $query->where('test_id', $testId)->select(['test_id', 'subject_id', 'option_id', 'topic_id', 'id', 'correct', 'answer']);
                })->whereIn('id', function ($query) use ($testId) {
                    $query->from('train_test_subjects')->where('test_id', $testId)->select('subject_id');
                })->get();

            foreach ($testList as $k => &$v){
                $v->append('sid');
                $v->option->append('sid');
                foreach ($v->option as $k1 => &$v1){
                    $v1->makeHidden(['subject_id']);
                    $v1->subject_sid = SubjectOption::encodeId($v1->subject_id);
                }
                $v->makeHidden(['topic_id']);

                if ($v->testOption){
                    $v->testOption->append('sid');
                    $v->testOption->makeHidden(['subject_id']);
                    if ($v->testOption){
                        $v->testOption->test_id = TestSubject::encodeId($v->testOption->test_id);
                        if ($v->testOption->option_id && $v->type != Subject::TYPE_TF){
                            $optionId = explode(',', $v->testOption->option_id);
                            $sidArr = [];
                            foreach ($optionId as $item){
                                $sidArr[] = SubjectOption::encodeId($item);
                            }
                            $v->testOption->option_id = collect($sidArr)->join(",");
                        }
                    }
                }
            }

        $subjectErrorCount = TestSubject::query()
            ->where('test_id', $testId)
            ->where('option_id', '<>', 0)
            ->where('correct', TestSubject::CORRECT_ERR)
            ->count();

        $createdAt = Carbon::parse($test->created_at);
        $endAt = Carbon::parse($test->end_at);
        $test->subject_error_count = $subjectErrorCount;
        $test->subject_completed_not_count = $test->subject_count - $test->subject_completed_count;
        $difference = $createdAt->diff($endAt);
        $differenceText = $difference->h . "小时" . $difference->i . "分" . $difference->s . "秒";
        $test->makeHidden(['user_id', 'topic_id']);
        $right = !$test->subject_correct_count ? 0 : bcmul($test->subject_correct_count /
            $test->subject_completed_count,

        100, 2);
        $topic = Topic::query()->where('id', $test->topic_id)->first();
        if ($topic->course_content_id){
            $courseContentId = Content::encodeId($topic->course_content_id);
        }

        if ($topic->course_category_id){
            $courseCategoryId = Content::encodeId($topic->course_category_id);
        }
        if (\Str::contains($test->score, '.0')) {
            $test->score = (int)$test->score;
        }
        return [
            'test' => $test,
            'right' => $right,
            'test_list' => $testList,
            'difference_text' => $differenceText,
            'course_content_id' => $courseContentId ?? 0,
            'course_category_id' => $courseCategoryId ?? 0,
        ];
    }

    /**
     * 删除测试
     * @param $testId
     * @return void
     */
    public function delTest($testId)
    {
        $uid = \Auth::id();
        $testId = Test::decodeSid($testId);
        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }
        $test->delete();

        TestSubject::query()->where('test_id', $test->id)->delete();
        return [];
    }

}
