<?php

namespace App\Http\Controllers\Api;

use App\Core\Enums\BusinessType;
use App\Models\Invitation;
use App\Models\User;
use App\Models\Visitor;
use App\Services\User\CreditService;
use App\Services\User\VisitorService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class InvitationController extends Controller
{
    /**
     * 邀请列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=6d62d7fb-14b5-4ab0-a2ad-17a9bfb1214a
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        $params = $this->validate($request, [
            'list_rows' => 'integer',
            'next_cursor' => 'string'
        ]);

        $listRows = $params['list_rows'] ?? 20;

        $invitations = Invitation::query()
            ->with([
                'referral' => function ($query) {
                    $query->publicFields();
                },
                'invitee' => function ($query) {
                    $query->publicFields();
                }
            ])
            ->where('referral_id', Auth::id())
            ->orderByDesc('id')
            ->cursorPaginate(perPage: $listRows, cursor: $params['next_cursor'] ?? null);

        return $this->cursorPaginateToArray($invitations);
    }

    /**
     * 分享奖励
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ServiceException
     */
    public function shareReward(Request $request): array
    {
        $params = $request->validate([
            'uuid' => 'required|string',
            'business_type' => 'required|string',
            'business_id' => 'required|string'
        ]);

        if (!$this->visitorToken) {
            Log::error("[share-reward] token异常");
            throw new BadRequestHttpException('token异常');
        }

        try {
            $business = BusinessType::from($params['business_type']);
        } catch (\Exception $e) {
            Log::error("[share-reward] ”{$params['business_type']}“数据错误");
            throw new NotFoundHttpException($params['business_type'] . "数据错误");
        }

        $query = $business->modelClass();
        $data = $query::whereSid($params['business_id'])->first();
        if (!$data) {
            Log::error("[share-reward] ”{$params['business_id']}“分享内容不存在");
            throw new NotFoundHttpException("分享内容不存在");
        }

        /** @var User $shareUser */
        $shareUser = User::query()
            ->where('uuid', $params['uuid'])
            ->where('status', User::STATUS_NORMAL)
            ->first();
        if (!$shareUser) {
            Log::error("[share-reward] ”{$params['uuid']}“分享用户不存在");
            throw new NotFoundHttpException("分享用户不存在");
        }

        $shareUserToken = Visitor::query()->where('user_id', $shareUser->id)->value('token');
        if (!$shareUserToken) {
            Log::error("[share-reward] ”{$shareUser->id}”分享用户token异常");
            throw new NotFoundHttpException("分享用户token异常");
        }

        // 是否是同一用户，同一用户则不发放奖励
        $relevant = VisitorService::getRelevant($this->visitorToken, Auth::id() ?? 0);

        if (in_array($shareUserToken, $relevant['tokens']) || in_array($shareUser->id, $relevant['userIds'])) {
            Log::error("[share-reward] token”{$this->visitorToken}”用户”{$shareUser->id}”相同用户不能获取奖励");
            throw new BadRequestHttpException("相同用户不能获取奖励");
        }

        $cacheKey = "reward:{$shareUser->id}";
        $cacheValue = Cache::get($cacheKey) ?? [];
        $config = config('heguibao.share');
        if (!empty($cacheValue)) {
            $rewardSum = 0;
            foreach ($cacheValue as $k => $reward) {
                // 是否当天已经领取过被分享用户的奖励，领取过则不再奖励
                if ($k == $this->visitorToken) {
                    Log::info("[share-reward] 用户”{$shareUser->id}”已领过分享token”{$this->visitorToken}”奖励");
                    throw new BadRequestHttpException("已领过该奖励");
                }
                $rewardSum = bcadd($rewardSum, $reward);
            }
            // 是否超过当天分享奖励，超过则不再奖励
            if ((int) bcadd($rewardSum, $config['reward_credit']) > (int) bcmul($config['limit_per_day'], $config['reward_credit'])) {
                Log::info("[share-reward] 用户”{$shareUser->id}”已超过每天最大奖励积分");
                throw new BadRequestHttpException("已超过每天最大奖励积分");
            }
        }

        // 赠送积分
        CreditService::recharge($shareUser->id, $config['reward_credit'], $business, $data->id, "分享奖励");

        // 设置缓存
        $cacheValue = array_merge($cacheValue, [$this->visitorToken => $config['reward_credit']]);
        $endOfDay = Carbon::now()->endOfDay();
        $ttl = Carbon::now()->diffInSeconds($endOfDay);
        Cache::put($cacheKey, $cacheValue, $ttl);

        return ['message' => 'success'];
    }
}
