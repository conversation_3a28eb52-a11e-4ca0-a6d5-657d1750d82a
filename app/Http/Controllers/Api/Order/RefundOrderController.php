<?php

namespace App\Http\Controllers\Api\Order;

use App\Http\Controllers\Api\Controller;
use App\Models\Order\Order;
use App\Models\Order\Payment;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class RefundOrderController extends Controller
{
    /**
     * 发起退款
     */
    public function store($orderNo)
    {
        $uid = \Auth::id();
        $payment = Payment::query()->where('out_trade_no', $orderNo)->where('user_id', $uid)->first();
        if (!$payment){
            throw new NotFoundHttpException("订单不存在");
        }

        if ($payment->status != Payment::STATUS_PAID){
            throw new NotFoundHttpException("订单未支付");
        }

        if ($payment->platform == Payment::PLATFORM_WECHAT){

        }


    }
}