<?php

namespace App\Http\Controllers\Api\Qa;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Api\Controller;
use App\Models\Qa\Answer;
use App\Models\Qa\Question;
use App\Models\User;
use App\Models\User\UserAttitude;
use App\Services\HuaWei\ModerationService;
use App\Services\User\CreditService;
use App\Services\User\TipService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AnswerController extends Controller
{

    /**
     *问题回答列表
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=d47aa899-e323-4da9-93ad-5defc2380446
     * @return void
     */
    public function index($questionId)
    {
        $questionId = Question::decodeSid($questionId);
        $uid = auth()->id();
        $answer = Answer::query()
            ->with('user', function ($query) {
                $query->select(['id', 'nickname', 'avatar']);
            })
            ->with('attitude', function ($query) use ($uid) {
                $query->where('user_id', $uid)->where('business_type', BusinessType::Answer);
            })
            ->where('status', Answer::STATUS_NORMAL)
            ->orderByDesc('id')
            ->where('question_id', $questionId);

        $page = $answer->cursorPaginate();
        foreach ($page as $k => &$v) {
            $v->append('sid');
            $v->makeHidden(['user_id', 'question_id']);

            $v->is_like = false;
            $v->dislike = false;

            if ($v->attitude) {
                $v->is_like = collect($v->attitude)->firstWhere('attitude', UserAttitude::ATTITUDE_LIKE) ? true : false;
                $v->dislike = collect($v->attitude)->firstWhere('attitude', UserAttitude::ATTITUDE_DISLIKE) ? true : false;
            }
        }
        $page = $this->cursorPaginateToArray($page);
        return $page;
    }

    /**
     * 创建问题回答
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=4bcbd51f-0273-4358-9eb1-297d1d35cb3f
     */
    public function store($questionId)
    {
        $params = request()->validate([
            'content' => 'required|string|max:1000',
            'anonymous' => 'bool',
        ]);

        $question = Question::whereSid($questionId)
            ->with('user', fn($query) => $query->select(['id', 'nickname']))
            ->first();

        if (!$question) {
            throw new NotFoundHttpException('问题不存在');
        }

        $user = Auth::user();

        if ($user->status == User::STATUS_MUTE) {
            throw new AccessDeniedHttpException('您已被禁言。');
        }

        $addArr['user_id'] = $user->id;
        $addArr['content'] = $params['content'];
        $addArr['question_id'] = $question->id;
        $addArr['anonymous'] = $params['anonymous'] ?? 0;

        $isSame = Answer::query()->where('user_id', $user->id)
            ->where('question_id', $question->id)
            ->where('content', $params['content'])
            ->where('created_at', '>=', now()->subMinutes(10))
            ->exists();

        if ($isSame) {
            throw new ConflictHttpException('内容重复，请10分钟后再试。');
        }

        // 请求华为云内容审核接口
        $res = app(ModerationService::class)->contentCheck($params['content']);
        if ($res['result']['suggestion'] != 'pass') {
            throw new BadRequestHttpException('您输入的内容中包含敏感词汇，请重新检查并修改，确保内容合规后再试');
        }

        DB::beginTransaction();

        try {
            $addArr['status'] = $res['result']['suggestion'] == 'pass' ? 1 : 2;
            $question->increment('answer_count');
            $question->reply_at = now();
            $question->save();
            $answer = Answer::create($addArr);
            if ($question->user_id != $user->id) {
                TipService::set($question->user_id, "tip-answer-me-page");
                TipService::set($question->user_id, "tip-answer-details-" . $question->id);
            }
            DB::commit();
            $answer->makeHidden(['question_id', 'user_id']);
            $answer->append('sid');
            $answer->user;
            $answer->is_like = false;
            $answer->dislike = false;
            return $answer;

        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error("创建问答回答出错，原因：" . $e->getMessage());
            throw new BadRequestHttpException($e->getMessage());
        }
    }

    /**
     * 删除回答
     * @return void
     */
    public function delete($sid)
    {
        $uid = auth()->id();
        $answer = Answer::whereSid($sid)->where('user_id', $uid)->first();
        if (!$answer) {
            throw new NotFoundHttpException('回答不存在');
        }
        Question::query()->where('id', $answer->question_id)->where('user_id', $uid)->decrement('answer_count');
        $answer->delete();

        return [];
    }

    /**
     * 我的回答
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=ab637e10-e323-4b44-b5ff-bff982c54401
     * @return array
     */
    public function myAnswers()
    {
        $uid = Auth::id();
        $page = Answer::query()
            ->where('status', Answer::STATUS_NORMAL)
            ->with('question', function ($query) {
                $query->select(['id', 'title', 'content']);
            })
            ->orderByDesc('created_at')
            ->orderByDesc('id')
            ->where('user_id', $uid)
            ->select(['id', 'question_id', 'content', 'like_count', 'dislike_count', 'created_at']);

        $page = $page->cursorPaginate(10);
        foreach ($page as $k => &$v) {
            $v->append('sid');
            $v->question->append('sid');

        }
        $page = $this->cursorPaginateToArray($page);
        return $page;
    }

}
