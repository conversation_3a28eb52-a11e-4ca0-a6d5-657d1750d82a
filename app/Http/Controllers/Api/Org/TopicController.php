<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Models\Org;
use App\Models\Org\Course;
use App\Models\Org\Topic;

class TopicController extends Controller
{
    /**
     * 机构题库列表
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=5dec2467930b4
     *
     * @param string $orgSid
     * @return array
     */
    public function index(string $orgSid): array
    {
        $orgId = Org::decodeSid($orgSid);

        $topics = Topic::publicFields()
            ->with('topic', fn ($query) => $query->publicFields())
            ->where('org_id', $orgId)
            ->where('status', Topic::STATUS_VISIBLE)
            ->latest('id')
            ->get()
            ?->setHidden(['id', 'org_id', 'topic_id']);

        $list = [];
        foreach ($topics as $topic) {
            $topic->topic->subjectCount();
            $list[] = [
                'sid' => $topic->topic->sid,
                'name' => $topic->topic->name,
                'subject_count' => $topic->topic->subject_count,
            ];
        }

        return $list;
    }
}
