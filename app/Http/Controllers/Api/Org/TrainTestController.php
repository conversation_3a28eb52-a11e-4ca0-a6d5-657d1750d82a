<?php

namespace App\Http\Controllers\Api\Org;

use App\Http\Controllers\Api\Controller;
use App\Models\Org;
use App\Models\Org\Enrollment;
use App\Models\Train\Test;
use Illuminate\Support\Facades\Auth;

class TrainTestController extends Controller
{
    /**
     * 考试记录
     *
     * @param string $orgSid 机构sid
     * @return array
     */
    public function index(string $orgSid): array
    {
        $orgId = Org::decodeSid($orgSid);
        $userId = Auth::id();

        $tests = Test::query()
            ->with('topic', fn ($query) => $query->publicFields())
            ->where('user_id', $userId)
            ->where('org_id', $orgId)
            ->where('enroll_id', '>', 0)
            ->where('type', Test::TYPE_EXAM)
            ->where('status', Test::STATUS_STOP)
            ->latest('id')
            ->get()?->makeHidden(['org_id', 'enroll_id']);

        if ($tests->isEmpty()) {
            return [];
        }

        return $tests->toArray();
    }
}
