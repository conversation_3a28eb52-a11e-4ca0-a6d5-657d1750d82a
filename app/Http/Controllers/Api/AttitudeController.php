<?php

namespace App\Http\Controllers\Api;

use App\Core\AttitudeInterface;
use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\User\UserAttitude;
use App\Observers\UserAttitudeObserver;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AttitudeController extends Controller
{

    /**
     * 添加表态
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=dc9157bf-2350-49bb-95fc-78a83934c2b6
     * @return void
     */
    public function store($businessType, $businessId)
    {
        $params = request()->validate([
            'attitude' => ['required', 'in:1,2']
        ]);

        try{
            $business = BusinessType::from($businessType);
        } catch (\ValueError $e) {
            throw new ServiceException($businessType."数据错误");
        }
        $query = $business->modelClass();
        $businessId = $query::decodeSid($businessId);

        $uid = \Auth::id();
        $data = $query::query()->where('id', $businessId)->first();
        if (!$data){
            throw new NotFoundHttpException("内容不存在");
        }

        $attitudeQuery= UserAttitude::query()
            ->where('business_type', $businessType)
            ->where('business_id', $data->id)
            ->where('user_id', $uid);

        $attitudeList = $attitudeQuery->get();

        $attitude = UserAttitude::query()
            ->where('business_type', $businessType)
            ->where('business_id', $data->id)
            ->where('user_id', $uid)
            ->where('attitude', $params['attitude'])
            ->first();
        if (!$attitude){
            $attitude = UserAttitude::query()->create([
                'business_type' => $businessType,
                'business_id'=> $data->id,
                'user_id' => $uid,
                'attitude' => $params['attitude']
            ]);
        }

        if ($attitude->resource && $attitude->resource instanceof AttitudeInterface) {
            [$likeField, $dislikeField] = app($query)->getAttitudeKeys();

            if ($params['attitude'] == UserAttitude::ATTITUDE_LIKE){
                if ($attitudeList->firstWhere('attitude', UserAttitude::ATTITUDE_DISLIKE) ? true : false){
                    $attitudeQuery->where('attitude', UserAttitude::ATTITUDE_DISLIKE)->delete();
                    $attitude->resource->decrement($dislikeField);
                }
                $attitude->resource->increment($likeField);
            }else{
                if ($attitudeList->firstWhere('attitude', UserAttitude::ATTITUDE_LIKE) ? true : false){
                    $attitudeQuery->where('attitude', UserAttitude::ATTITUDE_LIKE)->delete();
                    $attitude->resource->decrement($likeField);
                }
                $attitude->resource->increment($dislikeField);
            }
        }
        return [];
    }

    /**
     * 删除表态
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=3ac723a7-ebc3-4d86-a206-7b0242ab040c
     * @param $businessType
     * @param $businessId
     * @return array
     * @throws ServiceException
     */
    public function destroy($businessType, $businessId)
    {
        $params = request()->validate([
            'attitude' => ['required', 'in:1,2']
        ]);

        try{
            $business = BusinessType::from($businessType);
        } catch (\ValueError $e) {
            throw new ServiceException($businessType."数据错误");
        }
        $query = $business->modelClass();
        $businessId = $query::decodeSid($businessId);

        $uid = \Auth::id();

        $attitude = UserAttitude::query()
            ->where('business_type', $businessType)
            ->where('business_id', $businessId)
            ->where('user_id', $uid)
            ->where('attitude', $params['attitude'])
            ->first();
        if (!$attitude){
            throw new NotFoundHttpException("没有找到该表态");
        }
        if ($attitude->resource && $attitude->resource instanceof AttitudeInterface) {
            [$likeField, $dislikeField] = app($query)->getAttitudeKeys();
            if ($params['attitude'] == UserAttitude::ATTITUDE_LIKE){
                $attitude->resource->decrement($likeField);
            }else{
                $attitude->resource->decrement($dislikeField);
            }
        }

        if ($attitude){
            $attitude->delete();
        }
        return [];
    }
}