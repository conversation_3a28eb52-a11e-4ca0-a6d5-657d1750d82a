<?php

namespace App\Http\Controllers\Api\Punish;

use App\Http\Controllers\Api\Controller;
use App\Libs\Wechat\EasyWechatFactory;
use App\Models\Punish\PunishSubject;
use App\Models\Punish\PunishSubjectOption;
use App\Models\Punish\PunishTest;
use App\Models\Punish\PunishTestSubjects;
use App\Models\Punish\PunishTopic;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PunishController extends Controller
{
    /**
     * 题库选择
     * @return array
     */
    public function topic()
    {
        return PunishTopic::query()->withCount('subjects')->get()->toArray();
    }

    /**
     * 测试
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|PunishSubject[]
     */
    public function subjectTestList($topicId)
    {

        $topic =  PunishTopic::query()->find($topicId);
        if (!$topic) {
           throw new NotFoundHttpException('题库不存在');
        }

        if ($topicId == 1) {
            $subjects = PunishSubject::query()->where('topic_id', $topicId)->with('options')->get();
        } else {
            $subjects = PunishSubject::query()->whereIn('topic_id', [$topicId, 1])->with('options')->get();
        }

       return $subjects;
    }


    /**
     * 提交答案
     * @return array
     */
    public function submitAnswer(PunishTest $punishTest)
    {
        $params = $this->validate(request(), [
            'topic_id' => 'integer',
            'amount_tol' => 'integer',
            'options' => 'string'
        ]);
        $params['options'] = json_decode($params['options'], true);
        $user = \Auth::user();
        $punishTest->user_id = $user->id;
        $punishTest->topic_id = $params['topic_id'];
        $punishTest->amount_tol = $params['amount_tol'];
        $punishTest->save();

        $data = [];
        $now = now();
        foreach ($params['options'] as $k => $v) {
            $test = [
                'user_id' => $user->id,
                'topic_id' => $params['topic_id'],
                'test_id' => $punishTest->id,
                'subject_id' => $v['subject_id'],
                'option_id' => $v['option_id'],
                'created_at' => $now,
                'updated_at' => $now,
            ];
            $data[] = $test;
        }

        PunishTestSubjects::query()->insert($data);
        return ['test_id' => $punishTest->id];
    }

    /**
     * 考试详情
     * @return array
     */
    public function showTest($testId)
    {

        $user = \Auth::user();
        $test = PunishTest::query()->where('user_id', $user->id)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException('考试不存在');
        }

        $testSubjects = PunishTestSubjects::query()->where('test_id', $testId)->with('option')->with('subject')->get();
        return $testSubjects->toArray();
    }


    public function shareIndex()
    {
        return ["qrCode" => EasyWechatFactory::shareCode()];
    }
}
