<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\ServiceException;
use App\Models\Order\Order;
use App\Models\User\UserBalanceRecord;
use App\Services\User\BalanceService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * 用户余额
 */
class BalanceController extends Controller
{

    /**
     * 余额支付
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1c09826876c1be
     */
    public function payment(Request $request)
    {
        $params = $this->validate($request, [
            'order_no' => 'string|required'
        ]);

        $userId = auth()->id();

        $order = Order::query()
            ->where('order_no', $params['order_no'])
            ->where('user_id', $userId)
            ->first();

        if (!$order) {
            throw new BadRequestHttpException('订单不存在或无法操作该订单。');
        }

        try {
            $record = BalanceService::payment($order, $userId);

            return [
                'order' => $order,
                'record' => $record
            ];
        } catch (ServiceException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
    }

    /**
     * 余额记录
     * @see https://doc.apipost.net/docs/detail/352f078be001000?target_id=1c0d71fe76c1f0
     */
    public function index()
    {
        $typeMap = [UserBalanceRecord::TYPE_RECHARGE, UserBalanceRecord::TYPE_CONSUME];

        $params = \request()->validate([
            'type' => 'string|in:' . implode(',', $typeMap)
        ]);

        $userId = auth()->id();
        $builder = UserBalanceRecord::query()->where('user_id', $userId);

        if (!empty($params['type'])) {
            $builder->where('type', $params['type']);
        }

        $builder->orderBy('created_at', 'desc');

        $page = $builder->cursorPaginate();

        foreach ($page as $k => $v){
            $v->action_label = $v->business_type->modelLabel();
        }

        return $this->cursorPaginateToArray($page);
    }

}
