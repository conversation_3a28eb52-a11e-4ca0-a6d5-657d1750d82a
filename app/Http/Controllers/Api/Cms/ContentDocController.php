<?php

namespace App\Http\Controllers\Api\Cms;

use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Content;
use App\Models\Cms\ContentDoc;
use App\Models\Org\Enrollment;
use App\Models\User\UserContentDownload;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class ContentDocController extends Controller
{
    /**
     * 下载资料
     *
     * @param string $contentId 资料ID
     * @return array
     */
    public function download(string $contentId, Request $request): array
    {
        $params = $request->validate([
            'enroll_sid' => 'string'
        ]);

        $enrollment = null;

        if (!empty($params['enroll_sid'])) {
            $enrollment = Enrollment::whereSid($params['enroll_sid'])->first();
        }

        /** @var Content $content */
        $content = Content::whereSid($contentId)
            //->with('resource', fn ($query) => $query->content($isBuy))
            ->where('status', Content::STATUS_NORMAL)
            ->when(config('heguibao.hidden_videos'), fn($query) => $query->whereIn('type', [Content::TYPE_DOC, Content::TYPE_RICH_TEXT]))
            ->publicFields()
            ->addSelect('admin_id')
            ->first();

        if (!$content) {
            throw new NotFoundHttpException("资料不存在");
        }

        $content->forUser(Auth::id(), $enrollment?->id ?? 0)->append('download');

        $content->load([
            'resource' => fn($query) => $query->detail($content->download)
        ]);

        // 用户下载记录
        $download = new UserContentDownload();
        $download->user_id = Auth::id();
        $download->content_id = $content->id;
        $download->created_at = Carbon::now();
        $download->save();

        // 资料下载统计
        ContentDoc::query()->where('content_id', $content->id)->update(['download_count' => DB::raw('download_count + 1')]);

        return ['url' => $content->resource->filepath_src];
    }
}
