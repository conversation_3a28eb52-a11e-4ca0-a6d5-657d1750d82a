<?php

namespace App\Http\Controllers\Api\Cms;

use App\Core\Enums\BusinessType;
use App\Http\Controllers\Api\Controller;
use App\Models\Cms\Content;
use App\Models\Cms\Special;
use App\Models\Cms\SpecialContent;
use App\Models\User;
use App\Models\User\UserContentDownload;
use App\Services\Cms\ContentService;
use App\Services\User\CreditService;
use App\Services\User\FavoriteService;
use App\Services\User\OwnContentService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class SpecialController extends Controller
{
    /**
     * 专题列表
     *
     * @return array
     */
    public function index(): array
    {
        $builder = Special::publicFields()->where('status', Special::STATUS_NORMAL);

        $page = $builder->orderByDesc('recommend_at')->cursorPaginate(20);
        foreach ($page as $v) {
            $contentIds = $v->contents->pluck('id')->toArray();
            $v->download_count = UserContentDownload::query()->whereIn('content_id', $contentIds)->count();
        }

        return $this->cursorPaginateToArray($page);
    }

    /**
     * 专题详情
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=129f9701-2414-4e8b-97fd-f25e6f8021d3
     *
     * @param string $specialId 专题ID
     * @return Special
     */
    public function show(string $specialId): Special
    {
        $special = Special::whereSid($specialId)->where('status', Special::STATUS_NORMAL)->first();
        if (!$special) {
            throw new NotFoundHttpException("专题不存在");
        }

        $contentIds = SpecialContent::query()
            ->where('special_id', $special->id)
            ->orderBy('order')
            ->pluck('content_id')
            ->toArray();

        $contents = ContentService::getContentListBuilder()->whereIn('id', $contentIds)->orderByRaw('FIELD(id, '.join(',', $contentIds).')')->get();

        $userId = Auth::id();
        $special->download = OwnContentService::checkPurchase($userId, collect($contents)->pluck('id')->toArray());
        $special->favorite = FavoriteService::checkFavorite($userId, $special);
        $special->old_charge_credit = collect($contents)->pluck('charge_credit')->sum();
        $special->content_count = $contents->count();
        $special->contents = $contents;

        return $special;
    }

    /**
     * 购买专题
     *
     * @param string $specialId
     * @return Special
     * @throws \Throwable
     */
    public function buy(string $specialId): Special
    {
        $special = Special::whereSid($specialId)->where('status', Special::STATUS_NORMAL)->first();
        if (!$special) {
            throw new NotFoundHttpException("专题不存在");
        }

        $contentIds = SpecialContent::query()
            ->where('special_id', $special->id)
            ->pluck('content_id')
            ->toArray();

        if (empty($contentIds)) {
            throw new BadRequestHttpException("专题内容不存在");
        }

        $userId = Auth::id();
        if (OwnContentService::checkPurchase($userId, $contentIds)) {
            throw new BadRequestHttpException("该章节下所有内容均已购买，无需重复购买");
        }

        if ($special->charge_credit <= 0) {
            throw new BadRequestHttpException("资料积分异常");
        }

        $special->favorite = FavoriteService::checkFavorite($userId, $special);

        try {
            DB::beginTransaction();

            $contents = ContentService::getContentListBuilder()->whereIn('id', $contentIds)->get();
            $special->old_charge_credit = collect($contents)->pluck('charge_credit')->sum();
            $special->content_count = $contents->count();

            // 扣除用户积分，增加用户积分变更日志
            CreditService::consume($userId, $special->charge_credit, BusinessType::Special, $special->id, "购买专题”{$special->name}”");

            // 增加用户资料
            /** @var Content $content */
            foreach ($contents as $content) {
                OwnContentService::create($userId, $content->id, $content->getClassify());
            }

            DB::commit();

            $special->download = true;

            $special->favorite = FavoriteService::checkFavorite($userId, $special);
            $special->content_count = $contents->count();
            $special->contents = $contents;

            return $special;
        } catch (\Exception $e) {
            DB::rollBack();
            if ($e->getCode() == 403) {
                throw new AccessDeniedHttpException(User::getUserCredit($userId));
            } else {
                Log::error("购买专题异常，原因：" . $e->getMessage());
                throw new BadRequestHttpException($e->getMessage());
            }
        }
    }
}
