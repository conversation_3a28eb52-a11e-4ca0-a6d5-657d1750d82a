<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * API 父控制器
 */
abstract class Controller extends \Illuminate\Routing\Controller
{

    use AuthorizesRequests, ValidatesRequests {validate as rawValidate;}

    /**
     * 用户/访客设备 Token
     */
    protected ?string $visitorToken;

    /** @var ?string 平台 */
    protected ?string $platform;

    public function __construct(Request $request)
    {
        /** @var User $auth */
        $auth = auth()->user();
        if ($auth) {
            $this->updateUserActive($request, $auth);
        }

        $this->visitorToken = $request->header('X-Visitor-Token');
        $this->platform = $request->header('X-Platform');
    }

    /**
     * Validate the given request with the given rules.
     *
     * @param Request $request
     * @param  array  $rules
     * @param  array  $messages
     * @param  array  $attributes
     * @return array
     *
     * @throws BadRequestHttpException
     */
    public function validate(Request $request, array $rules,
                             array $messages = [], array $attributes = [])
    {
        try {
            return self::rawValidate($request, $rules, $messages, $attributes);
        } catch (ValidationException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
    }

    protected function updateUserActive(Request $request, User $user)
    {
        // 5分钟，更新用户的活跃时间
        $now = Carbon::now();
        $diff = $now->diffInSeconds($user->last_active_at, false);

        if ($diff > -300) {
            return;
        }

        $user->last_active_at = $now;
        $user->last_active_ip = $request->getClientIp();
        $user->save();
    }

    protected function ok()
    {
        return new \stdClass();
    }

    /**
     * 游标分页
     * @return array
     */
    public function cursorPaginateToArray($paginate)
    {
        $paginate = $paginate->toArray();
        return [
            'data' => $paginate['data'],
            'next_cursor' => $paginate['next_cursor'],
        ];
    }

    protected function error($message = '', $data = null, $code = 400): JsonResponse
    {
        if (is_int($data)) {
            $code = $data;
            $data = [];
        }

        if ($code <= 400) {
            $code = 400;
        }

        return response()->json(['message' => $message, 'data' => $data, 'code' => $code], $code, [], JSON_UNESCAPED_UNICODE);
    }
}
