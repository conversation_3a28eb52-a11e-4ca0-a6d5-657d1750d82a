<?php

namespace App\Http\Controllers\Api;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Attachment\AttachmentFile;
use App\Models\User;
use App\Models\User\UserBalanceRecord;
use App\Models\User\UserBind;
use App\Services\Common\AttachmentService;
use App\Services\User\CreditService;
use App\Services\User\UserTaskService;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class UserController extends Controller
{

    /**
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=a5d76b6a-7156-4595-b7c6-eb75bd6b8a65
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function me()
    {
        $user = auth()->user();
        if ($user) {
            $user->append('balance_show');
        }

        return $user;
    }


    /**
     * 用户头像修改
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=842d8b55-c2c0-4beb-be91-9b2c0e78c4f9
     * @return array []
     * @throws ServiceException
     */
    public function updateAvatar()
    {
        $params = request()->validate([
            'key' => ['required', 'string'],
        ]);
        $user =  auth()->user();

        //移除旧头像
        $user->avatar && AttachmentService::removeRelations(BusinessType::User, $user->id, $user->avatar);

        $file = AttachmentService::store($params['key'], 'avatar', BusinessType::User, $user->id);

        $user->avatar = $file->path;
        $user->save();

        UserTaskService::editNicknameAvatar($user->id, avatar: $user->avatar);
        return [];
    }


    /**
     * 昵称修改
     * @see https://console-docs.apipost.cn/preview/39929bf695444a26/f256a02357be96fc?target_id=ac5cd46e-dfb6-4c70-84a6-ff620339b4a2
     * @return void
     */
    public function updateNickname()
    {
        $params = request()->validate([
            'nickname' => ['required', 'string'],
        ]);
        $user =  auth()->user();
        $user->nickname = $params['nickname'];
        $user->save();

        UserTaskService::editNicknameAvatar($user->id, nickname: $params['nickname']);
        return [];
    }

    /**
     * 获取openId
     *
     * @param Request $request
     * @return array
     */
    public function getOpenId(Request $request): array
    {
        $params = $request->validate([
            'platform' => 'required|string|in:wechat.mp'
        ]);

        $openId = UserBind::query()
            ->where('user_id', \Auth::id())
            ->where('platform', $params['platform'])
            ->value('open_id');

        if (!$openId) {
            throw new NotFoundHttpException('openId不存在');
        }

        return ['open_id' => $openId];
    }
}
