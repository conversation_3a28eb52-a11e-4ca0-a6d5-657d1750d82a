<?php

namespace App\Http\Controllers\Org;

use App\Services\Org\OrgOrderService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $params = $request->validate([
            'id' => 'integer',
            'name' => 'string',
            'phone' => 'string',
            'payment_channel' => 'string',
            'business_type' => 'string',
            'order_no' => 'string',
            'transaction_no' => 'string',
            'status' => 'string',
            'payment_at' => 'array',
            'created_at' => 'array',
        ]);

        // 转换时间戳参数
        $params = $this->convertTimestampParams($params);

        $builder = app(OrgOrderService::class)->getOrderByOrgID($this->auth()->org_id, $params);

        return $this->apiPaginate($request, $builder);
    }

    /**
     * 转换毫秒时间戳为标准时间格式
     */
    private function convertTimestampParams(array $params): array
    {
        $timeFields = ['payment_at', 'created_at'];

        foreach ($timeFields as $field) {
            if (isset($params[$field]) && is_array($params[$field]) && count($params[$field]) === 2) {
                $params[$field] = [
                    Carbon::createFromTimestamp($params[$field][0] / 1000)->toDateTimeString(),
                    Carbon::createFromTimestamp($params[$field][1] / 1000)->toDateTimeString(),
                ];
            }
        }

        return $params;
    }
}
