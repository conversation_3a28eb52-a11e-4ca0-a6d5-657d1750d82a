<?php

namespace App\Http\Controllers\Org;

use App\Models\Org\EnrollmentForm;
use App\Models\Org\Template;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\TemplateService;
use Illuminate\Http\Request;

class TemplateController extends Controller
{
    public function index(Request $request): array
    {
        $params = $request->validate([
            'type' => 'string',
        ]);

        $admin = $this->auth();

        $params['org_id'] = $admin->org_id;

        $builder = Template::query();
        $this->builderWhere($builder, $params, ['org_id', 'type']);
        $this->builderOrderBy($request, $builder, 'id asc');

        $data = $this->apiPaginate($request, $builder);

        $hourCert = true;
        $studentArchive = true;
        $hourRecord = true;
        $enrollmentForm = true;

        foreach ($data['records'] as $record) {
            if ($record->is_default) {
                if ($record->type == Template::TYPE_HOUR_CERT) {
                    $hourCert = false;
                } elseif ($record->type == Template::TYPE_STUDENT_ARCHIVE) {
                    $studentArchive = false;
                } elseif ($record->type == Template::TYPE_HOUR_RECORD) {
                    $hourRecord = false;
                } elseif ($record->type == Template::TYPE_ENROLLMENT_FORM) {
                    $enrollmentForm = false;
                }
            }
        }

        $defaultTemplates = config('org.export.default_templates');

        $defaultTemplates = [
            [
                'id' => 0,
                'org_id' => $admin->org_id,
                'name' => '学时证明（系统）',
                'type' => Template::TYPE_HOUR_CERT,
                'is_default' => $hourCert,
                'tpl_path' => $defaultTemplates[Template::TYPE_HOUR_CERT],
                'tpl_path_url' => url('/' . $defaultTemplates[Template::TYPE_HOUR_CERT]),
                'created_at' => '2025-03-19 11:26:11',
                'updated_at' => '2025-03-19 11:26:11',
            ],
            [
                'id' => 0,
                'org_id' => $admin->org_id,
                'name' => '一期一档（系统）',
                'type' => Template::TYPE_STUDENT_ARCHIVE,
                'is_default' => $studentArchive,
                'tpl_path' => $defaultTemplates[Template::TYPE_STUDENT_ARCHIVE],
                'tpl_path_url' => url('/' . $defaultTemplates[Template::TYPE_STUDENT_ARCHIVE]),
                'created_at' => '2025-03-19 11:26:11',
                'updated_at' => '2025-03-19 11:26:11',
            ],
            [
                'id' => 0,
                'org_id' => $admin->org_id,
                'name' => '学习记录（系统）',
                'type' => Template::TYPE_HOUR_RECORD,
                'is_default' => $hourRecord,
                'tpl_path' => $defaultTemplates[Template::TYPE_HOUR_RECORD],
                'tpl_path_url' => url('/' . $defaultTemplates[Template::TYPE_HOUR_RECORD]),
                'created_at' => '2025-03-19 11:26:11',
                'updated_at' => '2025-03-19 11:26:11',
            ],
            [
                'id' => 0,
                'org_id' => $admin->org_id,
                'name' => '报名表（系统）',
                'type' => Template::TYPE_ENROLLMENT_FORM,
                'is_default' => $enrollmentForm,
                'tpl_path' => $defaultTemplates[Template::TYPE_ENROLLMENT_FORM],
                'tpl_path_url' => url('/' . $defaultTemplates[Template::TYPE_ENROLLMENT_FORM]),
                'created_at' => '2025-03-19 11:26:11',
                'updated_at' => '2025-03-19 11:26:11',
            ]
        ];

        $data['records'] = array_merge($defaultTemplates, $data['records']->toArray());

        return $data;
    }

    public function store(Request $request): Template
    {
        $params = $request->validate([
            'type' => 'required|string|in:' . implode(',', [
                Template::TYPE_HOUR_CERT, Template::TYPE_STUDENT_ARCHIVE, Template::TYPE_HOUR_RECORD, Template::TYPE_ENROLLMENT_FORM
            ]),
            'name' => 'required|string',
            'is_default' => 'integer',
            'tpl_path' => 'required|string',
        ]);

        $admin = $this->auth();

        $template = TemplateService::create($admin->org_id, $params);

        OperateLogService::create($admin->id, '创建模版', ['id' => $template->id, 'org_id' => $admin->org_id, 'params' => $params]);

        return $template;
    }

    public function update(Request $request, int $id): Template
    {
        $params = $request->validate([
            'name' => 'string',
            'is_default' => 'integer',
            'tpl_path' => 'string',
        ]);

        $admin = $this->auth();

        $template = TemplateService::update($id, $admin->org_id, $params);

        OperateLogService::create($admin->id, '修改模版', ['id' => $template->id, 'org_id' => $admin->org_id, 'params' => $params]);

        return $template;
    }

    public function destroy(int $id): array
    {
        $admin = $this->auth();

        TemplateService::remove($id, $admin->org_id);

        OperateLogService::create($admin->id, '修改模版', ['id' => $id, 'org_id' => $admin->org_id]);

        return $this->success();
    }

    public function default(int $id): Template
    {
        $admin = $this->auth();

        $template = TemplateService::setDefault($id, $admin->org_id);

        OperateLogService::create($admin->id, '设置默认模版', ['id' => $id, 'org_id' => $admin->org_id]);

        return $template;
    }

    public function getFields(): array
    {
        $admin = $this->auth();

        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::query()->where('org_id', $admin->org_id)->first();

        $fields = [
            Template::TYPE_HOUR_CERT => [
                ['label' => '证书编号', 'field' => '${证书编号}', 'desc' => ''],
                ['label' => '姓名', 'field' => '${姓名}', 'desc' => ''],
                ['label' => '身份证号', 'field' => '${身份证号}', 'desc' => ''],
                ['label' => '证书编号', 'field' => '${证书编号}', 'desc' => ''],
                ['label' => '机构名称', 'field' => '${机构名称}', 'desc' => '培训机构'],
                ['label' => '开课时间', 'field' => '${开课时间}', 'desc' => '培训日期'],
                ['label' => '照片', 'field' => '${照片}', 'desc' => '个人证件照，指定尺寸：${照片:50:50}'],
                ['label' => '应修学时', 'field' => '${应修学时}', 'desc' => ''],
                ['label' => '实修学时', 'field' => '${实修学时}', 'desc' => ''],
                ['label' => '二维码', 'field' => '${二维码}', 'desc' => ''],
                ['label' => '创建时间', 'field' => '${创建时间}', 'desc' => '证书产生的时间'],
            ],
            Template::TYPE_STUDENT_ARCHIVE => [
                ['label' => '班级名称', 'field' => '${班级名称}', 'desc' => ''],
                ['label' => '参培人数', 'field' => '${参培人数}', 'desc' => ''],
                ['label' => '负责老师', 'field' => '${负责老师}', 'desc' => ''],
                ['label' => '培训时间', 'field' => '${培训时间}', 'desc' => ''],
                ['label' => '课时记录', 'field' => '', 'desc' => '列表'],
                ['label' => '课时记录.姓名', 'field' => '${课时记录.姓名}', 'desc' => ''],
                ['label' => '课时记录.手机号码', 'field' => '${课时记录.手机号码}', 'desc' => ''],
                ['label' => '课时记录.工作单位', 'field' => '${课时记录.工作单位}', 'desc' => ''],
                ['label' => '课时记录.课程', 'field' => '${课时记录.课程}', 'desc' => ''],
                ['label' => '课时记录.应修学时', 'field' => '${课时记录.应修学时}', 'desc' => ''],
                ['label' => '课时记录.已修学时', 'field' => '${课时记录.已修学时}', 'desc' => ''],
                ['label' => '课时记录.考试成绩', 'field' => '${课时记录.考试成绩}', 'desc' => ''],
                ['label' => '课时记录.是否及格', 'field' => '${课时记录.是否及格}', 'desc' => ''],
                ['label' => '课时记录.完成情况', 'field' => '${课时记录.完成情况}', 'desc' => ''],
                ['label' => '课时记录.学习记录', 'field' => '${课时记录.学习记录}', 'desc' => ''],
            ],
            Template::TYPE_HOUR_RECORD => [
                ['label' => '学号', 'field' => '${学号}', 'desc' => ''],
                ['label' => '姓名', 'field' => '${姓名}', 'desc' => ''],
                ['label' => '联系电话', 'field' => '${手机号}', 'desc' => ''],
                ['label' => '身份证号', 'field' => '${身份证号}', 'desc' => ''],
                ['label' => '培训课程', 'field' => '${培训课程}', 'desc' => ''],
                ['label' => '开课时间', 'field' => '${开课时间}', 'desc' => ''],
                ['label' => '学员学习情况', 'field' => '', 'desc' => '列表'],
                ['label' => '学员学习情况.章节', 'field' => '${课时记录.名称}${课时记录.类型}', 'desc' => ''],
                ['label' => '学员学习情况.课时', 'field' => '${课时记录.课时}', 'desc' => ''],
                ['label' => '学员学习情况.开始学习时间', 'field' => '${课时记录.开始学习时间}', 'desc' => ''],
                ['label' => '学员学习情况.结束学习时间', 'field' => '${课时记录.结束学习时间}', 'desc' => ''],
                ['label' => '学员学习情况.学习进度', 'field' => '${课时记录.学习进度}', 'desc' => ''],
            ],
            Template::TYPE_ENROLLMENT_FORM => [
                ['label' => '学号', 'field' => '${学号}', 'desc' => ''],
                ['label' => '姓名', 'field' => '${姓名}', 'desc' => ''],
                ['label' => '联系电话', 'field' => '${手机号}', 'desc' => ''],
                ['label' => '身份证号', 'field' => '${身份证号}', 'desc' => ''],
                ['label' => '照片', 'field' => '${照片}', 'desc' => '个人证件照，指定尺寸：${照片:160:160}'],
            ]
        ];

        foreach ($form->fields as $field) {
            if ($field['type'] != 'file') {
                $hourCert = [
                    'label' => $field['name'], 'field' => "\${{$field['name']}}", 'desc' => ''
                ];

                $hourRecord = [
                    'label' => '学员学习情况.' . $field['name'], 'field' => "\${课时记录.{$field['name']}}", 'desc' => ''
                ];

                $studentArchive = [
                    'label' => '课时记录.' . $field['name'], 'field' => "\${课时记录.{$field['name']}}", 'desc' => ''
                ];

                $enrollmentForm = [
                    'label' => $field['name'], 'field' => "\${{$field['name']}}", 'desc' => ''
                ];

                if ($field['type'] == 'pic') {
                    $hourCert['desc'] = "指定尺寸：" . str_replace('}', ':50:50}', $hourCert['field']);
                    $hourRecord['desc'] = "指定尺寸：" . str_replace('}', ':50:50}', $hourRecord['field']);
                    $studentArchive['desc'] = "指定尺寸：" . str_replace('}', ':50:50}', $studentArchive['field']);
                    $enrollmentForm['desc'] = "指定尺寸：" . str_replace('}', ':50:50}', $enrollmentForm['field']);
                }

                $fields[Template::TYPE_HOUR_CERT][] = $hourCert;
                $fields[Template::TYPE_STUDENT_ARCHIVE][] = $studentArchive;
                $fields[Template::TYPE_HOUR_RECORD][] = $hourRecord;
                $fields[Template::TYPE_ENROLLMENT_FORM][] = $enrollmentForm;
            }
        }

        return $fields;
    }
}
