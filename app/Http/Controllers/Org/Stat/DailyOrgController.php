<?php

namespace App\Http\Controllers\Org\Stat;

use App\Http\Controllers\Org\Controller;
use App\Models\Stat\Org;
use App\Services\Stat\DailyOrgService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DailyOrgController extends Controller
{
    public function getSubtotalData(): array
    {
        $admin = $this->auth();

        $today = DailyOrgService::getSubtotalData($admin->org_id, Carbon::now()->toDateString());
        $yesterday = DailyOrgService::getSubtotalData($admin->org_id, Carbon::yesterday()->toDateString());
        $subtotal = DailyOrgService::getSubtotalData($admin->org_id);

        return [
            'today' => $today,
            'yesterday' => $yesterday,
            'subtotal' => $subtotal,
        ];
    }

    public function getDateList(Request $request): array
    {
        $params = $request->validate([
            'date_range' => 'array|size:2',
            'admin_id' => 'integer'
        ]);

        $admin = $this->auth();

        if (!isset($params['date_range'])) {
            $endDate = Carbon::yesterday()->toDateString();
            $startDate = Carbon::parse($endDate)->subDays(14)->toDateString();
        } else {
            $startDate = $params['date_range'][0];
            $endDate = $params['date_range'][1];
        }

        $dateRange = DailyOrgService::getDateRange($startDate, $endDate);

        return Org::query()
            ->where('org_id', $admin->org_id)
            ->whereBetween('date', $dateRange)
            ->orderBy('date')
            ->get()
            ->toArray();
    }
}
