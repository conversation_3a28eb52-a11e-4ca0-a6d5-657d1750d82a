<?php

namespace App\Http\Controllers\Org;

use App\Models\Org;
use App\Models\Org\Admin\Admin;
use App\Services\Org\Admin\AdminService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    public function __construct(Request $request)
    {
        parent::__construct($request);

        $this->middleware('auth.org', ['except' => ['login']]);
    }

    public function login(Request $request): array
    {
        $params = $request->validate([
            'username' => 'required|string|min:3',
            'password' => 'required|string|min:6'
        ]);

        $admin = AdminService::login($params['username'], $params['password'], $this->ip);

        $token = auth('org')->login($admin);

        return $this->respondWithToken($token);
    }

    public function logout(): array
    {
        JWTAuth::parseToken()->invalidate();

        return $this->success();
    }

    public function me(): array
    {
        $admin = $this->auth();

        AdminService::update($admin->id, [
            'last_active_at' => Carbon::now(),
            'last_active_ip' => $this->ip
        ]);

        $org = Org::query()->find($admin->org_id);

        return [
            'userId' => $admin->id,
            'userName' => $admin->username,
            'roles' => AdminService::getRoles($admin),
            'org' => $org,
            'config' => [
                'org_pc_url' => config('heguibao.pc_url'),
                'org_applet_path' => '/pages/organization/home',
                'base_url' => config('app.url'),
            ]
        ];
    }

    protected function respondWithToken(string $token): array
    {
        return [
            'token' => $token,
            'refreshToken' => $token,
            'ttl' => JWTAuth::factory()->getTTL() * 60
        ];
    }
}
