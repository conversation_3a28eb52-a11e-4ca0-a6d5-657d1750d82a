<?php

namespace App\Http\Controllers\Org\Enrollment;

use App\Http\Controllers\Org\Controller;
use App\Models\Org\EnrollmentForm;
use App\Services\Org\Admin\OperateLogService;
use App\Services\Org\EnrollmentFormService;
use Illuminate\Http\Request;

class EnrollmentFormController extends Controller
{
    public $defaultFieldNames = ['姓名', '身份证号', '手机号'];

    public function show(): ?EnrollmentForm
    {
        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::query()->where('org_id', $this->auth()->org_id)->first();
        if ($form == null) {
            $form = new EnrollmentForm();
        }
        $form->fields = $this->buildData($form->fields?:[]);

        return $form ?: null;
    }

    public function update(Request $request): EnrollmentForm
    {
        $params = $request->validate([
            'title' => 'required|string',
            'fields' => 'required|array',
        ]);

        $filteredFields = array_filter($params['fields'], function($field) {
            return !in_array($field['name'] ?? '', $this->defaultFieldNames);
        });

        $form = EnrollmentFormService::update($this->auth()->org_id, $params['title'], array_values($filteredFields));
        $form->fields = $this->buildData($form->fields);

        OperateLogService::create($this->auth()->id, '修改报名表单', ['id' => $this->auth()->org_id, 'params' => $params]);

        $form->setHidden([]);

        return $form;
    }

    private function buildData($fields) {
        $defaultFields = [];

        foreach ($this->defaultFieldNames as $value) {
            $defaultFields = array_merge($defaultFields, [
                [
                    'id' => 0,
                    'name' => $value,
                    'type' => 'text',
                    'required' => true,
                ]
            ]);
        }
        return array_merge($defaultFields, $fields);
    }
}