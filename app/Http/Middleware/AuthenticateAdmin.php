<?php

namespace App\Http\Middleware;

use App\Models\Admin\Admin;
use App\Services\Admin\AdminService;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class AuthenticateAdmin extends Middleware
{
    public function handle($request, \Closure $next, ...$guards)
    {
        $this->authenticate($request, ['admin']);

        try {
            /** @var Admin $auth */
            $auth = $this->auth->user();
            AdminService::checkStatus($auth);
        } catch (\Exception $e) {
           throw new AccessDeniedHttpException($e->getMessage());
        }

        return $next($request);
    }
}
