<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;

/**
 * Restful 格式化控制器
 */
class RestfulAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, \Closure $next)
    {
        /**
         * @var Response $response
         */
        $response = $next($request);

        if ($response instanceof JsonResponse) {
            if (($response->getEncodingOptions() & JSON_UNESCAPED_UNICODE) == 0) {
                $response->setEncodingOptions(JSON_UNESCAPED_UNICODE);
            }
            $content = $response->getData();
        } else {
            $content = $response->getContent();
        }

        $status = $response->getStatusCode();

        if ($status >= 200 && $status < 300) {
            if ($response->headers->allPreserveCase()['Content-Type'][0] == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
                return $response;
            }
            return new JsonResponse(['code' => 200, 'data' => $content, 'message' => 'ok'], 200, $response->headers->allPreserveCase(), JSON_UNESCAPED_UNICODE);
        } else {
            return $response;
        }
    }
}
