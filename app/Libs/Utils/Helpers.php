<?php

namespace App\Libs\Utils;

use Carbon\Carbon;

class Helpers
{
    /**
     * 转换数据
     * @param array $data
     * @param string $parentIdKey
     * @param string $valueKey
     * @param string $labelKey
     * @param int $parentId
     * @param int $level
     * @param array $cascadesValue
     * @param array $cascadesLabel
     * @return array
     */
    public static function dataToTree(array $data, string $parentIdKey = 'parent_id', string $valueKey = 'id', string $labelKey = 'name', int $parentId = 0, int $level = 1, array $cascadesValue = [], array $cascadesLabel = []): array
    {
        $list = [];
        foreach ($data as $item) {
            $item['cascades_value'] = array_merge($cascadesValue, [$item[$valueKey]]);
            $item['cascades_label'] = array_merge($cascadesLabel, [$item[$labelKey]]);
            if ($item[$parentIdKey] === $parentId) {
                $item['level'] = $level;
                $children = self::dataToTree($data, $parentIdKey, $valueKey, $labelKey, $item[$valueKey], $level + 1, $item['cascades_value'], $item['cascades_label']);
                if (!empty($children)) {
                    $item['children'] = $children;
                }
                $list[] = $item;
            }
        }
        return $list;
    }

    /**
     * 数字转中文
     * @param $num
     * @return string
     */
    public static function numberToChinese($num): string
    {
        // 中文数字字符
        $chineseNumerals = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        $chineseUnits = ['', '十', '百', '千'];
        $bigUnits = ['', '万', '亿', '兆']; // 大单位

        // 处理负数
        $negative = $num < 0 ? '负' : '';
        $num = abs($num);

        // 处理小数部分
        if (str_contains((string)$num, '.')) {
            [$integerPart, $decimalPart] = explode('.', (string)$num);
        } else {
            $integerPart = $num;
            $decimalPart = '';
        }

        // 转换整数部分
        $integerPart = (string)$integerPart;
        $integerLen = mb_strlen($integerPart);
        $result = '';
        $zeroFlag = false; // 标记零是否需要输出

        for ($i = 0; $i < $integerLen; $i++) {
            $currentDigit = $integerPart[$i];
            $position = $integerLen - $i - 1;
            $unitIndex = $position % 4; // 千、百、十、个位的单位
            $bigUnitIndex = intdiv($position, 4); // 万、亿、兆的大单位

            if ($currentDigit === '0') {
                $zeroFlag = true;
            } else {
                if ($zeroFlag) {
                    $result .= $chineseNumerals[0];
                    $zeroFlag = false;
                }
                // 特殊处理十位（10-19 不显示 "一"）
                if ($unitIndex == 1 && $currentDigit == '1' && $position == 1) {
                    $result .= $chineseUnits[$unitIndex];
                } else {
                    $result .= $chineseNumerals[$currentDigit] . $chineseUnits[$unitIndex];
                }
            }

            // 添加大单位
            if ($unitIndex === 0 && !$zeroFlag) {
                $result .= $bigUnits[$bigUnitIndex];
            }
        }

        // 去掉尾部的"零"
        if (str_ends_with($result, $chineseNumerals[0])) {
            $result = mb_substr($result, 0, -1);
        }

        // 转换小数部分
        $decimalResult = '';
        if (!empty($decimalPart)) {
            $decimalResult .= '点';
            for ($i = 0; $i < strlen($decimalPart); $i++) {
                $decimalResult .= $chineseNumerals[$decimalPart[$i]];
            }
        }

        return $negative . $result . $decimalResult;
    }

    /**
     * 通过生日获取年龄
     * @param string $birthday 生日日期
     * @param string $format 日期格式
     * @return int
     */
    public static function getAgeByBirthday(string $birthday, string $format = 'Ymd'): int
    {
        $birthDate = Carbon::createFromFormat($format, $birthday);
        return $birthDate->age;
    }


    /**
     * 将透明背景图片转换为白色背景
     *
     * @param string $sourceFile 源文件路径
     * @param string $destFile 目标文件路径
     * @param array $options 额外选项 ['quality' => 90, 'bg_color' => [255, 255, 255]]
     * @return bool 操作是否成功
     * @throws \InvalidArgumentException
     * @throws \RuntimeException
     */
    public static function fillTransparentBackground(string $sourceFile, string $destFile, array $options = []): bool
    {
        // 合并默认选项
        $options = array_merge([
            'quality' => 90,
            'bg_color' => [255, 255, 255], // RGB白色
        ], $options);

        // 检查文件是否存在和可读
        if (!is_readable($sourceFile)) {
            throw new \InvalidArgumentException("源文件不存在或不可读: {$sourceFile}");
        }

        // 检查目标目录是否可写
        $destDir = dirname($destFile);
        if (!is_dir($destDir) || !is_writable($destDir)) {
            throw new \InvalidArgumentException("目标目录不存在或不可写: {$destDir}");
        }

        try {
            // 获取图片信息
            $imageInfo = @getimagesize($sourceFile);
            if ($imageInfo === false) {
                throw new \InvalidArgumentException("无法获取图片信息: {$sourceFile}");
            }

            $mimeType = $imageInfo['mime'];

            // 创建图像资源
            $sourceImage = match ($mimeType) {
                'image/png' => @imagecreatefrompng($sourceFile),
                'image/gif' => @imagecreatefromgif($sourceFile),
                'image/jpeg' => @imagecreatefromjpeg($sourceFile),
                'image/webp' => @imagecreatefromwebp($sourceFile),
                default => throw new \InvalidArgumentException("不支持的图片类型: {$mimeType}")
            };

            if ($sourceImage === false) {
                throw new \RuntimeException("无法创建图像资源: {$sourceFile}");
            }

            // 获取图像尺寸
            $width = imagesx($sourceImage);
            $height = imagesy($sourceImage);

            // 创建新的图像
            $newImage = imagecreatetruecolor($width, $height);
            if ($newImage === false) {
                throw new \RuntimeException("无法创建新图像");
            }

            // 设置背景颜色
            $bgColor = imagecolorallocate(
                $newImage,
                $options['bg_color'][0],
                $options['bg_color'][1],
                $options['bg_color'][2]
            );

            // 填充背景颜色
            imagefill($newImage, 0, 0, $bgColor);

            // 保留透明度信息
            imagealphablending($newImage, true);
            imagesavealpha($newImage, true);

            // 将原图复制到新图上
            if (!imagecopy($newImage, $sourceImage, 0, 0, 0, 0, $width, $height)) {
                throw new \RuntimeException("复制图像失败");
            }

            // 获取输出格式
            $outputFormat = strtolower(pathinfo($destFile, PATHINFO_EXTENSION));

            // 保存新图片
            $result = match ($outputFormat) {
                'png' => imagepng($newImage, $destFile),
                'gif' => imagegif($newImage, $destFile),
                'jpg', 'jpeg' => imagejpeg($newImage, $destFile, $options['quality']),
                'webp' => imagewebp($newImage, $destFile, $options['quality']),
                default => throw new \InvalidArgumentException("不支持的输出格式: {$outputFormat}")
            };

            if (!$result) {
                throw new \RuntimeException("保存图像失败: {$destFile}");
            }

            return true;
        } catch (\Throwable $e) {
            // 重新抛出异常，但确保资源已释放
            throw $e;
        } finally {
            // 释放内存（无论成功失败）
            if (isset($sourceImage) && is_resource($sourceImage)) {
                imagedestroy($sourceImage);
            }

            if (isset($newImage) && is_resource($newImage)) {
                imagedestroy($newImage);
            }
        }
    }
}
