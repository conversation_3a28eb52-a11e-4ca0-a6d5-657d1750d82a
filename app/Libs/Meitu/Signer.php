<?php
namespace App\Libs\Meitu;

use App\Services\HttpClientService;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Utils;
use InvalidArgumentException;

class Signer
{
    private const ALGORITHM = 'SDK-HMAC-SHA256';
    private const BASIC_DATE_FORMAT = "Ymd\THis\Z";
    private const HEADERS = [
        'X_DATE' => 'X-Sdk-Date',
        'HOST' => 'Host',
        'AUTHORIZATION' => 'Authorization',
        'CONTENT_SHA256' => 'X-Sdk-Content-Sha256'
    ];
    private HttpClientService $httpClient;

    public function __construct(
        private readonly string $key,
        private readonly string $secret
    ) {
        $this->httpClient = new HttpClientService();
    }

    /**
     * 获取正常返回的错误码对应的错误内容
     *
     * @param $code
     * @return string
     */
    public function getCodeError($code)
    {
        return match ($code) {
            20001 => '处理错误',
            20003 => '人脸缺失',
            20004 => '人脸数大于1',
            20007 => '未传入人脸点',
            20008 => '照片不符合规范',
            20009 => '不支持的type',
            20010 => '检测不到第二张图片的人脸',
            20011 => '垂直高度不满足',
            20012 => '水平宽度不满足',
            20013 => '分辨率过大',
            20014 => '查找不到图片',
            20015 => '图片超限',
            20020 => '五官缺失',
            20021 => '非正脸 点头 俯仰角过大',
            20022 => '非正脸 摇头 旋转角过大',
            20023 => '脸部占比小像素低',
            21001 => '加载模型失败',
            21002 => '头发mask缺失',
            21003 => '人脸个数错误',
            21004 => 'ar中plist解析错误',
            21005 => 'ar人脸错误',
            21006 => 'ar超过人脸范围',
            21007 => 'json内容错误',
            21008 => '背景图片缺失',
            21009 => 'bodymask缺失',
            21010 => '人脸角度错误',
            default => '未知错误'
        };
    }




    /**
     * 发送签名请求
     *
     * @param string $url 请求地址
     * @param string $method HTTP 方法
     * @param array $headers 请求头
     * @param array $body 请求体
     * @param bool $isJson
     * @return array
     * @throws GuzzleException
     */
    public function request(string $url, string $method, array $headers = [], array $body = [], bool $isJson = true): array
    {
        $headers = $this->prepareHeaders($headers);
        $signedHeaders = $this->signedHeaders($headers);

        $canonicalRequest = $this->createCanonicalRequest($method, $url, $headers, $body, $signedHeaders);
        $stringToSign = $this->createStringToSign($canonicalRequest, $headers[self::HEADERS['X_DATE']]);
        $signature = $this->signStringToSign($stringToSign, $this->secret);

        $headers[self::HEADERS['AUTHORIZATION']] = $this->createAuthHeader($signature, $signedHeaders);

        return match (strtoupper($method)) {
            'GET' => $this->httpClient->setHeaders($headers)->get($url),
            'POST' => $this->httpClient->setHeaders($headers)->post($url, $body, $isJson),
            'PUT' => $this->httpClient->setHeaders($headers)->put($url, $body),
            'DELETE' => $this->httpClient->setHeaders($headers)->delete($url, $body),
            default => throw new InvalidArgumentException("Unsupported HTTP method: $method")
        };
    }

    /**
     * 准备请求头
     *
     * @param  array  $headers  原始请求头
     * @return array  处理后的请求头
     */
    private function prepareHeaders(array $headers): array
    {
        if (!isset($headers[self::HEADERS['X_DATE']])) {
            $headers[self::HEADERS['X_DATE']] = Carbon::now('UTC')->format(self::BASIC_DATE_FORMAT);
        }

        if (!isset($headers[self::HEADERS['CONTENT_SHA256']])) {
            $headers[self::HEADERS['CONTENT_SHA256']] = '';
        }

        return $headers;
    }

    /**
     * 创建规范请求
     *
     * @param string $method HTTP 方法
     * @param string $url 请求地址
     * @param array $headers 请求头
     * @param array $body 请求体
     * @param array $signedHeaders 已签名的请求头
     * @return string
     */
    private function createCanonicalRequest(
        string $method,
        string $url,
        array $headers,
        array $body,
        array $signedHeaders
    ): string {
        $parsedUrl = parse_url($url);
        $canonicalUri = $this->canonicalUri($parsedUrl['path'] ?? '/');

        $query = [];
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $query);
            ksort($query);
        }

        $canonicalQueryString = http_build_query($query);
        $canonicalHeaders = $this->canonicalHeaders($headers, $signedHeaders);
        $signedHeadersString = implode(';', $signedHeaders);
        $body = Utils::jsonEncode($body);
        $contentHash = $headers[self::HEADERS['CONTENT_SHA256']] ?: hash('sha256', $body);

        return implode("\n", [
            $method,
            $canonicalUri,
            $canonicalQueryString,
            $canonicalHeaders,
            $signedHeadersString,
            $contentHash
        ]);
    }

    /**
     * 创建规范 URI
     *
     * @param  string  $path  原始路径
     * @return string  处理后的 URI
     */
    private function canonicalUri(string $path): string
    {
        return rtrim($path, '/') . '/';
    }

    /**
     * 创建规范头信息
     *
     * @param  array  $headers  请求头
     * @param  array  $signedHeaders  已签名的请求头
     * @return string  规范化的头信息字符串
     */
    private function canonicalHeaders(array $headers, array $signedHeaders): string
    {
        $canonicalHeaders = [];
        $normalizedHeaders = array_change_key_case($headers);

        foreach ($signedHeaders as $header) {
            $canonicalHeaders[] = $header . ':' . trim($normalizedHeaders[$header]);
        }

        return implode("\n", $canonicalHeaders);
    }

    /**
     * 获取签名头列表
     *
     * @param  array  $headers  请求头
     * @return array  需要签名的头信息列表
     */
    private function signedHeaders(array $headers): array
    {
        $signedHeaders = array_map('strtolower', array_keys($headers));
        sort($signedHeaders);
        return $signedHeaders;
    }

    /**
     * 创建待签名字符串
     *
     * @param  string  $canonicalRequest  规范请求
     * @param  string  $timestamp  时间戳
     * @return string  待签名的字符串
     */
    private function createStringToSign(string $canonicalRequest, string $timestamp): string
    {
        return implode("\n", [
            self::ALGORITHM,
            $timestamp,
            bin2hex(hash('sha256', $canonicalRequest, true))
        ]);
    }

    /**
     * 签名字符串
     *
     * @param  string  $stringToSign  待签名字符串
     * @param  string  $signingKey  签名密钥
     * @return string  签名结果
     */
    private function signStringToSign(string $stringToSign, string $signingKey): string
    {
        return bin2hex(hash_hmac('sha256', $stringToSign, $signingKey, true));
    }

    /**
     * 创建认证头
     *
     * @param  string  $signature  签名结果
     * @param  array  $signedHeaders  已签名的请求头
     * @return string  认证头值
     */
    private function createAuthHeader(string $signature, array $signedHeaders): string
    {
        $headerValue = sprintf(
            '%s Access=%s, SignedHeaders=%s, Signature=%s',
            self::ALGORITHM,
            $this->key,
            implode(';', $signedHeaders),
            $signature
        );

        return 'Bearer ' . base64_encode($headerValue);
    }
}
