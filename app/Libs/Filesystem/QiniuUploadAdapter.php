<?php

namespace App\Libs\Filesystem;

use App\Exceptions\ServiceException;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Http\Request;
use Overtrue\Flysystem\Qiniu\QiniuAdapter;
use Qiniu\Http\Client;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

use function Qiniu\base64_urlSafeDecode;

class QiniuUploadAdapter implements UploadAdapter
{

    public function __construct(private FilesystemAdapter $disk)
    {}

    public function getDisk(): FilesystemAdapter
    {
        if (!$this->disk) {
            throw new \RuntimeException('Unable to get adapter: disk not initialize.');
        }
        return $this->disk;
    }

    /**
     * @return QiniuAdapter
     */
    private function getAdapter()
    {
        return $this->getDisk()->getAdapter();
    }

    /**
     * @inheritDoc
     */
    public function form($prefix, $allowMimeTypes = [], $maxSizeKB = 0, $diskName = null): array
    {
        //七牛对象存储上传预设
        $policy = [
            //临时区域的文件名
            'saveKey' => 'tmp/'.$prefix.'-'.uniqid().'$(ext)',
            // 'returnBody' => '{"url":"","key":$(key),"filename":$(fname),"size":"$(fsize)","mime":"$(mimeType)"}'
        ];

        $isLocal = app()->isLocal();

        //本地使用跳转，否则使用回调以兼容小程序等更多场景
        if ($isLocal) {
            $policy['returnUrl'] = route('upload_info', ['disk'=>$diskName, 'type'=>'return']);
            $policy['returnBody'] = '{"key":$(key),"filename":$(fname),"size":$(fsize),"mime":$(mimeType)}';
        } else {
            $policy['callbackUrl'] = $isLocal ? 'http://requestbin.cn:80/133wvmc1' : route('upload_info', ['disk'=>$diskName, 'type'=>'callback']);
            $policy['callbackBody'] = '{"key":"$(key)","filename":"$(fname)","size":$(fsize),"mime":"$(mimeType)"}';
            $policy['callbackBodyType'] = 'application/json';
        }

        if ($maxSizeKB > 0) {
            $policy['fsizeLimit'] = $maxSizeKB * 1024;
        }

        if ($allowMimeTypes) {
            $policy['mimeLimit'] = join(';', $allowMimeTypes);
        }

        $token = $this->disk->getAdapter()->getUploadToken(policy: $policy);

        return [
            'method' => 'POST',
            'url' => 'https://upload.qiniup.com/',
            'name' => 'file',
            'form_params' => [
                'token' => $token
            ]
        ];
    }

    /**
     * @inheritDoc
     */
    public function response(Request $request, $type): array
    {
        if ($type == 'return') {
            $ret = $request->get('upload_ret');
            $code = $request->get('code');

            if (!$ret) {
                //七牛上传失败
                if ($code) {
                    $error = $request->get('error') ?: $code;
                    throw new BadRequestHttpException($error);
                }
                throw new BadRequestHttpException('请求错误，缺少 upload_ret.');
            }

            $ret = base64_urlSafeDecode($ret);

            if (!$ret) {
                throw new BadRequestHttpException('请求错误，upload_ret 解析失败。');
            }

            $data = json_decode($ret, true);

            if (!$data) {
                throw new BadRequestHttpException('请求错误，upload_ret 解析失败。');
            }
        } else {
            $data = $request->toArray();
        }

        $key = $data['key'];
        $filename = $data['filename'];
        $filesize = $data['size'];
        $mime = $data['mime'];

        // try {
        //     $meta = $this->metadata($key);
        // } catch (\Exception $e) {
        //     throw new BadRequestHttpException($e->getMessage());
        // }

        // $filesize = $meta['fsize'];
        // $mime = $meta['mimeType'];

        $url = $this->disk->temporaryUrl($key, 3600);

        return compact('key', 'filename', 'filesize', 'mime', 'url');
    }

    /**
     * @inheritDoc
     */
    public function info($key): array
    {
        $meta = $this->metadata($key);

        $info = [
            'hash' => $meta['hash'],
            'mime' => $meta['mimeType'],
            'filesize' => $meta['fsize']
        ];

        //获取图片宽高信息
        if (str_starts_with($meta['mimeType'], 'image/')) {
            $imageInfoUrl = $this->getAdapter()->privateDownloadUrl($key.'?imageInfo');
            $imageInfo = Client::get($imageInfoUrl)->json();

            if ($imageInfo === null) {
                //七牛使用测试域名时，因为测试域名不对参数进行保留，可能会始终返回图片本身
                throw new ServiceException('你可能正在使用七牛的测试域名，导致获取图片信息失败。');
            }

            if (isset($imageInfo['error'])) {
                throw new ServiceException('Get image info error: '.$imageInfo['error']);
            }

            $info['width'] = $imageInfo['width'];
            $info['height'] = $imageInfo['height'];
        }

        return $info;
    }

    private function metadata($key)
    {
        $adapter = $this->getAdapter();

        /** @var \Qiniu\Http\Error $error */
        [$meta, $error] = $adapter->getBucketManager()->stat($adapter->getBucket(), $key);

        if ($error) {
            throw new ServiceException('Get metadata error: '.$error->message());
        }

        return $meta;
    }

}
