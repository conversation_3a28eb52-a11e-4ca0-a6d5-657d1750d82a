<?php

namespace App\Libs\Filesystem;

use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Http\Request;

/**
 * 基于 Filesystem 实现的可以向内外存储直传的抽象层
 */
interface UploadAdapter
{

    /**
     * 获取用于上传的表单信息
     *
     * @param string $prefix 文件 key 前缀，建议使用用户的 uuid，后台使用管理员 ID
     * @param array $allowMimeTypes 允许上传的文件类型，空数组代表不限制
     * @param int $maxSizeKB 允许上传的文件大小，单位为 KB，例如 1024=1MB，0 代表不限制
     * @param string|null $diskName 选中的存储，不指定则使用 filesystems.php 中配置的默认存储
     * @return array{method: string, url: string, name: string, form_params: array} 上传表单基础信息
     */
    public function form($prefix, $allowMimeTypes=[], $maxSizeKB = 0, $diskName=null): array;

    /**
     * 获取上传后的文件信息用于返回给客户端
     *
     * 此方法直接用于控制器，所以接收的是 Request，并且会抛 HttpException。
     * 对于对象存储，此方法通常是用于跳转和回调的地址并接收和处理返回信息。
     *
     * @param Request $request 控制器中接收到的请求
     * @param string $type 返回类型，用于区分跳转和回调，取值 "return" 或 "callback"
     * @return array{key: string, filename: string, filesize: int, mime: string, url: string}
     */
    public function response(Request $request, $type): array;

    /**
     * 根据 key 获取用于存储的文件信息
     *
     * @param string $key 文件在该存储中的 key
     * @return array{hash: string, mime: string, filesize?: int, width?: int, height?: int, duration?: int}
     */
    public function info($key): array;

    public function getDisk(): FilesystemAdapter;

}
