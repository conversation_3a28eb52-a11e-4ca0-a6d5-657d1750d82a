<?php

namespace App\Libs\Logger;

use Illuminate\Log\ParsesLogConfiguration;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Logger;

/**
 * 这是一个组合 Laravel 原 daily 日志与 Loki Redis 推送日志的工厂类
 *
 * 通过指定为 custom 驱动，并使用此类可以简单的配置出一个包含 daily 与 loki 两个日志的 channel。
 * 使用 Loki 时，本地文件日志就没必要保留太久了，建议保留默认。
 */
class LokiStackFactory
{

    use ParsesLogConfiguration;

    /**
     * @param array{filename: string, level: string, days: int, permission: int, name: ?string} $config
     */
    public function __invoke($config)
    {
        $level = $this->level($config);
        $bubble = $config['bubble'] ?? true;

        $loki = config('loki-stack');

        $handlers = [];

        //本地文件
        if ($loki['with_file_handler']) {
            $handlers[] = new RotatingFileHandler(
                storage_path('logs/' . $config['filename']),
                $config['days'] ?? 7,
                $level,
                $bubble,
                $config['permission'] ?? 0660,
                $config['locking'] ?? false
            );
        }

        //Loki
        if ($loki['enable']) {
            $labels = array_merge($loki['default_labels'], ['filename'=>$config['filename']]);
            $handlers[] = new LokiRedisHandler($level, $bubble, $labels, $loki['connection']);
        }

        return new Logger($this->parseChannel($config), $handlers);
    }

    protected function getFallbackChannelName()
    {
        return app()->bound('env') ? app()->environment() : 'production';
    }

}
