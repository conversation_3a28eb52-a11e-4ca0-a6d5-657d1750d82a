<?php

namespace App\Libs\Wechat;

use App\Exceptions\ServiceException;
use EasyWeChat\MiniApp\Application;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class EasyWechatFactory
{

    public static function mp()
    {
        $config = config('easywechat.mp');
        return new Application($config);
    }

    /**
     * 发送小程序订阅消息
     *
     * @param string $openId openId
     * @param string $templateId 模板ID
     * @param array $data 发送数据
     * @param string|null $page 跳转小程序页面路径
     * @return array
     * @throws \EasyWeChat\Kernel\Exceptions\BadResponseException
     * @throws \Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public static function sendSubscribeMessage(string $openId, string $templateId, array $data, ?string $page = null): array
    {
        $message = [
            'touser' => $openId,
            'template_id' => $templateId,
            'data' => $data,
        ];

        // 如果有跳转页面，添加页面参数
        if ($page) {
            $message['page'] = $page;
        }

        $app = self::mp();
        $client = $app->getClient();

        $accessToken = self::getAccessToken();

        $result = $client->postJson(
            'cgi-bin/message/subscribe/send?access_token=' . $accessToken,
            $message
        )->toArray();

        self::checkResultFail($result);
        $client->reset();

        return $result;
    }

    public static function getAccessToken(bool $force = false): string
    {
        $app = self::mp();
        $accessToken = $app->getAccessToken();

        return $force ? $accessToken->refresh() : $accessToken->getToken();
    }

    public static function checkResultFail(array $data): void
    {
        if (isset($data['errcode']) && $data['errcode'] != 0) {
            // 记录错误日志
            Log::error('[wechat] '.$data['errmsg'], $data);

            if ($data['errcode'] == 40001) {
                self::getAccessToken(true);
            }

            throw new ServiceException($data['errmsg']);
        }
    }

    /**
     * 获取小程序码
     * @param string $path
     * @return string
     * @throws \EasyWeChat\Kernel\Exceptions\BadResponseException
     * @throws \Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public static function shareCode($path = "/pages/index/index"): string
    {
        $accessToken = self::getAccessToken();
        header('Content-Type: application/json; charset=utf-8');

        $data = [
            'access_token' => $accessToken,
            'path' => $path,
            "width" => 356
        ];
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post('https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token='.$accessToken, $data);
        $body = $response->body();
        if (substr(trim($body), 0, 1) === '{' || substr(trim($body), 0, 1) === '[') {
            $result = json_decode($body, true);
            self::checkResultFail($result);
            throw new ServiceException("再试一下");
        } else {
            return $response->body();
        }
    }
}
