<?php

namespace App\Providers;

use App\Events\SearchEvent;
use App\Listeners\SearchListener;
use App\Models\Ers\ServiceOrder;
use App\Models\Org\Enrollment;
use App\Observers\OrgEnrollmentObserver;
use App\Observers\ServiceOrderObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        SearchEvent::class => [
            SearchListener::class
        ]
    ];

    /**
     * 应用程序的模型观察者。
     *
     * @var array
     */
    protected $observers = [
        ServiceOrder::class => [ServiceOrderObserver::class],
        Enrollment::class => [OrgEnrollmentObserver::class],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        if ($this->app->isLocal()) {
            // 记录DB操作日志
            DB::listen(function ($query) {
                $sql = $query->sql;
                foreach ($query->bindings as $value) {
                    $sql = Str::replaceFirst('?', sprintf('%s', is_string($value) ? "'$value'" : $value), $sql);
                }
                Log::info(sprintf('%s; [%ss]', $sql, number_format($query->time / 1000, 5)));
            });
        }
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
