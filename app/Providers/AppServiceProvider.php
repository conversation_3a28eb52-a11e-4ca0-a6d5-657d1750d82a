<?php

namespace App\Providers;

use App\Core\Enums\BusinessType;
use App\Libs\Sms\HuaweiCloudGateway;
use App\Models\Cms\Content;
use App\Models\Ers\FormOrderForm;
use App\Models\Ers\PaymentOrderPayment;
use App\Models\Ers\SolutionOrderDownload;
use App\Models\Ers\SolutionOrderPreview;
use App\Models\Org\CourseSub;
use App\Models\Org\Enrollment;
use App\Models\Org\OrgClass;
use App\Models\PersonalAccessToken;
use App\Models\User;
use Hidehalo\Nanoid\Client;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;
use Laravel\Sanctum\Sanctum;
use Overtrue\EasySms\EasySms;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //HashIds
        $this->app->singleton('nanoid', Client::class);

        //EasySms
        $this->app->singleton(EasySms::class, function() {
            $easySms = new EasySms(config('easysms'));
            $easySms->extend('huawei-cloud', function($gatewayConfig) {
                return new HuaweiCloudGateway($gatewayConfig);
            });
            return $easySms;
        });

        // relation
        Relation::morphMap(Content::$resource);
        Relation::morphMap(['user' => User::class]);

        //将 BusinessType 的枚举映射为全局业务类型多态命名（用于收藏 、点赞、订单等多处关联）
        $businessMorphMap = [];

        foreach (BusinessType::cases() as $type) {
            $modelClass = $type->modelClass();
            $modelClass && $businessMorphMap[$type->value] = $modelClass;
        }

        Relation::enforceMorphMap($businessMorphMap);

        // 无奈方案，只能定义全局，不能给模型单独定义
        Relation::enforceMorphMap([
            'form' => FormOrderForm::class,
            'payment' => PaymentOrderPayment::class,
            'payment_stage' => PaymentOrderPayment::class,
            'payment_final' => PaymentOrderPayment::class,
            'solution_preview' => SolutionOrderPreview::class,
            'solution_download' => SolutionOrderDownload::class
        ]);

        // 设置 Enrollment 的多态映射
        Relation::morphMap(Enrollment::$resource);
        // 设置 CourseSub 的多态映射
        Relation::morphMap(CourseSub::$resource);
        // 设置 OrgClass 的多态映射
        Relation::morphMap(OrgClass::$resource);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Sanctum::usePersonalAccessTokenModel(PersonalAccessToken::class);
    }
}
