<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use App\Core\HashIdAttribute;
use App\Models\Admin\Admin;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class ServiceOrder
 *
 * @package App\Models
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $project_id 项目ID
 * @property int $flow_id 流程ID
 * @property int $industry_id 行业ID
 * @property int $enterprise_id 企业类别ID
 * @property int $status 状态 0 草稿，1 用户待办，2 后台待办，3 已完结
 * @property int $admin_id 对接管理员ID
 * @property \Illuminate\Support\Carbon|null finished_at 完成时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Project $project
 * @property-read User $user
 * @property-read ?Admin $operator
 * @property-read Collection<ServiceOrderStep> $steps
 * @mixin \Eloquent
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class ServiceOrder extends Model
{
	use SoftDeletes, HashIdAttribute;

    protected $table = 'ers_service_orders';

	protected $casts = [
		'user_id' => 'integer',
		'project_id' => 'integer',
		'flow_id' => 'integer',
        'industry_id' => 'integer',
        'enterprise_id' => 'integer',
		'status' => 'integer',
		'admin_id' => 'integer',
        'finished_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
	];

	protected $fillable = [
		'user_id',
		'project_id',
		'flow_id',
        'industry_id',
        'enterprise_id',
		'status',
		'admin_id',
        'finished_at'
	];

    protected $hidden = ['id', 'user_id', 'admin_id', 'deleted_at', 'operator', 'project'];

    protected $appends = ['sid'];

    /** @var int 草稿 */
    const STATUS_DRAFT = 0;
    /** @var int 用户等待 */
    const STATUS_USER_PENDING = 1;
    /** @var int 后台等待 */
    const STATUS_ADMIN_PENDING = 2;
    /** @var int 已完成 */
    const STATUS_FINISH = 3;

    public static function addHashIdSalt()
    {
        return 'ErsServiceOrder';
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function steps()
    {
        return $this->hasMany(ServiceOrderStep::class, 'order_id', 'id');
    }

    public function records()
    {
        return $this->hasMany(ServiceOrderRecord::class, 'order_id', 'id');
    }

    public function flow()
    {
        return $this->belongsTo(Flow::class);
    }

    public function industry()
    {
        return $this->belongsTo(Industry::class);
    }

    public function enterprise()
    {
        return $this->belongsTo(EnterpriseCategory::class, 'enterprise_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function operator()
    {
        return $this->belongsTo(Admin::class, 'admin_id', 'id');
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'project_id', 'flow_id', 'status']);
    }
}
