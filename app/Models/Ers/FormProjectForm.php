<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FormProjectForm
 *
 * @package App\Models
 * @property int $id
 * @property int $project_id 项目ID
 * @property int $flow_id 流程ID
 * @property int $step_id 步骤ID
 * @property int $industry_id 行业ID
 * @property int $enterprise_id 企业类别ID
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property FormProjectInput[]|Collection $inputs
 * @mixin \Eloquent
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class FormProjectForm extends Model
{
	protected $table = 'ers_form_project_forms';

	protected $casts = [
		'project_id' => 'integer',
		'flow_id' => 'integer',
		'step_id' => 'integer',
		'industry_id' => 'integer',
		'enterprise_id' => 'integer'
	];

	protected $fillable = [
		'project_id',
		'flow_id',
		'step_id',
		'industry_id',
		'enterprise_id'
	];

    protected $hidden = ['project_id', 'flow_id', 'step_id'];

    public function scopePublicFields($query)
    {
        $query->select(['id', 'project_id', 'flow_id', 'step_id']);
    }

    public function inputs()
    {
        return $this->hasMany(FormProjectInput::class, 'project_form_id')->orderBy('sort');
    }
}
