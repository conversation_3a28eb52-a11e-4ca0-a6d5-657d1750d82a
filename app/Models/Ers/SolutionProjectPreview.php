<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class SolutionProjectPreview
 *
 * @property int $id
 * @property int $project_id 项目ID
 * @property int $flow_id 流程ID
 * @property int $step_id 步骤ID
 * @property string $allow_upload_types 允许上传的方案文档类型
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @mixin \Eloquent
 */
class SolutionProjectPreview extends Model
{
	use SoftDeletes;
	protected $table = 'ers_solution_project_previews';

	protected $casts = [
		'project_id' => 'integer',
		'flow_id' => 'integer',
		'step_id' => 'integer'
	];

	protected $fillable = [
		'project_id',
		'flow_id',
		'step_id',
        'allow_upload_types'
	];
}
