<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SolutionOrderDownload
 *
 * @package App\Models
 * @property int $id
 * @property int $order_id 工单ID
 * @property int $order_step_id 工单步骤ID
 * @property string $email 邮箱地址
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class SolutionOrderDownload extends Model
{
	protected $table = 'ers_solution_order_downloads';

	protected $casts = [
		'order_id' => 'integer',
		'order_step_id' => 'integer'
	];

	protected $fillable = [
		'order_id',
		'order_step_id',
		'email'
	];

    protected $hidden = ['id', 'order_id'];

}
