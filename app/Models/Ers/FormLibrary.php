<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class FormLibrary
 *
 * @package App\Models
 * @property int $id
 * @property string $title 标题
 * @property string $type 类型（不可更改）
 * @property bool $is_required 是否必填
 * @property string $desc 描述
 * @property array $options 限制或选项
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @mixin \Eloquent
 */
class FormLibrary extends Model
{
	use SoftDeletes;

	protected $table = 'ers_form_libraries';

    // 类型
    const TYPE_GROUP = 'group';
    const TYPE_TEXT = 'text';
    const TYPE_TEXTAREA = 'textarea';
    const TYPE_SELECT = 'select';
    const TYPE_CHECKBOX = 'checkbox';
    const TYPE_IMAGE = 'image';
    const TYPE_FILE = 'file';

	protected $casts = [
        'is_required' => 'boolean',
        'options' => 'array'
	];

	protected $fillable = [
		'title',
		'type',
		'is_required',
		'desc',
		'options'
	];

    public static function getTypeLabels(): array
    {
        return [
            self::TYPE_GROUP => '表单分组',
            self::TYPE_TEXT => '单行文本',
            self::TYPE_TEXTAREA => '多行文本',
            self::TYPE_SELECT => '单选',
            self::TYPE_CHECKBOX => '多选',
            self::TYPE_IMAGE => '图片上传',
            self::TYPE_FILE => '文件上传',
        ];
    }
}
