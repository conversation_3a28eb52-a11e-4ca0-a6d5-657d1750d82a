<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * Class FormOrderData
 *
 * @package App\Models
 * @property int $id
 * @property int $order_id 工单ID
 * @property int $project_input_id 项目表单项ID
 * @property int $order_form_id 工单表单ID
 * @property array $data 表单值
 * @property int $status 状态：0=未提交，1=待审核，2=已通过，3=未通过
 * @property string $reject_reason 未通过理由
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 * @property-read FormProjectInput $input
 */
class FormOrderData extends Model
{
	protected $table = 'ers_form_order_data';

	protected $casts = [
		'order_id' => 'integer',
		'project_input_id' => 'integer',
		'order_form_id' => 'integer',
		'data' => 'array',
        'status' => 'integer'
	];

	protected $fillable = [
		'order_id',
		'project_input_id',
		'order_form_id',
		'data',
        'status',
		'reject_reason'
	];

    protected $hidden = ['id', 'order_id'];

    protected $with = ['input'];

    const STATUS_UN_SUBMIT = 0;

    const STATUS_PENDING = 1;

    const STATUS_SUCCESS = 2;

    const STATUS_REJECT = 3;

    /**
     * @param $files
     * @return array
     */
    public static function fileUrls($files)
    {
        $fileUrls = [];
        foreach ($files as $file) {
            if (isset($file['path'])) {
                $file['path_src'] = AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $file['path']);
            }
            $fileUrls[] = $file;
        }

        return $fileUrls;
    }

    public function input()
    {
        return $this->belongsTo(FormProjectInput::class, 'project_input_id')->withTrashed();
    }

}
