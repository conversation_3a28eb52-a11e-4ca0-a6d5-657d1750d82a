<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class SolutionOrderPreview
 *
 * @package App\Models
 * @property int $id
 * @property int $order_id 工单ID
 * @property int $order_step_id 工单步骤ID
 * @property array $files 文件
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class SolutionOrderPreview extends Model
{
	protected $table = 'ers_solution_order_previews';

	protected $casts = [
		'order_id' => 'integer',
		'order_step_id' => 'integer',
		'files' => 'array'
	];

	protected $fillable = [
		'order_id',
		'order_step_id',
		'files'
	];

    protected $hidden = ['id', 'order_id'];

}
