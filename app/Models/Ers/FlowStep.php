<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Ers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class FlowStep
 *
 * @property int $id
 * @property int $flow_id 流程ID
 * @property string $name 模块在流程中的名称，不填则代表模块的默认名称
 * @property string $module 模块
 * @property int $step 步骤，0开始
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read ServiceOrderStep $orderStep
 * @mixin \Eloquent
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @method \Illuminate\Database\Eloquent\Builder|self flowName()
 */
class FlowStep extends Model
{
    use SoftDeletes;

    /**
     * @var FormProjectForm|null
     */
    public mixed $form;

    protected $table = 'ers_flow_steps';

    protected $casts = [
        'flow_id' => 'integer',
        'step' => 'integer'
    ];

    protected $fillable = [
        'flow_id',
        'name',
        'module',
        'step'
    ];

    protected $hidden = ['deleted_at'];

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'name', 'module', 'step']);
    }

    public function scopeFlowName($query)
    {
        return $query->select(['id', 'name']);
    }

    public function orderStep()
    {
        return $this->belongsTo(ServiceOrderStep::class, 'id', 'step_id');
    }
}
