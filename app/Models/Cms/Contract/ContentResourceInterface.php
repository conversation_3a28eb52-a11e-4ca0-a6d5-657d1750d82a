<?php

namespace App\Models\Cms\Contract;

use Illuminate\Database\Eloquent\Builder;

/**
 * CMS 内容关联的主体资源
 */
interface ContentResourceInterface
{

    /**
     * 资源模型的公开作用域（用于列表以及其它公开场景）
     *
     * @param Builder $query
     */
    public function scopePublicFields($query);

    /**
     * 详情页作用域
     *
     * @param Builder $query
     */
    public function scopeDetail($query);

    /**
     * 加载更多内容到详情接口中
     *
     * @param array $data
     */
    public function detailAppend(array $data = []);

    /**
     * 用户已购列表作用域
     *
     * @param Builder $query
     * @param int $userId
     */
    public function scopeOwn($query, $userId);

    /**
     * 加载更多内容到用户已购列表中
     */
    public function ownAppend();

}
