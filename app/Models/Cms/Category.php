<?php

namespace App\Models\Cms;

use App\Core\HashIdAttribute;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property string $name 名称
 * @property string $intro 简介
 * @property string $logo 图标
 * @property int $pid 父ID
 * @property string $path 路径索引
 * @property string $classify 大的归类，而不是业务类型 material 资料，course 课程，news 资讯
 * @property int $visible 是否展示  0 否，1 是
 * @property string $allow_types 允许的内容类型  为空继承上级、或者指定多个类型
 * @property int $sort 排序
 * @property int $hot_position 热门分类中的位置，0 代表不是热门分类，1-7 代表位置
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property Category $parent
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class Category extends Model
{
    use HasFactory, HashIdAttribute;
    /**
     * The table associated with the model.
     */
    protected $table = 'cms_categories';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'name', 'intro', 'logo', 'pid', 'path', 'classify', 'visible', 'allow_types', 'sort', 'hot_position', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'pid' => 'integer', 'visible' => 'integer', 'sort' => 'integer', 'hot_position'=>'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'pid', 'path'];

    protected $appends = ['sid', 'logo_src'];

    /** @var int 隐藏 */
    const VISIBLE_NOT = 0;

    /** @var int 展示 */
    const VISIBLE = 1;

    const CLASSIFY_MATERIAL = 'material';
    const CLASSIFY_COURSE = 'course';
    const CLASSIFY_COURSE_PACK = 'course_pack';
    const CLASSIFY_NEWS = 'news';

    protected static function addHashIdSalt()
    {
        return 'CMS-Category';
    }

    public function allowTypes(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                return $value ? explode(',', $value) : [];
            },
            set: function ($value) {
                return implode(',', $value);
            },
        );
    }

    protected function getLogoSrcAttribute(): string
    {
        return $this->logo ? AttachmentService::url(Storage::disk(), $this->logo) : '';
    }

    public static function appointCategory()
    {
        return ["安全视频", "培训课件"];
    }

    public function parent()
    {
        return $this->belongsTo(Category::class, 'pid');
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'name', 'pid', 'intro', 'logo', 'classify', 'path']);
    }
}
