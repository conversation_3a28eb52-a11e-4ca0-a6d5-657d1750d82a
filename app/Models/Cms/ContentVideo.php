<?php

namespace App\Models\Cms;

use App\Models\Cms\Contract\ContentResourceInterface;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $content_id 内容ID
 * @property string $filepath 视频文件
 * @property int $filesize 文件大小
 * @property int $duration 视频时长（单位：秒）
 * @property array|null $extend 扩展信息
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read Content $content
 */
class ContentVideo extends Model implements ContentResourceInterface
{
    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_videos';

    protected $primaryKey = 'content_id';

    protected $hidden = ['content_id', 'extend', 'filepath'];

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['content_id', 'filepath', 'filesize', 'duration', 'extend', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['content_id' => 'integer', 'filesize' => 'integer', 'duration' => 'integer', 'extend' => 'array', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $appends = ['filepath_src', 'video_src', 'video_cover', 'format'];

    public function getFormatAttribute()
    {
        return 'video';
    }

    public function getVideoSrcAttribute()
    {
        if ($this->duration > 0 && is_array($this->extend) && array_key_exists('hd', $this->extend)) {
            if (isset($this->extend['hd'])) {
                return AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->extend['hd']);
            } else {
                return $this->getFilepathSrcAttribute();
            }
        }

        return "";
    }

    public function getVideoCoverAttribute()
    {
        if (is_array($this->extend) && array_key_exists('screenshot', $this->extend) && isset($this->extend['screenshot'])) {
            return AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->extend['screenshot']);
        }

        return "";
    }

    protected function getFilepathSrcAttribute(): string
    {
        return $this->filepath ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->filepath) : '';
    }

    public function scopePublicFields($query)
    {
        return $query->select(['content_id', 'duration', 'filepath', 'extend']);
    }

    public function content()
    {
        return $this->belongsTo(Content::class);
    }

    public function scopeDetail($query)
    {
        return $query->select(['content_id', 'duration', 'filepath', 'extend']);
    }

    public function detailAppend($data = []) {
        // TODO: Implement detailAppend() method.
    }

    public function scopeOwn($query, $userId) {
        // TODO: Implement scopeOwn() method.
    }

    public function ownAppend() {
        // TODO: Implement ownAppend() method.
    }
}
