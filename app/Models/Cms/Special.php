<?php

namespace App\Models\Cms;

use App\Core\HashIdAttribute;
use App\Core\OrderableInterface;
use App\Models\Admin\Admin;
use App\Models\Order\Order;
use App\Services\Common\AttachmentService;
use App\Services\User\OwnContentService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property string $name 名称
 * @property string $intro 简介
 * @property string $cover 封面
 * @property int $charge_credit 积分价格（0代表免费）
 * @property int $status 状态  0 草稿，1 正常，2 隐藏
 * @property string $recommend_at 推荐时间（默认NULL代表不推荐，推荐按时间倒排）
 * @property int $admin_id
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property SpecialContent[]|Collection $contents
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class Special extends Model implements OrderableInterface
{
    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected $table = 'cms_specials';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'name', 'intro', 'cover', 'charge_credit', 'status', 'recommend_at', 'admin_id', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'charge_credit' => 'integer', 'status' => 'integer', 'admin_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'admin_id'];

    protected $appends = ['sid', 'cover_src'];

    /** @var int 草稿 */
    const STATUS_DRAFT = 0;

    /** @var int 正常 */
    const STATUS_NORMAL = 1;

    /** @var int 隐藏 */
    const STATUS_HIDE = 2;

    public function scopePublicFields($query)
    {
        $query->select(['id', 'name', 'intro', 'cover', 'charge_credit']);
    }

    public function contents()
    {
        return $this->hasMany(SpecialContent::class, 'special_id', 'id')->orderBy('order');
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'admin_id', 'id');
    }

    protected function getCoverSrcAttribute(): string
    {
        return $this->cover ? AttachmentService::url(Storage::disk(), $this->cover) : '';
    }

    /**
     * @inheritDoc
     */
    protected static function addHashIdSalt()
    {
        return 'CMS-Special';
    }

    public function orderDelivery(Order $order): bool
    {
        /** @var SpecialContent[]|Collection $specialContents */
        $specialContents = SpecialContent::query()
            ->where('id', 1)
            ->with([
                'content' => function($query) {
                    $query->select(['id', 'category_id', 'type', 'title'])
                        ->with([
                            'category' => function($query) {
                                $query->select(['id', 'pid', 'path', 'classify']);
                            }
                        ]);
                }
            ])
            ->get();

        if ($specialContents->isEmpty()) {
            throw new \Exception('专题下没有任何内容可购买。');
        }

        //为用户添加专题下每一个内容的拥有权
        foreach ($specialContents as $sc) {
            OwnContentService::create($order->user_id, $sc->content_id, $sc->content->getClassify());
        }

        return true;
    }

    public function orderName(): string
    {
        return $this->name;
    }

}
