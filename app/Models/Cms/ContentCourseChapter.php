<?php

namespace App\Models\Cms;

use App\Libs\Utils\Helpers;
use App\Services\Org\CourseService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

/**
 * @property int $id
 * @property int $content_id 内容ID
 * @property string $name 名称
 * @property int $sections_count 学时数量
 * @property int $status 状态  0 隐藏，1 显示
 * @property int $sort 排序
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Illuminate\Database\Eloquent\Collection|ContentCourseSection[] $sections
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class ContentCourseChapter extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'cms_content_course_chapters';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'content_id', 'name', 'sections_count', 'status', 'sort', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'content_id' => 'integer', 'sections_count' => 'integer', 'status' => 'integer', 'sort' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'content_id', 'sort'];

    /** @var int 显示 */
    const STATUS_SHOW = 1;

    /** @var int 隐藏 */
    const STATUS_HIDE = 0;

    protected static function booted()
    {
        static::created(function (self $chapter) {
            Log::info('新增章');
            CourseService::updateCourseLessons('add', null, $chapter);
        });

        static::deleting(function (self $chapter) {
            //清除关联小节
            $chapter->sections->each(fn($section) => $section->delete());

            //清除关联学习记录
            ContentCourseProgress::query()->where('chapter_id', $chapter->id)->delete();

            Log::info('删除章');
            CourseService::updateCourseLessons('del', null, $chapter);
        });

        static::updated(function (self $chapter) {
            if ($chapter->isDirty('status')) {
                Log::info('更新章状态');
                CourseService::updateCourseLessons('update', null, $chapter);
            }
        });
    }

    public function content()
    {
        return $this->belongsTo(Content::class, 'content_id', 'id');
    }

    public function sections()
    {
        return $this->hasMany(ContentCourseSection::class, 'chapter_id', 'id')->orderByDesc('sort');
    }

    public function scopeContent($query)
    {
        $query->select(['id', 'content_id', 'name', 'sections_count', 'sort'])
            ->where('status', self::STATUS_SHOW)
            ->with('sections', function ($query) {
               $query->publicFields()
                   ->where('status', self::STATUS_SHOW)
                   ->orderbyDesc('sort')
                   ->orderBy('id');
            })
            ->orderbyDesc('sort')
            ->orderBy('id');
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'content_id', 'name', 'sections_count', 'sort']);
    }

    /**
     * 为章节列表包裹上序号
     *
     * 章和节会被自动附加上 sn 字段代表序号的表达文本
     *
     * @param Collection<static> $chapters
     * @return void
     */
    public static function wrapSn($chapters)
    {
        $autoChapterSn = 0;

        foreach ($chapters as $chapter) {
            $chapter->sn = '第'.Helpers::numberToChinese(++$autoChapterSn).'章'; //章自动序号
            $autoSectionSn = 0;

            foreach ($chapter->sections as $section) {
                $section->sn = sprintf('%02d', ++$autoSectionSn); //节自动序号
            }
        }
    }

    /**
     * 获取课程的章节树
     *
     * @param array $courseIds 课程ID列表
     * @param bool $wrapSn 是否附加序号
     *
     * @return \Illuminate\Database\Eloquent\Builder[]|Collection
     */
    public static function getCoursesTree(array $courseIds, bool $wrapSn = false)
    {
        $chapters = self::query()
            ->whereIn('content_id', $courseIds)
            ->where('status', self::STATUS_SHOW)
            ->with('sections', fn($query) => $query->where('status', ContentCourseChapter::STATUS_SHOW)->orderByDesc('sort'))
            ->orderByDesc('sort')
            ->get()
            ->groupBy('content_id');

        if ($wrapSn) {
            foreach ($chapters as $chapter) {
                self::wrapSn($chapter);
            }
        }

        return $chapters;
    }

}
