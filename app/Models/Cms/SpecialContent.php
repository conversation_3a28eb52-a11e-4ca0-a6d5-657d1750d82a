<?php

namespace App\Models\Cms;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $special_id 专题ID
 * @property int $content_id 内容ID
 * @property int $order 排序（正序）
 * @property \Carbon\Carbon $created_at
 *
 * @property-read Content $content
 */
class SpecialContent extends Model
{
    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     */
    protected $table = 'cms_special_contents';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'special_id', 'content_id', 'sort', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'special_id' => 'integer', 'content_id' => 'integer', 'sort'=>'integer', 'created_at' => 'datetime'];

    public function content()
    {
        return $this->belongsTo(Content::class);
    }
}
