<?php

namespace App\Models\Cms;

use App\Core\Enums\BusinessType;
use App\Core\HashIdAttribute;
use App\Core\OrderableInterface;
use App\Models\Admin\Admin;
use App\Models\Cms\Contract\ContentResourceInterface;
use App\Models\Order\Order;
use App\Models\Org\Enrollment;
use App\Models\User\UserAttitude;
use App\Models\User\UserFavorite;
use App\Models\User\UserOwnContent;
use App\Services\Cms\ContentService;
use App\Services\Common\AttachmentService;
use App\Services\Org\EnrollCourseService;
use App\Services\Org\EnrollmentService;
use App\Services\Org\StudentService;
use App\Services\User\AttitudeService;
use App\Services\User\FavoriteService;
use App\Services\User\OwnContentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Laravel\Scout\Searchable;

/**
 * @property int $id
 * @property int $category_id 分类ID
 * @property string $title 标题
 * @property int $type 类型  1 文档，2 富文本，3 视频，4 章节视频(课程)
 * @property string $cover 缩略图
 * @property string $intro 简介/描述
 * @property int $view_limit 观看限制  0 免费，1 积分，2 收费
 * @property int $charge_credit 收取积分
 * @property string $charge_amount 收取价格
 * @property int $status 状态  0 草稿，1 处理中，2 正常，3 隐藏
 * @property string $status_desc 状态描述
 * @property int $views 浏览次数
 * @property int $views_add 固定增加的浏览数
 * @property string $source 内容来源
 * @property string $release_at 发布时间
 * @property string $recommend_at 推荐时间
 * @property int $admin_id 创建或发布的管理员 ID
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $deleted_at
 * @property-read Admin|null $admin 创建或发布的管理员账号
 * @property-read Category $category
 * @property-read ContentDoc|ContentVideo|ContentRichText|ContentCourse|ContentCoursePack|ContentResourceInterface $resource
 * @property-read ContentRelation[]|Collection $relationList
 * @property-read bool $purchase 是否可以购买
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class Content extends Model implements OrderableInterface
{
    use HasFactory, HashIdAttribute, Searchable, SoftDeletes;

    /** @var int 草稿 */
    const STATUS_DRAFT = 0;

    /** @var int 处理中 */
    const STATUS_PROCESSING = 1;

    /** @var int 正常 */
    const STATUS_NORMAL = 2;

    /** @var int 隐藏 */
    const STATUS_HIDE = 3;

    /** @var int 文档 */
    const TYPE_DOC = 1;

    /** @var int 富文本 */
    const TYPE_RICH_TEXT = 2;

    /** @var int 视频 */
    const TYPE_VIDEO = 3;

    /** @var int 课程 */
    const TYPE_COURSE = 4;

    /** @var int 课程包 */
    const TYPE_COURSE_PACK = 5;

    const VIEW_LIMIT_FREE = 0;
    const VIEW_LIMIT_CREDIT = 1;
    const VIEW_LIMIT_AMOUNT = 2;
    const VIEW_LIMIT_CREDIT_AMOUNT = 3;

    /**
     * The table associated with the model.
     */
    protected $table = 'cms_contents';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'category_id', 'title', 'type', 'cover', 'intro', 'view_limit', 'charge_credit', 'charge_amount', 'status', 'status_desc', 'views', 'views_add', 'source', 'release_at', 'recommend_at', 'created_at', 'updated_at', 'deleted_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'category_id' => 'integer', 'type' => 'integer', 'view_limit' => 'integer', 'charge_credit' => 'integer', 'status' => 'integer', 'views' => 'integer', 'views_add'=>'integer', 'release_at' => 'datetime', 'recommend_at' => 'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'category_id', 'type', 'views_add', 'admin_id', 'laravel_through_key'];

    protected $appends = ['sid', 'type_label', 'cover_src'];

    protected $userId = null;
    private int $enrollId;

    public static $resource = [
        '1' => ContentDoc::class,
        '2' => ContentRichText::class,
        '3' => ContentVideo::class,
        '4' => ContentCourse::class,
        '5' => ContentCoursePack::class
    ];

    public static $businessTypes = [
        BusinessType::Content,
        BusinessType::CmsMaterial,
        BusinessType::CmsCourse,
        BusinessType::CmsCoursePack,
        BusinessType::CmsNews
    ];

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopePublicFields(Builder $query): Builder
    {
        return $query->select(['id', 'category_id', 'title', 'intro', 'type', 'cover', 'source', 'views', 'views_add', 'release_at', 'view_limit', 'charge_credit', 'charge_amount']);
    }

    public static array $typeLabels = [
        self::TYPE_DOC => 'doc',
        self::TYPE_RICH_TEXT => 'rich_text',
        self::TYPE_VIDEO => 'video',
        self::TYPE_COURSE => 'course',
        self::TYPE_COURSE_PACK => 'course_pack'
    ];

    public function getTypeLabelAttribute()
    {
        return self::$typeLabels[$this->type];
    }

    public function scopeWithOwnState(Builder $query, int $userId, int $enrollId=0)
    {
        return $query->withExists(['ownContents as own_content' => fn($query) => $query->where('user_id', $userId)->where('enroll_id', $enrollId)])
            ->withExists(['userAttitude as is_attitude' => fn($query) => $query->where('user_id', $userId)])
            ->withExists(['userFavorite as is_favorite' => fn($query) => $query->where('user_id', $userId)]);
    }

    /**
     * 关联为指定用户的资源（协助获取关联的用户拥有、收藏、点赞等状态）
     *
     * @param int $userId
     * @param int $enrollId
     * @return $this
     */
    public function forUser($userId, int $enrollId=0)
    {
        $this->userId = $userId;
        $this->enrollId = $enrollId;
        return $this;
    }

    /**
     * 内容是否可以下载
     */
    protected function download(): Attribute
    {
        return Attribute::make(
            get: fn () => ContentService::canView($this->userId, $this, $this->enrollId),
        )->shouldCache();
    }

    /**
     * 内容是否收藏
     */
    protected function favorite(): Attribute
    {
        return Attribute::make(
            // 调用 FavoriteService 检查内容是否被收藏
            get: fn () => FavoriteService::checkFavorite($this->userId, $this),
        )->shouldCache();
    }

    /**
     * 获取内容业务类型
     *
     * @return BusinessType
     */
    public function getBusinessTypeAttribute(): BusinessType
    {
        return $this->getBusinessType();
    }

    /**
     * 内容点赞情况
     */
    public function attitude(): Attribute
    {
        return Attribute::make(
            get: fn () => AttitudeService::getAttitude($this->userId, $this),
        )->shouldCache();
    }

    /**
     * 是否允许支付
     *
     * 对于隐藏的内容，不允许单独支付，注意此属性要求 view_limit 和 status 字段判断，publicFields() 作用域不包含 status 字段，
     * 要调用此虚拟属性，请使用 addSelect('status') 并记得 makeHidden('status')
     *
     * @return bool
     */
    public function getPurchaseAttribute(): bool
    {
        return $this->view_limit != Content::VIEW_LIMIT_FREE && $this->status == Content::STATUS_NORMAL;
    }

    public function resource()
    {
        return $this->morphTo('resource', 'type', 'id');
    }

    /**
     * @return class-string<Model>
     */
    public function getResourceClass()
    {
        return self::$resource[$this->type];
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function relationList()
    {
        return $this->hasMany(ContentRelation::class, 'content_id');
    }

    public function ownContents()
    {
        return $this->hasMany(UserOwnContent::class, 'content_id');
    }

    public function userAttitude()
    {
        return $this->hasOne(UserAttitude::class,'business_id', 'id')
            ->whereIn("business_type", self::$businessTypes);
    }

    public function userFavorite()
    {
        return $this->hasOne(UserFavorite::class,'business_id', 'id')
            ->whereIn("business_type", self::$businessTypes);
    }

    /**
     * 内容封面图片的完整 URL
     *
     * @return Attribute
     */
    protected function coverSrc(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->cover
                ? AttachmentService::url(Storage::disk(), $this->cover)
                : config('app.url') . '/static/images/no-image.jpg',
        )->shouldCache();
    }

    /**
     * 获取内容所属归类
     *
     * @return string
     */
    public function getClassify()
    {
        if ($this->category) {
            if ($this->category->classify) {
                return $this->category->classify;
            } elseif ($this->category->pid != 0) {
                //所属分类没有归类设置，则直接向顶级分类查找
                [$topId] = explode('-', $this->category->path);
                $topClassify = Category::query()->where('id', $topId)->value('classify');
                if ($topClassify) {
                    return $topClassify;
                }
            }
        }

        return null;
    }

    /**
     * 获取内容的业务类型
     *
     * @return BusinessType
     */
    public function getBusinessType()
    {
        $classify = $this->getClassify();

        if (isset($classify)) {
            return BusinessType::from('cms.' . $classify);
        } else {
            return BusinessType::Content;
        }
    }

    /**
     * @inheritDoc
     */
    protected static function addHashIdSalt()
    {
        return 'CMS-Content';
    }

    /**
     * 获取模型的可索引的数据。
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'category_id' => $this->category_id,
            'type' => self::$typeLabels[$this->type],
            'doc_type' => $this->document ? $this->document->format : '',
            'title' => $this->title ?? '',
            'intro' => $this->intro ?? '',
            'status' => $this->status,
            'release_at' => ($this->release_at ?? $this->created_at)->toDateTimeString()
        ];
    }

    /**
     * @throws \Throwable
     */
    public function orderDelivery(Order $order): bool
    {
        $orgId = $order->extend['org_id'] ?? 0;
        $expiredAt = null;

        if ($orgId) {
            if (in_array($this->getBusinessType(), [BusinessType::CmsCourse, BusinessType::CmsCoursePack])) {
                $expiredAt = Carbon::now()->addMonths(UserOwnContent::ORG_EXPIRED_MOUTH_DEFAULT);
            }

            // ！！注意新增类型要改这里
            $type = $this->getBusinessType() == BusinessType::CmsCourse ? Enrollment::TYPE_COURSE : Enrollment::TYPE_COURSE_PACK;

            $student = StudentService::check($orgId, $order->user_id, '', $order->user->phone, '');
            $enrollment = EnrollmentService::createEnroll($orgId, $order->user_id, $student->id, $type, $order->business_id);
            EnrollCourseService::create($enrollment);
        }

        OwnContentService::create(
            $order->user_id,
            $order->business_id,
            $this->getClassify(),
            expiredAt: $expiredAt,
            orgId: $orgId,
            enrollId: $enrollId ?? 0
        );

        return true;
    }

    public function orderName(): string
    {
        return $this->title;
    }

    /**
     * 获取搜索内容类型
     *
     * @return array
     */
    public static function getSearchTypes(): array
    {
        $types = array_values(self::$typeLabels);
        $docTypes = ContentDoc::getFormat();

        return array_merge($types, $docTypes);
    }

    /**
     * 显示叠加的计数
     */
    public function views(): Attribute
    {
        $add = $this->views_add ?? 0;
        //这里的 set 是为了不对实际修改 views 时造成影响
        return Attribute::make(
            get: fn($value, array $attrs) => $value + $add,
            set: fn($value, array $attrs) => $value - $add
        );
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * 学习内容章节进度
     * @return void
     */
    public function progresses()
    {
        return $this->hasMany(ContentCourseProgress::class,'content_id', 'id');
    }
    /**
     * 学习内容
     * @return void
     */
    public function sections()
    {
        return $this->hasMany(ContentCourseSection::class,'content_id', 'id');
    }
}
