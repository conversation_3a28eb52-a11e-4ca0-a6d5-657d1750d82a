<?php

namespace App\Models\User;

use App\Models\Cms\Content;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $content_id 资料ID
 * @property \Carbon\Carbon $created_at
 */
class UserContentDownload extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'user_content_downloads';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'content_id', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'content_id' => 'integer', 'created_at' => 'datetime'];

    public $timestamps = false;

    public function content()
    {
        return $this->belongsTo(Content::class, 'content_id', 'id');
    }
}
