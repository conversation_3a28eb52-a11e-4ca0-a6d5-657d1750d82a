<?php

namespace App\Models\User;

use App\Models\Train\Topic;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 *
 *
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $topic_id 题库ID
 * @property \Carbon\Carbon|null $expired_at 过期时间
 * @property \Carbon\Carbon $created_at
 * @property string $deleted_at
 * @property int $org_id 机构id
 * @property int $enroll_id 学员id
 * @property-read Topic|null $topic
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereEnrollId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereOrgId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereTopicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|UserOwnTopic withoutTrashed()
 * @mixin \Eloquent
 */
class UserOwnTopic extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     */
    protected $table = 'user_own_topics';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'topic_id', 'expired_at', 'created_at', 'deleted_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'topic_id' => 'integer', 'expired_at'=>'datetime', 'created_at' => 'datetime'];

    const UPDATED_AT = null;

    /**
     * 为用户添加拥有的题库
     *
     * @param int $userId
     * @param int $topicId
     * @param \Carbon\Carbon|null 截止有效期，null 代表永久有效
     * @param int $orgId
     * @param int $enrollId
     * @return static
     */
    public static function add(int $userId,int $topicId, $expiredAt,int $orgId = null,int $enrollId = null)
    {
        $own = new self();
        $own->user_id = $userId;
        $own->topic_id = $topicId;
        $own->expired_at = $expiredAt;
        $own->org_id = $orgId;
        $own->enroll_id = $enrollId;
        $own->save();
        return $own;
    }

    public function topic()
    {
        return $this->belongsTo(Topic::class, 'topic_id', 'id');
    }

}
