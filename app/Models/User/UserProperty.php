<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $user_id 内容ID
 * @property string $tips 提示标记
 * @property int $current_topic_id 当前题库ID
 * @property string $nickname_edited_at 昵称修改时间
 * @property string $avatar_edited_at 头像修改时间
 * @property string $info_edited_at 账号修改时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserProperty extends Model
{
    protected $primaryKey = 'user_id';

    /**
     * The table associated with the model.
     */
    protected $table = 'user_properties';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['user_id', 'tips', 'current_topic_id', 'info_edited_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['user_id' => 'integer', 'tips' => 'array', 'current_topic_id' => 'integer', 'created_at' =>
        'datetime', 'updated_at' => 'datetime'];

    protected $attributes = [
        'current_topic_id' => 0,
        'tips' => '',
        'nickname_edited_at' => null,
        'avatar_edited_at' => null,
    ];

    /**
     * 获取包含用户某些字段值的模型实例
     *
     * @param int $userId
     * @param array $columns
     * @return UserProperty
     */
    public static function get($userId, $columns = ['*']): UserProperty
    {
        if (!in_array('*', $columns)) {
            array_push($columns, 'user_id', 'created_at', 'updated_at');
            $columns = array_unique($columns);
        }

        $prop = self::query()->findOrNew($userId, $columns);

        if (!$prop->user_id) {
            $prop->user_id = $userId;
        }

        return $prop;
    }

}
