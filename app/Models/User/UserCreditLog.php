<?php

namespace App\Models\User;

use App\Core\Enums\BusinessType;
use App\Core\HashIdAttribute;
use App\Core\OrderableInterface;
use App\Models\Order\Order;
use App\Models\User;
use App\Services\User\CreditService;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $origin_credit 变更前积分
 * @property int $change_credit 最初变更积分数（正为加，负为减）
 * @property int $real_change_credit 实际变更数量（如果退了为0，没退则与预计保持一致）
 * @property string $type 变更类型 recharge 收入，consume 减少
 * @property BusinessType $business_type 业务类型
 * @property int $business_id 业务ID
 * @property string $remark 备注
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class UserCreditLog extends Model implements OrderableInterface
{

    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected $table = 'user_credit_logs';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'origin_credit', 'change_credit', 'real_change_credit', 'type', 'business_type', 'business_id', 'remark', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'origin_credit' => 'integer', 'change_credit' => 'integer', 'real_change_credit' => 'integer', 'business_type'=>BusinessType::class, 'business_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'business_id'];

    protected $appends = ['sid'];

    /** @var string 收入 */
    const TYPE_RECHARGE = 'recharge';

    /** @var string 消费 */
    const TYPE_CONSUME = 'consume';

    const ACTION_ORDER = "order";
    const ACTION_INVITATION = "invitation";

    public function scopePublicFields($query)
    {
        $query->select(['id', 'user_id', 'change_credit', 'real_change_credit', 'type', 'business_type', 'business_id']);
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public static function getActionLabels($type)
    {
        return match($type) {
            self::ACTION_ORDER => "订单",
            self::ACTION_INVITATION => "邀请好友",
            default => ""
        };
    }

    /**
     * @inheritDoc
     */
    public function orderDelivery(Order $order): bool
    {
        $credits = CreditService::calcAmountCredit($order->total_amount);
        CreditService::recharge($order->user_id, $credits, BusinessType::Order, $order->id, $this->orderName());
        return true;
    }

    /**
     * @inheritDoc
     */
    public function orderName(): string
    {
        return '积分充值';
    }

}
