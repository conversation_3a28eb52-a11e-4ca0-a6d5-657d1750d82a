<?php

namespace App\Models\User;

use App\Models\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $platform 平台
 * @property string $open_id OPEN_ID
 * @property string $union_id UNION_ID
 * @property string $access_token 三方accessToken
 * @property string $nickname 昵称
 * @property string $avatar 头像
 * @property \Carbon\Carbon $last_logged_at 最后登录时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read User $user
 */
class UserBind extends Model
{

    const PLATFORM_WECHAT_MP = 'wechat.mp';

    const LABELS = [
        self::PLATFORM_WECHAT_MP => "微信",
    ];
    /**
     * The table associated with the model.
     */
    protected $table = 'user_binds';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'platform', 'open_id', 'union_id', 'access_token', 'nickname', 'avatar', 'last_logged_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'last_logged_at'=>'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function mp($type)
    {
        return match($type) {
            self::PLATFORM_WECHAT_MP => "微信",
            default => ""
        };
    }

    /**
     * 头像转换为访问地址
     */
    public function labels(): Attribute
    {
        $data = $this;
        return Attribute::make(
            get: function ($value) use ($data) {
                return self::LABELS[$data->platform];
            },
        );
    }

    /**
     * 添加绑定
     *
     * @param int $userId 用户ID
     * @param string $platform 平台
     * @param string $openId OPEN_ID
     * @param string $unionId UNION_ID
     * @param string $accessToken 三方accessToken
     * @param string $nickname 昵称
     * @param string $avatar 头像
     * @return UserBind
     */
    public static function bind($userId, $platform, $openId, $unionId='', $accessToken='', $nickname='', $avatar='')
    {
        $bind = new self();
        $bind->user_id = $userId;
        $bind->platform = $platform;
        $bind->open_id = $openId;
        $bind->union_id = $unionId;
        $bind->access_token = $accessToken;
        $bind->nickname = $nickname;
        $bind->avatar = $avatar;
        $bind->last_logged_at = now();
        $bind->save();

        return $bind;
    }

}
