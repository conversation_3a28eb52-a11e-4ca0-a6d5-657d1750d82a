<?php

namespace App\Models\Qa;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 问答标签关联
 *
 * @property int $id
 * @property int $tag_id 标签ID
 * @property int $question_id 问题ID
 * @property \Illuminate\Support\Carbon $created_at
 * @method static \Illuminate\Database\Eloquent\Builder|TagRelation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TagRelation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TagRelation whereQuestionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TagRelation whereTagId($value)
 * @mixin \Eloquent
 */
class TagRelation extends Model
{
    use HasFactory;

    protected $table = 'qa_tag_relations';

    protected $fillable = ['tag_id', 'question_id'];

    const UPDATED_AT = null;

}
