<?php

namespace App\Models\Qa;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 问答标签
 *
 * @property int $id
 * @property string $name 标签名称
 * @property int $system 是否系统标签
 * @property string|null $recommend_at 推荐时间
 * @property int $ref_count 引用计数
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereRecommendAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereRefCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Tag whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Tag extends Model
{
    use HasFactory;

    protected $table = 'qa_tags';

    protected $fillable = ['name'];

    protected $hidden = ['system', 'recommend_at', 'ref_count', 'created_at', 'updated_at', 'laravel_through_key'];

    /**
     * @param Builder $query
     * @param bool $inRelationQuery 是否在关联查询中，在关联查询中则需要添加表前缀以解决查询时的字段冲突
     * @return Builder
     */
    public function scopePublicFields($query, $inRelationQuery=false)
    {
        $prefix = $inRelationQuery ? $this->table.'.' : '';
        return $query->select([$prefix.'id', $prefix.'name']);
    }

    public function addRelation($questionId)
    {
        $relation = TagRelation::create([
            'tag_id' => $this->id,
            'question_id' => $questionId
        ]);

        $this->increment('ref_count');

        return $relation;
    }

}
