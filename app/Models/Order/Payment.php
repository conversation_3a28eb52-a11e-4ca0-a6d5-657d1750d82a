<?php

namespace App\Models\Order;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $order_id 订单ID
 * @property string $out_trade_no 商户（我们自己）支付单号（使用订单编号作为前缀）
 * @property string|null $transaction_no 支付平台交易单号
 * @property string $platform 平台  alipay 支付宝支付，wechat 微信支付
 * @property string $client 客户端  mp 小程序
 * @property string $amount 实际支付金额
 * @property int $status 状态  0 待支付，1 已付款，2 已退款
 * @property \Carbon\Carbon|null $payment_at 支付时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @property-read Order $order 所属订单
 */
class Payment extends Model
{

    /** @var int 等待支付 */
    const STATUS_UNPAID = 0;

    /** @var int 已经付款 */
    const STATUS_PAID = 1;

    /** @var int 正在退款中 */
    const STATUS_REF_HAVE = 2;

    /** @var int 已经完成退款 */
    const STATUS_REFUNDED = 3;

    const PLATFORM_WECHAT = 'wechat';

    const CLIENT_MP = 'mp';

    /**
     * The table associated with the model.
     */
    protected $table = 'order_payments';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'order_id', 'out_trade_no', 'transaction_no', 'platform', 'client', 'amount', 'status', 'payment_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'order_id' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'payment_at'=>'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['id', 'user_id', 'order_id'];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

}
