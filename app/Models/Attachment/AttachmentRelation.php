<?php

namespace App\Models\Attachment;

use App\Core\Enums\BusinessType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $file_id 文件ID
 * @property int $target_id 引用方ID
 * @property BusinessType $target_type 引用方类型
 * @property \Carbon\Carbon $created_at
 *
 * @property-read AttachmentFile $file
 */
class AttachmentRelation extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'attachment_relations';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'file_id', 'target_id', 'target_type', 'created_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'file_id' => 'integer', 'target_type'=>BusinessType::class, 'target_id' => 'integer', 'created_at' => 'datetime'];

    const UPDATED_AT = null;

    public function file()
	{
		return $this->belongsTo(AttachmentFile::class);
	}

	/**
	 * 根据关系遍历附件关联
	 *
	 * @param BusinessType $targetType
	 * @param array|int $targetIds
	 * @return Collection|static[]
	 */
	public static function fetchByRelations(BusinessType $targetType, $targetIds)
	{
		!is_array($targetIds) && $targetIds = [$targetIds];
		return self::query()
            ->with('file')
			->where('target_type', $targetType)
			->whereIn('target_id', $targetIds)
			->get();
	}

	/**
	 * 统计指定文件已有关联数量
	 *
	 * @param int $fileId
	 * @return int
	 */
	public static function countByFileId($fileId)
	{
		return self::query()->where('file_id', $fileId)->count();
	}

    /**
     * 为附件文件建立关联
     *
     * @param AttachmentFile $file
     * @param BusinessType $targetType
     * @param int|array $targetIds
     * @return void
     */
    public static function saveRelations(AttachmentFile $file, BusinessType $targetType, $targetIds)
    {
        !is_array($targetIds) && $targetIds = [$targetIds];

        foreach ($targetIds as $targetId) {
            $id = AttachmentRelation::query()
                ->select('id')
                ->where(['target_type'=>$targetType, 'target_id'=>$targetId, 'file_id'=>$file->id])
                ->first();

            if (!$id) {
                $relation = new AttachmentRelation();
                $relation->file_id = $file->id;
                $relation->target_id = $targetId;
                $relation->target_type = $targetType;
                $relation->save();
            }
        }
    }

}
