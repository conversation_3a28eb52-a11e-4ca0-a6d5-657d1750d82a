<?php
namespace App\Models\Org;

use App\Core\HashIdAttribute;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property int $enroll_id 报名ID
 * @property string $name 姓名
 * @property string $phone 手机号码
 * @property string $id_card_front 身份证正面图
 * @property string $id_card_back 身份证反面图
 * @property string $id_card_number 身份证号
 * @property string $photo 照片
 * @property string $extra 扩展信息
 * @property Carbon $created_at 创建时间
 *
 * 班级学员存档表
 */
class EnrollArchive extends Model
{
    use HashIdAttribute;

    const UPDATED_AT = null;

    /**
     * The table associated with the model.
     */
    protected $table = 'org_enroll_archives';

    protected $fillable = [
        'enroll_id',
        'name',
        'phone',
        'id_card_front',
        'id_card_back',
        'id_card_number',
        'photo',
        'extra',
        'created_at',
    ];

    protected $casts = [
        'enroll_id' => 'integer',
        'extra' => 'array',
        'created_at' => 'datetime'
    ];

    public function getIdCardFrontUrlAttribute(): string
    {
        return $this->id_card_front
            ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->id_card_front)
            : '';
    }

    public function getIdCardBackUrlAttribute(): string
    {
        return $this->id_card_back
            ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->id_card_back)
            : '';
    }

    public function getPhotoUrlAttribute(): string
    {
        return $this->photo
            ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->photo)
            : '';
    }

    public function extra(): Attribute
    {
        return Attribute::make(
            get: function ($extra) {
                $extra = $extra ? json_decode($extra, true) : [];

                if (is_array($extra)) {
                    foreach ($extra as &$item) {
                        if (in_array($item['type'], EnrollmentForm::fileTypes())) {
                            if ($item['type'] == EnrollmentForm::TYPE_PHOTO) {
                                $item['value'] = AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $item['value']);
                            } else {
                                foreach ($item['value'] as &$value) {
                                    $value['path_url'] = AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $value['path']);
                                }
                            }
                        }
                    }
                } else {
                    $extra = [];
                }

                return $extra;
            }
        );
    }
}
