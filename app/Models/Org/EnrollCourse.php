<?php
namespace App\Models\Org;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property int $org_id 所属机构ID
 * @property int $enroll_id 报名ID
 * @property int $course_id 课程ID
 * @property int $learned_duration 已学学时（秒数）
 * @property bool $learn_finished 是否已学完
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * 学习的课程表
 */
class EnrollCourse extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_enroll_courses';

    protected $fillable = [
        'id',
        'org_id',
        'enroll_id',
        'course_id',
        'learned_duration',
        'learn_finished',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'enroll_id' => 'integer',
        'course_id' => 'integer',
        'learned_duration' => 'integer',
        'learn_finished' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
}
