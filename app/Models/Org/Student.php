<?php
namespace App\Models\Org;

use App\Core\HashIdAttribute;
use App\Models\Org;
use App\Services\Common\AttachmentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property int $org_id 所属机构ID
 * @property int $user_id 用户ID
 * @property string $name 姓名
 * @property string $phone 手机号码
 * @property string $work_unit 工作单位
 * @property string $id_card_front 身份证正面图
 * @property string $id_card_back 身份证反面图
 * @property string $id_card_number 身份证号
 * @property string $photo 照片
 * // 扩展信息
 * // type - 与报名表 fields type 一致
 * // id - 与报名表 fields id 关联
 * // name - 名称
 * // value - 填写的值或选择的选项列表，如果是图片或者签名则是图片的路径
 * @property array $extra 报名扩展信息
 * @property Carbon $latest_start_at 最近开课时间
 * @property Carbon $latest_enroll_at 最近报名时间
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Org $org 所属机构
 * @property-read EnrollmentForm $form 报名表单
 * @property-read Enrollment[] $enrollments
 */
class Student extends Model
{
    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected $table = 'org_students';

    protected $hidden = ['id', 'user_id', 'org_id'];

    protected $appends = ['sid', 'id_card_front_url', 'id_card_back_url', 'photo_url'];

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'user_id',
        'name',
        'phone',
        'work_unit',
        'id_card_front',
        'id_card_back',
        'id_card_number',
        'photo',
        'extra',
        'latest_start_at',
        'latest_enroll_at',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'user_id' => 'integer',
        'extra' => 'array',
        'latest_start_at' => 'datetime',
        'latest_enroll_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function getIdCardFrontUrlAttribute(): string
    {
        return $this->id_card_front ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->id_card_front) : '';
    }

    public function getIdCardBackUrlAttribute(): string
    {
        return $this->id_card_back ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->id_card_back) : '';
    }

    public function getPhotoUrlAttribute(): string
    {
        return $this->photo ? AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->photo) : '';
    }

    public function extra(): Attribute
    {
        return Attribute::make(
            get: function ($extra) {
                $extra = $extra ? json_decode($extra, true) : [];

                if (is_array($extra)) {
                    foreach ($extra as &$item) {
                        if (in_array($item['type'], EnrollmentForm::fileTypes())) {
                            if ($item['type'] == EnrollmentForm::TYPE_PHOTO) {
                                $item['value'] = AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $item['value']);
                            } else {
                                foreach ($item['value'] as &$value) {
                                    $value['path_url'] = AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $value['path']);
                                }
                            }
                        }
                    }
                } else {
                    $extra = [];
                }

                return $extra;
            }
        );
    }

    public static function addHashIdSalt()
    {
        return 'OrgStudent';
    }

    public function org()
    {
        return $this->belongsTo(Org::class, 'org_id', 'id');
    }

    public function form()
    {
        return $this->belongsTo(EnrollmentForm::class, 'org_id', 'org_id');
    }

    public function enrollments()
    {
        return $this->hasMany(Enrollment::class, 'student_id', 'id');
    }
}
