<?php

namespace App\Models\Org\Admin;

use Illuminate\Foundation\Auth\User as AuthUser;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

/**
 * @property int $id
 * @property int $org_id
 * @property string $username 用户名
 * @property string $password 密码
 * @property string $real_name 姓名
 * @property string $phone 手机号
 * @property string $email 邮箱
 * @property int $status 状态 0 正常，1 禁用
 * @property int $is_main 是否主账号
 * @property string $last_logged_at 最近登录时间
 * @property string $last_logged_ip 最近登录IP
 * @property string $last_active_at 最近活跃时间
 * @property string $last_active_ip 最近活跃IP
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property AdminRole[] $roles
 */
class Admin extends AuthUser implements JWTSubject
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_admins';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'org_id', 'username', 'password', 'real_name', 'phone', 'email', 'status', 'is_main', 'last_logged_at', 'last_logged_ip', 'last_active_at', 'last_active_ip', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'org_id' => 'integer', 'status' => 'integer', 'is_main' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['password'];

    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function roles()
    {
        return $this->hasMany(AdminRole::class, 'admin_id');
    }
}
