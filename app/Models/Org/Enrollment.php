<?php
namespace App\Models\Org;

use App\Core\Enums\BusinessType;
use App\Core\HashIdAttribute;
use App\Exceptions\ServiceException;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePack;
use App\Models\Order\Order;
use App\Models\Org;
use App\Models\User\UserOwnContent;
use App\Services\Common\AttachmentService;
use App\Services\Org\Export\HourCertExporter;
use Carbon\Carbon;
use App\Models\Train\Topic as TrainTopic;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @property int $id
 * @property string $sid
 * @property int $org_id 所属机构ID
 * @property int $user_id 用户ID
 * @property int $class_id 所在班级ID
 * @property int $student_id 学员id
 * @property string $type 学习类型:课程或题库
 * @property int $resource_id 课程/题库ID/课程包
 * @property Carbon $started_at 开课时间
 * @property Carbon $expired_at 到期时间
 * @property int $status 状态:0=待开课,1=学习中,2=已完成,3=已过期
 * @property int $learned_duration 已学学时（秒数）
 * @property bool $learn_finished 是否已学完
 * @property int $exam_taken 是否已考试
 * @property int $exam_score 考试最高成绩
 * @property bool $exam_passed 考试是否通过了
 * @property Carbon $exam_pass_at 考试通过时间
 * @property int $exam_retake 补考状态:0=不能补考,1=允许补考,2=已补考
 * @property int $exam_latest_id 最近一次考试 ID
 * @property int $exam_count 考试次数
 * @property string $hour_cert 学时证明图片
 * @property bool $archived 是否存档
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read string $number 学号
 * @property-read Org $org 所属机构
 * @property Student $student 学员信息
 * @property-read EnrollArchive $archive 学员归档信息
 * @property-read OrgClass $classroom 所属班级
 * @property-read EnrollmentForm $form 报名表单
 * @property ContentCourse|TrainTopic|ContentCoursePack $resource 资源
 * @property-read string $hour_cert_url 学时证明URL
 * @property-read string $hour_cert_image_url 学时证明图片URL
 * @property-read string $study_record_url 学习记录URL
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 */
class Enrollment extends Model
{
    use HashIdAttribute;

    /**
     * The table associated with the model.
     */
    protected $table = 'org_enrollments';

    /** @var string 课程类型 */
    public const TYPE_COURSE = 'course';
    /** @var string 题库类型 */
    public const TYPE_TOPIC = 'topic';
    /** @var string 课程包类型 */
    public const TYPE_COURSE_PACK = 'course_pack';

    /** @var array|string[] 关联资源 */
    public static array $resource = [
        self::TYPE_COURSE => ContentCourse::class,
        self::TYPE_TOPIC => TrainTopic::class,
        self::TYPE_COURSE_PACK => ContentCoursePack::class,
    ];

    /** @var array|string[] 类型字典 */
    public static array $typeDict = [
        self::TYPE_COURSE => '课程',
        self::TYPE_TOPIC => '题库',
        self::TYPE_COURSE_PACK => '课程包',
    ];

    /** @var array|string[] 状态字典 */
    public static array $statusDict = [
        self::STATUS_PENDING => '待开课',
        self::STATUS_LEARNING => '学习中',
        self::STATUS_COMPLETED => '已完成',
        self::STATUS_INCOMPLETE => '未完成',
        self::STATUS_EXPIRED => '已过期',
    ];

    /** @var int 待开课状态 */
    public const STATUS_PENDING = 0;
    /** @var int 学习中状态 */
    public const STATUS_LEARNING = 1;
    /** @var int 已完成状态 */
    public const STATUS_COMPLETED = 2;
    /** @var int 已过期状态 */
    public const STATUS_EXPIRED = 3;
    /** @var int 未完成状态 */
    public const STATUS_INCOMPLETE = 4;

    /** @var int 不能补考 */
    const  RETAKE_NOT = 0;
    /** @var int 可以补考 */
    const  RETAKE_CAN = 1;
    /** @var int 已经补考 */
    const  RETAKE_ALR = 2;

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = ['id', 'user_id', 'org_id', 'resource_id', 'class_id'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $appends = ['sid', 'number', 'amount', 'order_info'];

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'user_id',
        'class_id',
        'student_id',
        'type',
        'resource_id',
        'started_at',
        'expired_at',
        'status',
        'learned_duration',
        'learn_finished',
        'exam_taken',
        'exam_score',
        'exam_retake',
        'exam_passed',
        'exam_pass_at',
        'exam_latest_id',
        'exam_count',
        'hour_cert',
        'archived',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'user_id' => 'integer',
        'class_id' => 'integer',
        'student_id' => 'integer',
        'resource_id' => 'integer',
        'status' => 'integer',
        'learned_duration' => 'integer',
        'learn_finished' => 'boolean',
        'exam_taken' => 'boolean',
        'exam_score' => 'integer',
        'exam_retake' => 'integer',
        'exam_passed' => 'boolean',
        'exam_pass_at' => 'datetime',
        'exam_latest_id' => 'integer',
        'exam_count' => 'integer',
        'started_at' => 'datetime',
        'expired_at' => 'datetime',
        'archived' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取学员编号
     * @return string
     */
    public function getNumberAttribute(): string
    {
        return $this->id ? sprintf('%d%010d', $this->org_id, $this->id) : '';
    }

    /**
     * 获取学时证明 URL
     * @return string
     */
    public function getHourCertUrlAttribute(): string
    {
        $idCardNumber = $this->getIdCardNumber();
        return config('heguibao.pc_url') . '/cert/hour-cert/' . $this->sid
            . '?hash=' . md5("{$this->id},{$this->org_id},{$idCardNumber},{$this->sid},hour-cert");
    }

    /**
     * 获取订单金额
     * @return string
     */
    public function getAmountAttribute(): ?string
    {
        return $this->balanceRecord ? str_replace('-', '¥ ', $this->balanceRecord->amount) : '-';
    }

    /**
     * 获取学时证明图片 URL
     * @return string
     */
    public function getHourCertImageUrlAttribute(): string
    {
        if ($this->hour_cert) {
            return AttachmentService::url(Storage::disk(config('heguibao.storage.priv')), $this->hour_cert);
        } elseif ($this->exam_passed && $this->learn_finished) {
            //确保有学时证明图像，开始生成
            $exporter = new HourCertExporter($this->org_id, $this->id, []);
            $result = $exporter->handle();

            if ($result === null) {
                throw new ServiceException('无法获取学员的学时证明');
            }

            //exporter 处理后，表已经有学时证明的数据，此时刷新模型重新加载
            $this->refresh();
            //防止进入死循环

            if (!$this->hour_cert) {
                throw new ServiceException('获取学员的学时证明失败');
            }

            return $this->getHourCertImageUrlAttribute();
        } else {
            return '';
        }
    }

    /**
     * 学习记录 URL
     * @return string
     */
    public function getStudyRecordUrlAttribute(): string
    {
        $idCardNumber = $this->getIdCardNumber();
        return config('heguibao.pc_url').'/cert/study-record/'.$this->sid.'?hash='.md5("$this->id,$this->org_id,{$idCardNumber},$this->sid,study-record");
    }

    public function getOrderInfoAttribute()
    {
        $order = Order::query()
            ->where('user_id', $this->user_id)
            ->where('status', Order::STATUS_PAID)
            ->where('business_id', $this->resource_id)
            ->whereJsonContains('extend->org_id', $this->org_id)
            ->select(['payment_amount', 'payment_at', 'business_type', 'extend', 'created_at'])
            ->first();
        if (!$order) {
            return [];
        }
        $isTopic = $order->business_type == BusinessType::Topic;
        if ($isTopic) {
            return [
                'amount' => '¥ '.$order->payment_amount,
                'payment_at' => $order->payment_at,
                'expired_at' => Carbon::parse($order->payment_at)->addDays($order->extend['valid_days'])->toDateTimeString()
            ];
        } else {
            return [
                'amount' => '¥ '.$order->payment_amount,
                'payment_at' => $order->payment_at,
                'expired_at' => Carbon::parse($order->created_at)->addMonths(UserOwnContent::ORG_EXPIRED_MOUTH_DEFAULT)->toDateTimeString()
            ];
        }
    }

    public function getIdCardNumber()
    {
        if (isset($this->student)) {
            $idCardNumber = $this->student->id_card_number;
        } else {
            $idCardNumber = $this->getStudent()->id_card_number;
        }

        return $idCardNumber;
    }

    public static function addHashIdSalt()
    {
        return 'OrgEnrollment';
    }

    /**
     * 学习记录状态
     *
     * @return int[]
     */
    public static function studyStatuses()
    {
        return [self::STATUS_LEARNING, self::STATUS_INCOMPLETE, self::STATUS_COMPLETED];
    }

    public function org()
    {
        return $this->belongsTo(Org::class, 'org_id', 'id');
    }

    public function classroom()
    {
        return $this->hasOne(OrgClass::class, 'id', 'class_id');
    }

    public function form()
    {
        return $this->belongsTo(EnrollmentForm::class, 'org_id', 'org_id');
    }

    public function resource()
    {
        return $this->morphTo('resource', 'type', 'resource_id');
    }

    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    public function archive()
    {
        return $this->hasOne(EnrollArchive::class, 'enroll_id', 'id');
    }

    public function balanceRecord()
    {
        return $this->hasOne(BalanceRecord::class, 'enroll_id')
            ->where('org_id', $this->org_id)
            ->where('status', BalanceRecord::STATUS_NORMAL)
            ->where('type', BalanceRecord::TYPE_EXPENSE)
            ->latest('created_at');
    }


    public function scopePublicFields($query)
    {
        return $query->select(['id', 'user_id', 'org_id', 'resource_id', 'class_id', 'type']);
    }

    /**
     * 获取资源名称（课程或题库名称）
     *
     * 注意每次调用此方法都会查询数据库，尽量不要在循环中使用
     *
     * @return string
     */
    public function getResourceName()
    {
        return $this->type == self::TYPE_TOPIC
            ? Topic::query()->where('id', $this->resource_id)->select(['name'])->value('name')
            : Content::withTrashed()->where('id', $this->resource_id)->select(['title'])->value('title');  // 课程和课程包都是 Content 中的 name
    }

    /**
     * 获取学员信息
     *
     * 注意每次调用此方法都会查询数据库，尽量不要在循环中使用
     *
     * @return \Illuminate\Database\Eloquent\Builder|Model|object|null|EnrollArchive|Student
     */
    public function getStudent()
    {
        return $this->archived
            ? EnrollArchive::query()->where('enroll_id', $this->id)->first()
            : Student::query()->where('id', $this->student_id)->first();
    }

    /**
     * 获取（类型名称）资源名称（课程或题库名称）
     *
     * @return string
     */
    public function getTypeResourceName(): string
    {
        $typeDict = self::$typeDict;
        $resourceName = '-';

        if ($this->resource) {
            if ($this->type == Enrollment::TYPE_COURSE && $this->resource->content) {
                $resourceName = $this->resource->content->title;
            } else {
                $resourceName = $this->resource->name;
            }

            $resourceName = "（{$typeDict[$this->type]}）$resourceName";
        }

        return $resourceName;
    }

    public function toResource()
    {
        if ($this->resource && in_array($this->type, [Enrollment::TYPE_COURSE, Enrollment::TYPE_COURSE_PACK])) {
            $content = $this->resource->content;

            $content && $this->resource->name = $content->title;
        }

        return $this->resource;
    }

    public function toStudent(): Student
    {
        $student = $this->student;

        // 报名信息完成，学员信息改成存档信息
        if ($this->status == Enrollment::STATUS_COMPLETED && $this->archive) {
            $student->name = $this->archive->name;
            $student->phone = $this->archive->phone;
            $student->photo = $this->archive->photo;
            $student->id_card_number = $this->archive->id_card_number;
            $student->id_card_front = $this->archive->id_card_front;
            $student->id_card_back = $this->archive->id_card_back;
            $student->extra = $this->archive->extra;
        }

        return $student;
    }

}
