<?php

namespace App\Models\Org;

use App\Models\Org\Admin\Admin;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 机构报名操作记录模型
 *
 * @property int $id
 * @property int $enroll_id 报名ID
 * @property int $admin_id 操作人ID，用户提交为0
 * @property int $type 操作类型 1 信息提交，2 信息审核，3 缴费，4 申请退款，5 退款审核， 6 退款成功， 7 取消退款
 * @property string $remark 审核备注
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class EnrollOperateRecord extends Model
{
    protected $table = 'org_enroll_operate_records';

    // 操作类型常量
    const TYPE_SUBMIT = 1;              // 信息提交
    const TYPE_REVIEW = 2;              // 信息审核
    const TYPE_PAYMENT = 3;             // 缴费
    const TYPE_APPLY_REFUND = 4;        // 申请退款
    const TYPE_REFUND_REVIEW = 5;       // 退款审核
    const TYPE_REFUND_SUCCESS = 6;      // 退款成功
    const TYPE_CANCEL_REFUND = 7;       // 取消退款

    protected $fillable = [
        'enroll_id',
        'admin_id',
        'type',
        'remark',
    ];

    protected $casts = [
        'enroll_id' => 'integer',
        'admin_id' => 'integer',
        'type' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $appends = ['type_label'];
    /**
     * 关联报名记录
     */
    public function enroll(): BelongsTo
    {
        return $this->belongsTo(Enroll::class, 'enroll_id', 'id');
    }

    /**
     * 关联操作人（机构管理员）
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'admin_id', 'id');
    }

    /**
     * 操作类型标签
     */
    public static function getTypeLabels(): array
    {
        return [
            self::TYPE_SUBMIT => '信息提交',
            self::TYPE_REVIEW => '信息审核',
            self::TYPE_PAYMENT => '缴费',
            self::TYPE_APPLY_REFUND => '申请退款',
            self::TYPE_REFUND_REVIEW => '退款审核',
            self::TYPE_REFUND_SUCCESS => '退款成功',
            self::TYPE_CANCEL_REFUND => '取消退款',
        ];
    }

    /**
     * 获取操作类型标签
     */
    public function typeLabel(): Attribute
    {
        return Attribute::make(
            get: fn() => self::getTypeLabels()[$this->type] ?? '未知操作',
        );
    }

    /**
     * 是否为用户操作
     */
    public function isUserOperation(): bool
    {
        return $this->admin_id === 0;
    }

    /**
     * 获取操作人名称
     */
    public function operatorName(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->isUserOperation() ? '用户' : $this->admin->name ?? '未知管理员',
        );
    }
}
