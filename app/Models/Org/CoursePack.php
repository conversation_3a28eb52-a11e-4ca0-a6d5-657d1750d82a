<?php
namespace App\Models\Org;

use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCoursePack;
use App\Models\Cms\ContentCoursePackList;
use App\Services\Org\TopicService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property int $course_pack_id 课程包ID
 * @property int $status 状态:0=隐藏,1=显示
 * @property float $price_original 原价
 * @property float $price_sell 售价
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 * @property ContentCoursePack $contentCoursePack
 */
class CoursePack extends Model
{
    /** @var int 隐藏状态 */
    public const STATUS_HIDDEN = 0;
    /** @var int 显示状态 */
    public const STATUS_VISIBLE = 1;

    /**
     * The table associated with the model.
     */
    protected $table = 'org_course_packs';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'course_pack_id',
        'status',
        'price_original',
        'price_sell',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'course_pack_id' => 'integer',
        'status' => 'integer',
        'price_original' => 'float',
        'price_sell' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];


    protected static function booted()
    {
        // 创建课程时自动创建章节
        static::created(function (CoursePack $coursePack) {
            /** @var ContentCoursePack $contentCoursePack */
            $contentCoursePack = $coursePack->contentCoursePack;

            // 添加课程题库
            if ($contentCoursePack->topic_id > 0 && $contentCoursePack->topic) {
                TopicService::checkCreate($coursePack->org_id, $contentCoursePack->topic);
            }
        });
    }

    public function contentCoursePack()
    {
        return $this->belongsTo(ContentCoursePack::class, 'course_pack_id', 'content_id');
    }

    /**
     * 获取课程包的key是课程ID的章节列表
     *
     * 结构：
     * Collection<课程ID, Collection<ContentCourseChapter>>
     *
     * 获取某个课程的章节列表
     *      return->get($courseId);
     *
     * @param int $orgId
     * @param int $coursePackId
     * @param bool $wrapSn
     * @return Collection<int, Collection<ContentCourseChapter>>
     */
    public static function getCoursesResourceTree(int $orgId, int $coursePackId, bool $wrapSn = false)
    {
        $courseIds = ContentCoursePackList::query()
            ->where('content_id', $coursePackId)
            ->orderBy('order')
            ->pluck('course_id')
            ->toArray();

        // 查询哪些课程当前机构有配置
        $subChapterSections = CourseSub::getCoursesResourceTree($orgId, $courseIds, $wrapSn);
        $subCourseIds = $subChapterSections->keys()->toArray();

        // 查询课程章节
        $otherCourseIds = array_diff($courseIds, $subCourseIds);
        $otherChapterSections = collect();

        if ($otherCourseIds) {
            $otherChapterSections = ContentCourseChapter::getCoursesTree($otherCourseIds, $wrapSn);
        }

        $chapterSections = collect();

        // 根据课程id排序
        foreach ($courseIds as $courseId) {
            if ($subChapterSections->has($courseId)) {
                $chapterSections->put($courseId, $subChapterSections->get($courseId));
            } elseif ($otherChapterSections->has($courseId)) {
                $chapterSections->put($courseId, $otherChapterSections->get($courseId));
            }
        }

        return $chapterSections;
    }
}
