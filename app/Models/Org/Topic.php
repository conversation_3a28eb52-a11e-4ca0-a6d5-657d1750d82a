<?php
namespace App\Models\Org;

use App\Models\Train\Topic as TrainTopic;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 *
 *
 * @property int $id
 * @property int $org_id 机构ID
 * @property int $topic_id 题库ID
 * @property int $status 状态:0=隐藏,1=显示
 * @property int $exam_time 考试时间
 * @property int $pass_score 及格分数
 * @property array $exam_config 考试配置
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read \App\Models\Train\Topic $topic
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @property string $price_original_30 原价30天
 * @property string $price_sell_30 售价30天
 * @property string $price_original_60 原价30天
 * @property string $price_sell_60 售价60天
 * @property-read TrainTopic|null $trainTopic
 * @method static \Illuminate\Database\Eloquent\Builder|Topic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Topic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Topic query()
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereExamConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereExamTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereOrgId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic wherePassScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic wherePriceOriginal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic wherePriceOriginal30($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic wherePriceOriginal60($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic wherePriceSell($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic wherePriceSell30($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic wherePriceSell60($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereTopicId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Topic whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Topic extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_topics';

    /** @var int 隐藏状态 */
    public const STATUS_HIDDEN = 0;
    /** @var int 显示状态 */
    public const STATUS_VISIBLE = 1;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'org_id',
        'topic_id',
        'status',
        'price_original_30',
        'price_sell_30',
        'price_original_60',
        'price_sell_60',
        'exam_time',
        'pass_score',
        'exam_config',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'topic_id' => 'integer',
        'status' => 'integer',
        'exam_time' => 'integer',
        'pass_score' => 'integer',
        'exam_config' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'org_id', 'topic_id']);
    }

    public function enrollment()
    {
        return $this->morphOne(Enrollment::class, 'recourse');
    }

    public function topic()
    {
        return $this->belongsTo(\App\Models\Train\Topic::class, 'topic_id', 'id');
    }

    /**
     * 关联训练题库
     */
    public function trainTopic(): BelongsTo
    {
        return $this->belongsTo(TrainTopic::class, 'topic_id', 'id');
    }

    /**
     * @return array
     */
    public static function getExamConfig()
    {
        return [
            "judge" => [
                "count" => 0,
                "score" => 0
            ],
            "single_choice" => [
                "count" => 60,
                "score" => 1
            ],
            "multiple_choice" => [
                "count" => 20,
                "score" => 2
            ]
        ];

    }
}
