<?php
namespace App\Models\Org;

use App\Models\Attachment\AttachmentFile;
use App\Models\Org;
use App\Models\Org\Admin\Admin;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property string $type 导出类型
 * @property string $desc 描述
 * @property int $admin_id 操作人ID
 * @property int $status 状态:0=进行中,1=已完成
 * @property string $file 导出文件路径
 * @property string $error 错误信息
 * @property array $extra 导出参数
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read string $type_name
 * @property-read Admin $admin
 * @property-read AttachmentFile $attachment
 *
 * @property Org $org
 */
class Export extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'org_exports';

    /** @var string 导出类型-学时证明 */
    const TYPE_HOUR_CERT = 'hour_cert';
    /** @var string 导出类型-一期一档 */
    const TYPE_STUDENT_ARCHIVE = 'student_archive';
    /** @var string 导出类型-学习记录 */
    const TYPE_HOUR_RECORD = 'hour_record';
    /** @var string 导出类型-考试试卷 */
    const TYPE_TEST_PAPER = 'test_paper';
    /** @var string 导出类型-机构学员 */
    const TYPE_ORG_ENROLLMENT = 'org_enrollment';
    /** @var string 导出类型-报名表单 */
    const TYPE_ORG_ENROLLMENT_FORM = 'org_enrollment_form';
    /** @var string 一键打包下载 */
    const TYPE_ORG_DOWNLOAD_PACK = 'org_download_pack';

    /** @var int 进行中状态 */
    const STATUS_IN_PROGRESS = 0;
    /** @var int 已完成状态 */
    const STATUS_COMPLETED = 1;
    /** @var int 失败状态 */
    const STATUS_FAILED = 2;
    /** @var int 部分完成状态 */
    const STATUS_PARTLY_COMPLETED = 3;


    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'org_id',
        'type',
        'desc',
        'admin_id',
        'status',
        'file',
        'error',
        'extra',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'admin_id' => 'integer',
        'status' => 'integer',
        'extra' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function getTypeNameAttribute(): string
    {
        return self::getTypeName($this->type);
    }

    public static function getTypeName(string $type): string
    {
        return match ($type) {
            self::TYPE_HOUR_CERT => '学时证明',
            self::TYPE_STUDENT_ARCHIVE => '一期一档',
            self::TYPE_HOUR_RECORD => '学习记录',
            self::TYPE_TEST_PAPER => '考试试卷',
            self::TYPE_ORG_ENROLLMENT => '机构学员',
            default => '未知',
        };
    }

    public function org()
    {
        return $this->hasOne(Org::class, 'id', 'org_id');
    }

    public function admin()
    {
        return $this->hasOne(Admin::class, 'id', 'admin_id');
    }

    public function attachment()
    {
        return $this->hasOne(AttachmentFile::class, 'path', 'file');
    }
}
