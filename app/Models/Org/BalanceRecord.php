<?php
namespace App\Models\Org;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $org_id 机构ID
 * @property string $type 消费类型
 * @property string $origin_balance 原始金额
 * @property string $amount 变动金额
 * @property int $enroll_id 学员ID
 * @property string $remark 描述
 * @property int $status 状态
 * @property int $ref_id 关联ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
class BalanceRecord extends Model
{

    /**
     * The table associated with the model.
     */
    protected $table = 'org_balance_records';


    /** @var string 支出类型 */
    public const TYPE_EXPENSE = 'expense';

    /** @var string 收入类型 */
    public const TYPE_INCOME = 'income';

    /** @var string 退款类型 */
    public const TYPE_REFUND = 'refund';

    public const STATUS_NORMAL = 1;
    public const STATUS_REFUND = 2;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'org_id',
        'type',
        'origin_balance',
        'amount',
        'enroll_id',
        'remark',
        'status',
        'ref_id',
        'created_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'org_id' => 'integer',
        'enroll_id' => 'integer',
        'ref_id' => 'integer',
        'status' => 'integer',
        'created_at' => 'datetime'
    ];

    public function enrollment()
    {
        return $this->belongsTo(Enrollment::class, 'enroll_id');
    }
}
