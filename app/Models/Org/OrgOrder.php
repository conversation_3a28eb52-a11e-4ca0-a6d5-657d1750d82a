<?php

namespace App\Models\Org;

use Illuminate\Database\Eloquent\Model;

class OrgOrder extends Model
{
    /** @var int 线上支付 */
    const PAYMENT_CHANNEL_ONLINE = 1;
    /** @var int 线下支付 */
    const PAYMENT_CHANNEL_OFFLINE = 2;

    /** @var int 待支付 */
    const STATUS_UNPAID = 0;

    /** @var int 已支付 */
    const STATUS_PAID = 1;

    /** @var int 已退款 */
    const STATUS_REFUNDED = 2;


    protected $table = 'org_orders';

    protected $fillable = [
        'org_id',
        'user_id',
        'student_id',
        'source_id',
        'source_table',
        'business_type',
        'name',
        'phone',
        'title',
        'order_no',
        'transaction_no',
        'total_amount',
        'payment_amount',
        'payment_channel',
        'status',
        'payment_at',
        'expired_at'
    ];
}
