<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $referral_id 推荐人
 * @property int $invitee_id 被邀请人
 * @property int $status 状态  0 已邀请，1 已奖励
 * @property int $credit 赠送的积分
 * @property string $send_at 积分发放时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Invitation extends Model
{

    const STATUS_INVITED = 1;
    const STATUS_REWARDED = 2;

    /**
     * The table associated with the model.
     */
    protected $table = 'invitations';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'referral_id', 'invitee_id', 'status', 'credit', 'send_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'referral_id' => 'integer', 'invitee_id' => 'integer', 'status' => 'integer', 'credit' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['referral_id', 'invitee_id'];

    public function referral()
    {
        return $this->belongsTo(User::class, 'referral_id', 'id');
    }

    public function invitee()
    {
        return $this->belongsTo(User::class, 'invitee_id', 'id');
    }

    public function scopePublicFields($query)
    {
        $query->select(['id', 'referral_id', 'invitee_id', 'status', 'credit', 'send_at']);
    }
}
