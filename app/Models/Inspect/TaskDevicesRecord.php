<?php

namespace App\Models\Inspect;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TaskDevicesRecord extends Model
{
    // 待巡检状态
    const WAIT_INSPECT = 0;
    // 已巡检状态
    const INSPECTED = 1;
    // 待审批状态
    const PENDING_APPROVAL = 2;
    // 已审批状态
    const APPROVED = 3;

    // 审批状态:0=暂不处理,1=已处理
    const APPROVE_WAIT = 0;
    const APPROVE_DONE = 1;

    // 已巡检的状态集
    const INSPECTED_STATUS = [
        self::INSPECTED,
        self::PENDING_APPROVAL,
        self::APPROVED,
    ];

    // 任务设备记录表
    protected $table = 'inspect_task_devices_records';

    protected $fillable = [
        'task_id',
        'device_id',
        'checked_at',
        'image_url',
        'status',
        'abnormal_count',
        'question',
        'suggestion',
        'approver_id',
        'approver_status',
        'approver_at',
    ];

    protected $casts = [
        'image_url' => 'array',
        'checked_at' => 'datetime',
        'approver_at' => 'datetime',
    ];

    public function task(): BelongsTo
    {
        return $this->belongsTo(Task::class, 'task_id');
    }

    public function device(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'device_id')->withTrashed();
    }

    public function deviceItem(): HasMany
    {
        return $this->hasMany(DeviceItem::class, 'device_id', 'device_id');//->withTrashed();
    }

    public function deviceItemsRecord()
    {
        return $this->hasMany(DeviceItemsRecord::class, 'task_devices_record_id');
    }
}

