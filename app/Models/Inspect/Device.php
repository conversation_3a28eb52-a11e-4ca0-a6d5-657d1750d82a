<?php

namespace App\Models\Inspect;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Device extends Model
{
    use SoftDeletes;
    // 设备表
    protected $table = 'inspect_devices';

    protected $fillable = [
        'org_id',
        'user_id',
        'name',
        'inspection_item_count',
        'image_url',
        'remark',
    ];

    /**
     * 设备巡检项
     * @return HasMany
     */
    public function items(): HasMany
    {
        return $this->hasMany(DeviceItem::class, 'device_id')->withTrashed();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
