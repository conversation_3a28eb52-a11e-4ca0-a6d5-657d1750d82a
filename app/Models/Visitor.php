<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property string $token 设备token
 * @property \Carbon\Carbon $last_active_at 最后活跃时间
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 *
 * @mixed \Illuminate\Database\Eloquent
 */
class Visitor extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'visitors';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'token', 'last_active_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'last_active_at'=>'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

}
