<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * SmsRecord
 *
 * @property int $id
 * @property string $phone 手机号
 * @property string $type 类型
 * @property string $ip ip
 * @property int $platform 平台
 * @property string $content 内容
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class SmsRecord extends Model
{
    use HasFactory;

    /** @var int 华为 */
    const PLATFORM_HUAWEI = 1;

    /** @var int 腾讯 */
    const PLATFORM_TENCENT = 2;

    /**
     * The table associated with the model.
     */
    protected $table = 'sms_records';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'phone', 'type', 'ip', 'platform', 'content', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'platform' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
}
