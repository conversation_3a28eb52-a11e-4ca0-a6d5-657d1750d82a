<?php

namespace App\Models;

use App\Core\HashIdAttribute;
use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Models\Org\Admin\Admin;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * 组织机构
 *
 * @property int $id
 * @property string $name 机构名称
 * @property string $alias 别名简称
 * @property string $domain URL中自定义ID
 * @property string $logo logo图路径
 * @property string $verified_status 认证状态
 * @property int $total_students 学员总数
 * @property int $need_photo 是否需要证件照
 * @property int $total_classes 开班总数
 * @property int $total_trained 培训总数
 * @property float $balance 余额
 * @property string $business_license 营业执照
 * @property string|null $business_scope 业务范围
 * @property string $contact 联系方式
 * @property string $service_qrcode 客服二维码图片
 * @property string $official_seal_image 机构公章图片
 * @property int $area_code 所在地区码
 * @property string $area_text 所在地区名字
 * @property \Carbon\Carbon|null $verified_at 认证时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @property Admin $mainAdmin
 *
 * @method \Illuminate\Database\Eloquent\Builder|self publicFields()
 * @mixin \Eloquent
 */
class Org extends Model
{
    use HasFactory;
    use HashIdAttribute;

    protected $table = 'orgs';

    /** @var string 待认证 */
    const VERIFIED_STATUS_PENDING = 'pending';
    /** @var string 待认证 */
    const VERIFIED_STATUS_VERIFIED = 'verified';
    /** @var string 待认证 */
    const VERIFIED_STATUS_REJECTED = 'rejected';

    protected $fillable = [
        'name',
        'alias',
        'domain',
        'need_photo',
        'logo',
        'verified_status',
        'total_students',
        'total_classes',
        'total_trained',
        'balance',
        'business_license',
        'business_scope',
        'contact',
        'service_qrcode',
        'official_seal_image',
        'area_code',
        'area_text'
    ];

    protected $casts = [
        'total_students' => 'int',
        'total_classes' => 'int',
        'total_trained' => 'int',
        'balance' => 'float',
        'area_code' => 'int',
        'verified_at' => 'datetime'
    ];

    protected $hidden = ['id', 'created_at', 'updated_at'];

    protected $appends = ['sid', 'logo_url', 'business_license_url', 'service_qrcode_url', 'official_seal_image_url'];

    public static function addHashIdSalt()
    {
        return 'Organization';
    }

    public function scopePublicFields($query)
    {
        return $query->select(['id', 'name', 'alias', 'need_photo', 'logo', 'verified_status', 'service_qrcode']);
    }

    /**
     * 主管理员
     */
    public function mainAdmin(): HasOne
    {
        return $this->hasOne(Admin::class, 'org_id', 'id')->where('is_main', 1);
    }

    public function areaText(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                return $value ? json_decode($value, true) : [];
            },
            set: function ($value) {
                return json_encode($value, JSON_UNESCAPED_UNICODE);
            }
        );
    }

    public function getLogoUrlAttribute(): string
    {
        return $this->logo ? AttachmentService::url(Storage::disk(), $this->logo) : '';
    }

    public function getBusinessLicenseUrlAttribute(): string
    {
        return $this->business_license ? AttachmentService::url(Storage::disk(), $this->business_license) : '';
    }

    public function getServiceQrcodeUrlAttribute(): string
    {
        return $this->service_qrcode ? AttachmentService::url(Storage::disk(), $this->service_qrcode) : '';
    }

    public function getOfficialSealImageUrlAttribute(): string
    {
        return $this->official_seal_image ? AttachmentService::url(Storage::disk(), $this->official_seal_image) : '';
    }
}
