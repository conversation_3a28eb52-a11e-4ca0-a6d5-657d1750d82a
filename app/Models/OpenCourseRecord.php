<?php

namespace App\Models;

use App\Models\Cms\Content;
use App\Models\Train\Topic;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 开课记录
 *
 * @property int $id
 * @property int $batch_id 批次ID
 * @property int $user_id 用户ID
 * @property int $org_id 机构ID
 * @property array $course_ids 课程ID
 * @property array $topic_ids 题库ID
 * @property array $course_pack_ids 题库ID
 * @property \Illuminate\Support\Carbon $created_at 创建时间
 *
 * @property-read User $user
 * @property-read Org $org
 *
 * @mixin \Eloquent
 */
class OpenCourseRecord extends Model
{
    use HasFactory;

    const UPDATED_AT = null;

    protected $table = 'open_course_records';

    protected $fillable = ['id', 'batch_id', 'user_id', 'org_id', 'course_ids', 'topic_ids', 'course_pack_ids', 'created_at'];

    protected $casts = ['batch_id' => 'integer', 'user_id' => 'integer', 'org_id' => 'integer', 'course_ids' => 'array', 'topic_ids' => 'array', 'course_pack_ids' => 'array', 'created_at' => 'datetime'];

    public function courses()
    {
        return Content::whereIn('id', $this->course_ids ?: []);
    }

    public function topics()
    {
        return Topic::whereIn('id', $this->topic_ids ?: []);
    }

    public function packs()
    {
        return Content::query()->whereIn('id', $this->course_pack_ids ?: [])->where("type", Content::TYPE_COURSE_PACK);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function org()
    {
        return $this->belongsTo(Org::class);
    }
}
