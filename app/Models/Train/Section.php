<?php

namespace App\Models\Train;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $topic_id
 * @property int $chapter_id
 * @property string $name 节名称
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property Topic $topic
 * @property Chapter $chapter
 * @property Example $example
 */
class Section extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'train_sections';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'topic_id', 'chapter_id', 'name', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'topic_id' => 'integer', 'chapter_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    public function topic()
    {
        return $this->belongsTo(Topic::class, 'topic_id', 'id');
    }

    public function chapter()
    {
        return $this->belongsTo(Chapter::class, 'chapter_id', 'id');
    }

    public function example()
    {
        return $this->hasOne(Example::class, 'section_id', 'id');
    }

    public function subjects()
    {
        return $this->hasMany(Subject::class, 'section_id', 'id');
    }
}
