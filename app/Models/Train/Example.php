<?php

namespace App\Models\Train;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $topic_id
 * @property int $chapter_id
 * @property int $section_id
 * @property string $content
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Example extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'train_examples';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'topic_id', 'chapter_id', 'section_id', 'content', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'topic_id' => 'integer', 'chapter_id' => 'integer', 'section_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
}
