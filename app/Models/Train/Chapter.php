<?php

namespace App\Models\Train;

use App\Core\HashIdAttribute;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $topic_id
 * @property string $name 章名称
 * @property int $example 案例章 0 否，1 是，代表下面节都是案例
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Chapter extends Model
{

    use HashIdAttribute;

    protected $hidden = [
        'id',
    ];

    protected static function addHashIdSalt()
    {
        return "TRAIN-chapter";
    }
    /**
     * The table associated with the model.
     */
    protected $table = 'train_chapters';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'topic_id', 'name', 'example', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'topic_id' => 'integer', 'example' => 'integer',  'created_at' => 'datetime', 'updated_at' => 'datetime'];

    public function topic()
    {
        return $this->belongsTo(Topic::class, 'topic_id', 'id');
    }

    public function subjects()
    {
        return $this->hasMany(Subject::class, 'chapter_id', 'id');
    }

    public function sections()
    {
        return $this->hasMany(Section::class, 'chapter_id', 'id');
    }

    public function test()
    {
        return $this->hasMany(Test::class, 'chapter_id', 'id');
    }
}
