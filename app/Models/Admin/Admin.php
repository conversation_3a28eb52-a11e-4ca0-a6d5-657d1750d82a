<?php

namespace App\Models\Admin;

use App\Models\Stat\DailyPromoter;
use App\Services\Stat\DailyPromoterService;
use Illuminate\Foundation\Auth\User as AuthUser;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * @property int $id
 * @property string $username 用户名
 * @property string $password 密码
 * @property string $real_name 姓名
 * @property string $phone 手机号
 * @property string $email 邮箱
 * @property int $status 状态 0 正常，1 禁用
 * @property int $is_admin 是否超管 0 否，1 是
 * @property string $last_logged_at 最近登录时间
 * @property string $last_logged_ip 最近登录IP
 * @property string $last_active_at 最近活跃时间
 * @property string $last_active_ip 最近活跃IP
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property UserRole[] $roles
 */
class Admin extends AuthUser implements JWTSubject
{
    /**
     * The table associated with the model.
     */
    protected $table = 'admins';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'username', 'password', 'real_name', 'phone', 'email', 'status', 'is_admin', 'last_logged_at', 'last_logged_ip', 'last_active_at', 'last_active_ip', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'status' => 'integer', 'is_admin' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    protected $hidden = ['password'];

    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function roles()
    {
        return $this->hasMany(UserRole::class, 'admin_id');
    }

    public function stats()
    {
        $select = array_merge(['admin_id'], DailyPromoterService::toSumSelects());

        return $this
            ->hasOne(DailyPromoter::class, 'admin_id', 'id')
            ->selectRaw(implode(',', $select))
            ->groupBy('admin_id');
    }
}
