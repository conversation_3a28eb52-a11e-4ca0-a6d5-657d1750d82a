<?php

namespace App\Models\Admin;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $name 角色名称
 * @property string $code 角色标识
 * @property string $desc 角色描述
 * @property array $permissions 权限
 * @property int $system 系统 0:否 1:是
 * @property int $status 状态 0:禁用 1:启用
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Role extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'admin_roles';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'name', 'code', 'desc', 'permissions', 'system', 'status', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'system' => 'integer', 'status' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * 管理编辑员，可以查看所有的发布内容
     */
    const PERMISSION_MANAGE_EDITOR = 'manage_editor';
    /**
     * 管理推广员，可以查看所有的数据统计
     */
    const PERMISSION_MANAGE_PROMOTER = 'manage_promoter';

    /**
     * 企业服务订单管理员，可以查看所有的服务工单
     */
    const PERMISSION_ERS_ORDER_MANAGER = 'manage_ers_order';

    public function menus()
    {
        return $this->hasMany(RoleMenu::class, 'role_id');
    }

    public function admins()
    {
        return $this->hasMany(UserRole::class, 'role_id', 'id');
    }

    protected function permissions(): Attribute
    {
        return Attribute::make(
            get: function ($value) {
                return $value ? json_decode($value, true) : [];
            },
            set: function ($value) {
                return $value ? json_encode($value) : '';
            },
        );
    }
}
