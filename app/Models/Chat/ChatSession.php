<?php

namespace App\Models\Chat;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

/**
 * 问答会话
 *
 * @property int $id
 * @property int $user_id
 * @property string $model 模型
 * @property string $title 标题
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 */
class ChatSession extends Model
{
    // 文心一言 4.0
    const MODEL_ERNIE_BOT_4 = 'ERNIE_Bot_4';

    protected $table = 'chat_sessions';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'user_id', 'model', 'title', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = ['id' => 'integer', 'user_id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];


    protected $hidden = ['user_id'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function messages()
    {
        return $this->hasMany(ChatMessage::class, 'session_id', 'id');
    }
}
