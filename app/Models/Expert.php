<?php

namespace App\Models;

use App\Services\Common\AttachmentService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * 专家
 *
 * @property int $id
 * @property int $user_id
 * @property string $name 姓名
 * @property int $gender 性别 1:男 2:女
 * @property string $phone 联系电话
 * @property string $residence 常住地
 * @property string $major 毕业所学专业
 * @property string $safety_work_experience 安全从业经历
 * @property string $photo 个人照片
 * @property string $education 学历
 * @property string $occupation 职务
 * @property string $industry 从事行业
 * @property int $work_year 工作年限
 * @property array $fields 擅长领域
 * @property array $services 安全服务方向
 * @property array $certs 资格证书
 * @property array $scene_photos 现场照片
 * @property string $course_scopes 授课范围
 * @property string $typical_cases 典型案例
 * @property string $serve_customers 服务客户
 * @property string $teaching_styles 教学风格
 * @property string $remark 备注信息
 * @property string $reason 拒绝原因
 * @property int $status 状态
 * @property int $is_visible 是否可见
 * @property int $sort 排序
 * @property string|null $pass_at 通过时间
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @mixin \Eloquent
 */
class Expert extends Model
{
    use HasFactory;

    protected $table = 'experts';

    protected $fillable = ['id', 'user_id', 'name', 'gender', 'phone', 'residence', 'major', 'safety_work_experience',
        'photo', 'education', 'occupation', 'industry', 'work_year', 'fields', 'services', 'certs', 'scene_photos',
        'course_scopes', 'typical_cases', 'serve_customers', 'teaching_styles', 'remark', 'extra_text', 'reason', 'status', 'is_visible', 'sort', 'pass_at',
        'created_at', 'updated_at'];

    protected $casts = [
        'user_id' => 'integer', 'gender' => 'integer', 'work_year' => 'integer', 'status' => 'integer', 'is_visible' => 'integer',
        'sort' => 'integer', 'services' => 'array', 'fields' => 'array', 'certs' => 'array', 'scene_photos' => 'array',
        'pass_at' => 'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'
    ];

    protected $hidden = ['user_id', 'created_at', 'updated_at'];

    protected $appends = ['photo_url', 'cert_urls', 'scene_photo_urls'];

    /** @var int 待审核 */
    const STATUS_PENDING = 0;

    /** @var int 已通过 */
    const STATUS_NORMAL = 1;

    /** @var int 已拒绝 */
    const STATUS_REJECT = 2;

    /** @var int 可见 */
    const VISIBLE_YES = 1;

    /** @var int 不可见 */
    const VISIBLE_NO = 0;

    protected static function booted()
    {
        static::saving(function ($expert) {
            $fields = is_array($expert->fields) ? implode(' ', $expert->fields) : '';
            $services = is_array($expert->services) ? implode(' ', $expert->services) : '';
            $workYear = $expert->work_year ? $expert->work_year . '年' : '';

            $expert->extra_text = implode(' ', array_filter([$workYear, $fields, $services]));
        });
    }


    public function getPhotoUrlAttribute(): string
    {
        return AttachmentService::url(Storage::disk(config('heguibao.storage.pub')), $this->photo);
    }

    public function getCertUrlsAttribute(): array
    {
        $urls = [];
        foreach ($this->certs as $value) {
            $urls[] = AttachmentService::url(Storage::disk(config('heguibao.storage.pub')), $value['path']);
        }

        return $urls;
    }

    public function getScenePhotoUrlsAttribute(): array
    {
        $urls = [];
        foreach ($this->scene_photos as $value) {
            $urls[] = AttachmentService::url(Storage::disk(config('heguibao.storage.pub')), $value['path']);
        }

        return $urls;
    }

    public function getCertsAttribute($value): array
    {
        $files = json_decode($value, true);
        foreach ($files as &$file) {
            if (isset($file['path'])) {
                $file['path_src'] = AttachmentService::url(Storage::disk(config('heguibao.storage.pub')), $file['path']);
            }
        }

        return $files;
    }

    public function getScenePhotosAttribute($value): array
    {
        $files = json_decode($value, true);
        foreach ($files as &$file) {
            if (isset($file['path'])) {
                $file['path_src'] = AttachmentService::url(Storage::disk(config('heguibao.storage.pub')), $file['path']);
            }
        }

        return $files;
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
