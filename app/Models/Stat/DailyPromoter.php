<?php

namespace App\Models\Stat;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $admin_id
 * @property string $date 日期
 * @property int $consume_credit 消耗的总积分数量
 * @property int $consume_credit_content 内容消耗的积分数量
 * @property int $consume_credit_special 专题消耗的积分数量
 * @property int $payment_order 支付订单数量
 * @property string $payment_amount 支付金额
 * @property int $payment_user 支付用户数
 * @property int $payment_course_order 支付课程订单数量
 * @property string $payment_course_amount 支付课程金额
 * @property int $payment_course_user 支付课程用户数
 * @property string $platform_material_amount 支付资料金额
 * @property int $content 内容总数量
 * @property int $content_material 资料数量
 * @property int $content_course 课程数量
 * @property int $content_news 资讯数量
 * @property int $content_view 浏览数量
 * @property int $content_download 资料下载数
 * @property int $content_special 专题数数
 * @property int $platform_content 平台内容总数量
 * @property int $platform_content_material 平台资料总数量
 * @property int $platform_content_course 平台课程总数量
 * @property int $platform_content_news 平台资讯总数量
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class DailyPromoter extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'stat_daily_promoters';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id', 'admin_id', 'date', 'consume_credit', 'consume_credit_content', 'consume_credit_special', 'payment_order', 'payment_amount', 'payment_user',  'payment_course_order', 'payment_course_amount',
        'payment_course_user', 'platform_material_amount', 'content', 'content_material', 'content_course', 'content_news', 'content_view', 'content_download', 'content_special', 'platform_content',
        'platform_content_material', 'platform_content_course', 'platform_content_news', 'created_at', 'updated_at'
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'admin_id' => 'integer',
        'consume_credit' => 'integer',
        'consume_credit_content' => 'integer',
        'consume_credit_special' => 'integer',
        'payment_order' => 'integer',
        'payment_user' => 'integer',
        'payment_course_order' => 'integer',
        'payment_course_user' => 'integer',
        'content' => 'integer',
        'content_material' => 'integer',
        'content_course' => 'integer',
        'content_news' => 'integer',
        'content_view' => 'integer',
        'content_download' => 'integer',
        'content_special' => 'integer',
        'platform_content' => 'integer',
        'platform_content_material' => 'integer',
        'platform_content_course' => 'integer',
        'platform_content_news' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'consume_credit' => 0,
        'consume_credit_content' => 0,
        'consume_credit_special' => 0,
        'payment_order' => 0,
        'payment_amount' => '0.00',
        'payment_user' => 0,
        'payment_course_order' => 0,
        'payment_course_amount' => '0.00',
        'payment_course_user' => 0,
        'platform_material_amount' => '0.00',
        'content' => 0,
        'content_material' => 0,
        'content_course' => 0,
        'content_news' => 0,
        'content_view' => 0,
        'content_download' => 0,
        'content_special' => 0,
        'platform_content' => 0,
        'platform_content_material' => 0,
        'platform_content_course' => 0,
        'platform_content_news' => 0,
    ];
}
