<?php

namespace App\Models\Punish;

use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property string $name 名称
 * @property int $sort 排序
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic query()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishTopic whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class PunishTopic  extends Model
{
    protected $table = 'punish_topics';

    public function subjects()
    {
        return $this->hasMany(PunishSubject::class, 'topic_id', 'id');
    }

}
