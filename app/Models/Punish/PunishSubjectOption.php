<?php

namespace App\Models\Punish;

use Illuminate\Database\Eloquent\Model;

/**
 *
 *
 * @property int $id
 * @property int $subject_id
 * @property string $name 适用条件
 * @property string $level 载量阶次
 * @property string $standard 具体标准
 * @property int $amount 罚款金额
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption query()
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption whereExplain($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PunishSubjectOption whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class PunishSubjectOption  extends Model
{
    protected $table = 'punish_subject_options';

}
