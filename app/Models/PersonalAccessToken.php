<?php

namespace App\Models;

use <PERSON><PERSON>\Sanctum\PersonalAccessToken as SanctumPersonalAccessToken;


/**
 * @property int $id
 * @property string $tokenable_type
 * @property int $tokenable_id
 * @property string $name
 * @property string $token
 * @property string $abilities
 * @property string $platform
 * @property string $last_used_at
 * @property string $expires_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class PersonalAccessToken extends SanctumPersonalAccessToken
{
    /**
     * The table associated with the model.
     */
    protected $table = 'personal_access_tokens';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = ['id', 'tokenable_type', 'tokenable_id', 'name', 'token', 'abilities', 'platform', 'last_used_at', 'expires_at', 'created_at', 'updated_at'];

    /**
     * The attributes that should be cast to native types.
     */
    protected $casts = [
        'id' => 'integer',
        'tokenable_id' => 'integer',
        'abilities' => 'array',
        'last_used_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
}
