<?php

namespace App\Console\Commands;

use App\Services\Common\AttachmentService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ClearTmpCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:clear-tmp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '删除临时文件（每小时执行一次）';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $logger = Log::channel('task');
        $expire = time() - 5;
        $count = 0;

        clearstatcache();

        $disk = Storage::disk(config('heguibao.storage.local'));

        foreach ($disk->listContents(AttachmentService::TMP_DIR) as $row) {
            if ($row->lastModified() < $expire) {
                $path = $row->path();
                if ($row->isDir()) {
                    $disk->deleteDirectory($path);
                    $logger->info("Deleted expired tmp directory: ".basename($path));
                } else {
                    $disk->delete($path);
                    $logger->info("Deleted expired tmp file: ".basename($path));
                }
                ++$count;
            }
        }

        $this->info("已清理 $count 个临时文件。");

        return self::SUCCESS;
    }
}
