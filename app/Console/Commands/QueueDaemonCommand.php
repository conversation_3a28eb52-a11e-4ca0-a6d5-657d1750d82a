<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class QueueDaemonCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    // 修改 $signature 以支持 -d 参数
    protected $signature = 'queue:daemon {method : start|stop|restart} {--d : Run the process in the background}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '启动通过主进程监控的多个队列消费者进程。';

    /**
     * 启动多少个进程
     * @var int
     */
    protected $num = 4;

    /**
     * 主进程 pid 文件位置
     * @var string
     */
    protected $pidFile = '{storage_path}/framework/queue-daemon.pid';

    /**
     * 标准输出是否已关闭，如果已关闭则当前进程不应该输出任何东西
     * @var bool
     */
    protected $stdClosed = false;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $method = $this->argument('method');

        switch ($method) {
            case 'start':
                return $this->start();

            case 'stop':
                return $this->stop();

            case'restart':
                return $this->restart();

            default:
                $this->warn("Unknown command: $method");
                return self::FAILURE;
        }
    }

    /**
     * 启动进程
     */
    public function start()
    {
        $pidFile = $this->getPidFile();

        if (is_file($pidFile)) {
            [$pid] = file($pidFile, FILE_IGNORE_NEW_LINES);

            if (posix_kill($pid, 0)) {
                $this->warn("Process already running.");
                return self::FAILURE;
            }
        }

        // 进入后台守护进程模式
        $daemon = $this->option('d');
        $daemon && $this->daemonize();

        // 启动工作进程
        $command = [PHP_BINARY, base_path().'/artisan', 'queue:work'];

        /** @var Process[] $processes */
        $processes = [];

        for ($i = 0; $i < $this->num; $i++) {
            // 执行一个进程并监控该进程是否正常
            $process = new Process($command);
            $process->setPty(false);
            $process->start();
            $pid = $process->getPid();
            $processes[$pid] = $process;
            $this->info("Worker $pid started.");
        }

        $keep = true;

        // 注册停止信号
        foreach ([SIGINT, SIGTERM] as $sig) {
            $reg = pcntl_signal($sig, function ($signal) use (&$keep, &$processes) {
                $keep = false;

                $this->warn("Signal $signal received, stopping workers...");

                $ps = $processes;
                $processes = [];

                foreach ($ps as $pid => $process) {
                    $process->stop();
                    $this->warn("Worker $pid stopped.");
                }
            });

            if (!$reg) {
                $this->error("Failed to register signal $sig handler.");
                return self::FAILURE;
            }
        }

        // 注册重启信号
        $reg = pcntl_signal(SIGUSR2, function() use (&$processes, $pidFile, $daemon) {
            $this->warn("Signal SIGUSR2 received, restarting workers...");

            //重启因为可能代码发生更新，工作目录有变化，所以不能直接调用原重启的 $process->start()
            $ps = $processes;
            $processes = [];

            //停止老进程
            foreach ($ps as $pid => $process) {
                $process->stop();
                $this->warn("Worker $pid stopped.");
            }

            //从最新的工作目录启动以确保应用了最新的逻辑代码
            [, $workDir] = file($pidFile, FILE_IGNORE_NEW_LINES);

            //清除 pid 以确保重启后 pid 文件不冲突
            unlink($pidFile);

            //启动新进程
            $this->info("Start new processes");
            $command = [PHP_BINARY, $workDir.'/artisan', 'queue:daemon', 'start'];
            $daemon && $command[] = '--d';
            $process = new Process($command, $workDir);
            $process->enableOutput();
            $process->start();

            foreach ($process->getIterator() as $op) {
                echo $op;
            }

            $process->wait();

            //退出当前进行
            exit(0);
        });

        if (!$reg) {
            $this->error("Failed to register signal SIGUSR2 handler.");
            return self::FAILURE;
        }

        // 写入当前程序 pid 以及工作目录
        file_put_contents($pidFile, getmypid()."\n".base_path());

        // 监控进程
        while ($keep) {
            foreach ($processes as $pid => $process) {
                if (!$process->isRunning()) {
                    $this->info("Worker $pid stopped, restarting...");
                    $process->start();
                    unset($processes[$pid]);
                    $pid = $process->getPid();
                    $processes[$pid] = $process;
                    $this->info("Worker $pid started.");
                }
            }

            // 降低 CPU 占用
            usleep(500000); // 500ms
        }

        // 确保所有子进程停止
        foreach ($processes as $pid => $process) {
            $process->stop();
            $this->warn("Worker $pid stopped");
        }

        // 删除 pid 文件
        if (is_file($pidFile)) {
            unlink($pidFile);
        }

        $this->info('All workers stopped.');

        return self::SUCCESS;
    }

    /**
     * 停止进程
     */
    public function stop()
    {
        $pidFile = $this->getPidFile();

        if (!is_file($pidFile)) {
            $this->warn("Queue worker not running.");
            return self::FAILURE;
        }

        [$pid] = file($pidFile, FILE_IGNORE_NEW_LINES);
        if (posix_kill($pid, 0)) {
            posix_kill($pid, SIGTERM);

            // 等待主进程退出
            for ($i = 0; $i < 10; $i++) {
                if (!posix_kill($pid, 0)) {
                    $this->info("Worker $pid stopped.");
                    return self::SUCCESS;
                }
                usleep(500000); // 500ms
            }

            $this->warn("Failed to stop worker $pid.");
        } else {
            unlink($pidFile);
            $this->warn("Worker not running.");
        }

        return self::SUCCESS;
    }

    public function restart()
    {
        $pidFile = $this->getPidFile();

        if (!is_file($pidFile)) {
            $this->warn('Worker processes not started.');
            return self::FAILURE;
        }

        [$pid, $workDir] = file($pidFile, FILE_IGNORE_NEW_LINES);

        if (!posix_kill($pid, 0)) {
            $this->warn("Worker processes not running.");
            return self::FAILURE;
        }

        $this->info("Restarting workers.");

        //出于当前部署模式是基于 Deployer 的版本号目录软链形式，为了确保重启的进程是最新的业务代码（在最新的工作目录中重启），通过 pid 文件向主进程传递最新的工作目录
        if (base_path() != $workDir) {
            file_put_contents($pidFile, $pid . "\n" . base_path());
        }

        //发送重启信号给主进程
        posix_kill($pid, SIGUSR2);

        return self::SUCCESS;
    }

    private function getPidFile()
    {
        return str_replace('{storage_path}', storage_path(), $this->pidFile);
    }

    protected function daemonize()
    {
        $this->info('Queue worker start in daemon mode.');

        //$this->closeStdIO();

        $pid = pcntl_fork();
        if ($pid === -1) {
            $this->error('Failed to fork process.');
            exit(1);
        } elseif ($pid) {
            exit(0);
        }

        if (posix_setsid() == -1) {
            $this->error('Setsid fail');
            exit(1);
        }

        // Fork again avoid SVR4 system regain the control of terminal.
        //$pid = pcntl_fork();
        //if ($pid === -1) {
        //    $this->error('Failed to fork process (2).');
        //    exit(1);
        //} elseif ($pid) {
        //    exit(0);
        //}

        //pcntl_signal(SIGHUP, SIG_IGN);
    }

    //protected function closeStdIO()
    //{
    //    $this->stdClosed = true;
    //
    //    fclose(STDIN);
    //    fclose(STDOUT);
    //    fclose(STDERR);
    //    $stdin = fopen('/dev/null', 'r');
    //    $stdout = fopen('/dev/null', 'a');
    //    $stderr = fopen('/dev/null', 'a');
    //}

    //public function line($string, $style = null, $verbosity = null)
    //{
    //    if ($this->stdClosed) {
    //        $style === null && $style = 'info';
    //        Log::log($style, $string);
    //    } else {
    //        parent::line($string, $style, $verbosity);
    //    }
    //}

}
