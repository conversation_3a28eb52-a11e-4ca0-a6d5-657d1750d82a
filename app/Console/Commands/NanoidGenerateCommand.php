<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class NanoidGenerateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nanoid:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate an nanoid';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = app('nanoid')->generateId();
        $this->info($id);
    }

}
