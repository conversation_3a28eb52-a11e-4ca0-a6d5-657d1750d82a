<?php

namespace App\Console\Commands;

use App\Core\Enums\BusinessType;
use App\Models\Cms\Content;
use App\Models\Order\Order;
use App\Models\Org\BalanceRecord;
use App\Models\Org\Enrollment;
use App\Models\Org\OrgOrder;
use App\Models\Train\Topic;
use App\Models\User\UserOwnContent;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * 机构订单数据同步，数据源来自机构开课记录和学员自行购买记录
 * 命令：
 *   日常维护使用增量同步：php artisan sync:order --incremental
 *   数据迁移使用全量同步：php artisan sync:order
 *   验证数据使用试运行：php artisan sync:order --dry-run
 *   强制执行：php artisan sync:order --force
 *   组合使用：php artisan sync:order --incremental --dry-run
 */
class SyncOrderCommand extends Command
{
    protected $signature = 'sync:order
                            {--incremental : 增量同步，只同步最近更新的数据}
                            {--force : 强制执行，忽略锁定检查}
                            {--dry-run : 试运行，不实际写入数据库}';

    protected $description = '同步机构开课和学员自行购买记录数据到机构订单表';

    private const CACHE_LOCK_KEY = 'sync_order_command_lock';
    private const CACHE_LOCK_TTL = 3600; // 1小时

    private array $studentsInfoMap = [];
    private array $topicNameMap = [];
    private array $courseNameMap = [];
    private int $processedOnlineOrders = 0;
    private int $processedOfflineOrders = 0;
    private int $skippedOnlineOrders = 0;
    private int $skippedOfflineOrders = 0;

    public function handle(): int
    {
        if (!$this->acquireLock()) {
            $this->error('同步任务正在运行中，请稍后再试');
            return 1;
        }

        try {
            $this->info('开始同步订单数据...');
            $startTime = microtime(true);

            $this->loadBaseData();
            $orderData = $this->collectOrderData();

            if (empty($orderData)) {
                $this->info('没有数据需要同步');
                return 0;
            }

            $this->validateOrderData($orderData);

            if (!$this->option('dry-run')) {
                $this->syncToDatabase($orderData);
            } else {
                $this->info('试运行模式：跳过数据库写入');
            }

            $this->displaySummary($startTime);
            return 0;

        } catch (\Exception $e) {
            $this->error('同步失败: ' . $e->getMessage());
            $this->error('错误位置: ' . $e->getFile() . ':' . $e->getLine());
            return 1;
        } finally {
            // 模拟多进程执行，前一个未执行完
//            sleep(60);
            $this->releaseLock();
        }
    }

    /**
     * 获取锁
     */
    private function acquireLock(): bool
    {
        if ($this->option('force')) {
            return true;
        }

        return Cache::add(self::CACHE_LOCK_KEY, true, self::CACHE_LOCK_TTL);
    }

    /**
     * 释放锁
     */
    private function releaseLock(): void
    {
        Cache::forget(self::CACHE_LOCK_KEY);
    }

    /**
     * 加载基础数据
     */
    private function loadBaseData(): void
    {
        $this->info('加载基础数据...');

        // 加载学员信息映射
        $students = DB::table('org_students')
            ->select(['id', 'org_id', 'user_id', 'name', 'phone'])
            ->get();

        foreach ($students as $student) {
            $combinationKey = $student->org_id . '-' . $student->user_id;
            $this->studentsInfoMap[$combinationKey] = $student;
        }

        // 加载题库和课程名称映射
        $this->topicNameMap = Topic::query()->pluck('name', 'id')->toArray();
        $this->courseNameMap = Content::withTrashed()->pluck('title', 'id')->toArray();

        $this->info('基础数据加载完成');
        $this->info('加载学员信息映射，数量: ' . count($this->studentsInfoMap));
        $this->info('加载题库名称映射，数量: ' . count($this->topicNameMap));
        $this->info('加载课程名称映射，数量: ' . count($this->courseNameMap));
    }

    /**
     * 收集订单数据
     */
    private function collectOrderData(): array
    {
        $onlineOrderData = $this->collectOnlineOrderData();
        $offlineOrderData = $this->collectOfflineOrderData();

        return array_merge($onlineOrderData, $offlineOrderData);
    }

    /**
     * 收集线上订单数据
     */
    private function collectOnlineOrderData(): array
    {
        $this->info('收集线上订单数据...');

        $userIds = array_unique(array_map(function($key) {
            return explode('-', $key)[1];
        }, array_keys($this->studentsInfoMap)));

        $query = Order::query()
            ->with(['payment'])
            ->whereIn('user_id', $userIds)
            ->whereIn('business_type', [BusinessType::CmsCourse, BusinessType::CmsCoursePack, BusinessType::Topic])
            ->whereNotNull('extend');

        // 增量同步：只获取最近更新的数据
        if ($this->option('incremental')) {
            $lastSyncTime = Cache::get('sync_order_last_online_sync', now()->subDays(7));
            $query->where('updated_at', '>', $lastSyncTime);
        }

        $onlineOrders = $query->get();
        $onlineOrderData = [];

        foreach ($onlineOrders as $order) {
            if (!$this->validateOnlineOrder($order)) {
                continue;
            }

            $onlineOrderData[] = $this->buildOnlineOrderData($order);
            $this->processedOnlineOrders++;
        }

        $this->info("线上订单处理完成: 处理 {$this->processedOnlineOrders} 条，跳过 {$this->skippedOnlineOrders} 条不符合条件的");

        // 更新最后同步时间
        if ($this->option('incremental')) {
            Cache::put('sync_order_last_online_sync', now());
        }

        return $onlineOrderData;
    }

    /**
     * 验证线上订单
     */
    private function validateOnlineOrder(Order $order): bool
    {
        if (!isset($order->extend['org_id'])) {
            $this->skippedOnlineOrders++;
            return false;
        }

        $combinationKey = $order->extend['org_id'] . '-' . $order->user_id;
        if (!isset($this->studentsInfoMap[$combinationKey])) {
            $this->skippedOnlineOrders++;
            return false;
        }

        return true;
    }

    /**
     * 构建线上订单数据
     */
    private function buildOnlineOrderData(Order $order): array
    {
        $expiredAt = null;
        if ($order->business_type == BusinessType::Topic) {
            if ($order->payment_at && isset($order->extend['valid_days'])) {
                $expiredAt = Carbon::parse($order->payment_at)->addDays($order->extend['valid_days']);
            }
        } else {
            if ($order->payment_at) {
                $expiredAt = Carbon::parse($order->payment_at)->addMonths(UserOwnContent::ORG_EXPIRED_MOUTH_DEFAULT);
            }
        }

        $combinationKey = $order->extend['org_id'] . '-' . $order->user_id;
        $student = $this->studentsInfoMap[$combinationKey];

        return [
            'org_id' => $order->extend['org_id'],
            'user_id' => $order->user_id,
            'student_id' => $student->id,
            'source_id' => $order->id,
            'source_table' => Order::class,
            'business_type' => $order->business_type->value,
            'name' => $student->name,
            'phone' => $student->phone,
            'title' => $order->title,
            'order_no' => $order->order_no,
            'transaction_no' => $order->payment?->transaction_no ?? null,
            'total_amount' => $order->total_amount,
            'payment_amount' => $order->payment_amount,
            'payment_channel' => OrgOrder::PAYMENT_CHANNEL_ONLINE,
            'status' => $order->status,
            'payment_at' => $order->payment_at?->toDateTimeString(),
            'expired_at' => $expiredAt?->toDateTimeString(),
            'created_at' => $order->created_at->toDateTimeString(),
        ];
    }

    /**
     * 收集线下订单数据
     */
    private function collectOfflineOrderData(): array
    {
        $this->info('收集线下订单数据...');

        $query = BalanceRecord::query()
            ->with(['enrollment', 'enrollment.resource'])
            ->whereIn('type', [BalanceRecord::TYPE_EXPENSE, BalanceRecord::TYPE_REFUND]);

        // 增量同步：只获取最近更新的数据
        if ($this->option('incremental')) {
            $lastSyncTime = Cache::get('sync_order_last_offline_sync', now()->subDays(7));
            $query->where('updated_at', '>', $lastSyncTime);
        }

        $offlineOrders = $query->get();
        $offlineOrderData = [];

        foreach ($offlineOrders as $record) {
            if (!$this->validateOfflineOrder($record)) {
                continue;
            }

            $offlineOrderData[] = $this->buildOfflineOrderData($record);
            $this->processedOfflineOrders++;
        }

        $this->info("线下订单处理完成: 处理 {$this->processedOfflineOrders} 条，跳过 {$this->skippedOfflineOrders} 条不符合条件的");

        // 更新最后同步时间
        if ($this->option('incremental')) {
            Cache::put('sync_order_last_offline_sync', now());
        }

        return $offlineOrderData;
    }

    /**
     * 验证线下订单
     */
    private function validateOfflineOrder(BalanceRecord $record): bool
    {
        if (!$record->enrollment) {
            $this->skippedOfflineOrders++;
            return false;
        }

        $enrollment = $record->enrollment;
        $combinationKey = $enrollment->org_id . '-' . $enrollment->user_id;
        if (!isset($this->studentsInfoMap[$combinationKey])) {
            $this->skippedOfflineOrders++;
            return false;
        }

        return true;
    }

    /**
     * 构建线下订单数据
     */
    private function buildOfflineOrderData(BalanceRecord $record): array
    {
        $enrollment = $record->enrollment;

        if ($enrollment->type == Enrollment::TYPE_TOPIC) {
            $title = $this->topicNameMap[$enrollment->resource_id] ?? '-';
        } else {
            $title = $this->courseNameMap[$enrollment->resource_id] ?? '-';
        }

        $combinationKey = $enrollment->org_id . '-' . $enrollment->user_id;
        $student = $this->studentsInfoMap[$combinationKey];

        return [
            'org_id' => $enrollment->org_id,
            'user_id' => $enrollment->user_id,
            'student_id' => $student->id,
            'source_id' => $record->id,
            'source_table' => BalanceRecord::class,
            'business_type' => $enrollment->type,
            'name' => $student->name,
            'phone' => $student->phone,
            'title' => $title,
            'order_no' => null,
            'transaction_no' => null,
            'total_amount' => abs($record->amount),
            'payment_amount' => abs($record->amount),
            'payment_channel' => OrgOrder::PAYMENT_CHANNEL_OFFLINE,
            'status' => $record->type == BalanceRecord::TYPE_REFUND ? OrgOrder::STATUS_REFUNDED : OrgOrder::STATUS_PAID,
            'payment_at' => $record->created_at?->toDateTimeString(),
            'expired_at' => $enrollment->expired_at?->toDateTimeString(),
            'created_at' => $record->created_at->toDateTimeString(),
        ];
    }

    /**
     * 验证订单数据
     */
    private function validateOrderData(array $orderData): void
    {
        $this->info('验证订单数据...');

        $errors = [];

        // 检查重复的 payment_channel + source_id 组合
        $uniqueKeys = [];
        foreach ($orderData as $index => $order) {
            $key = $order['payment_channel'] . '-' . $order['source_id'];
            if (isset($uniqueKeys[$key])) {
                $errors[] = "发现重复的唯一键: {$key}";
            } else {
                $uniqueKeys[$key] = $index;
            }
        }

        // 检查重复的 order_no
        $orderNos = array_filter(array_column($orderData, 'order_no'));
        $duplicateOrderNos = array_diff_assoc($orderNos, array_unique($orderNos));
        if (!empty($duplicateOrderNos)) {
            $errors[] = "发现重复的订单号: " . implode(', ', array_unique($duplicateOrderNos));
        }

        // 检查重复的 transaction_no
        $transactionNos = array_filter(array_column($orderData, 'transaction_no'));
        $duplicateTransactionNos = array_diff_assoc($transactionNos, array_unique($transactionNos));
        if (!empty($duplicateTransactionNos)) {
            $errors[] = "发现重复的交易号: " . implode(', ', array_unique($duplicateTransactionNos));
        }

        if (!empty($errors)) {
            foreach ($errors as $error) {
                $this->error($error);
            }
            throw new \Exception('数据验证失败');
        }

        $this->info('数据验证通过');
    }

    /**
     * 同步数据到数据库
     */
    private function syncToDatabase(array $orderData): void
    {
        $this->info('开始同步到数据库...');

        $batchSize = 500; // 批量处理大小
        $batches = array_chunk($orderData, $batchSize);
        $totalBatches = count($batches);


        try {
            foreach ($batches as $index => $batch) {
                $this->info("处理批次 " . ($index + 1) . "/{$totalBatches}");

                OrgOrder::query()->upsert(
                    $batch,
                    ['payment_channel', 'source_id'], // 唯一键:支付渠道 + 源数据ID 锁定唯一记录避免重复插入
                    [
                        'org_id',
                        'user_id',
                        'source_table',
                        'business_type',
                        'title',
                        'order_no',
                        'transaction_no',
                        'total_amount',
                        'payment_amount',
                        'status',
                        'payment_at',
                        'expired_at',
                        'created_at',
                    ]
                );
            }

            $this->info('数据同步完成');
        } catch (\Exception $e) {
            logger()->channel('task')->error('同步订单数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->error($e->getMessage());
        }
    }

    /**
     * 显示同步摘要
     */
    private function displaySummary(float $startTime): void
    {
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);

        $totalProcessed = $this->processedOnlineOrders + $this->processedOfflineOrders;
        $totalSkipped = $this->skippedOnlineOrders + $this->skippedOfflineOrders;

        $this->info('');
        $this->info('=== 同步摘要 ===');
        $this->info("执行时间: {$duration} 秒");
        $this->info("线上订单: 处理 {$this->processedOnlineOrders} 条，跳过 {$this->skippedOnlineOrders} 条不符合条件的");
        $this->info("线下订单: 处理 {$this->processedOfflineOrders} 条，跳过 {$this->skippedOfflineOrders} 条不符合条件的");
        $this->info("总计: 处理 {$totalProcessed} 条，跳过 {$totalSkipped} 条");

        if (!$this->option('dry-run')) {
            $actualCount = OrgOrder::query()->count();
            $this->info("数据库中总记录数: {$actualCount}");
        }

        $this->info('=== 同步完成 ===');
    }
}
