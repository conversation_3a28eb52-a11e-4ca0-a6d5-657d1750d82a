<?php

namespace App\Console\Commands;

use App\Libs\Sms\SmsTemplate;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org\Course;
use App\Models\Org\CourseSub;
use App\Models\Org\ExamRecord;
use App\Models\Train\Subject;
use App\Services\SmsService;
use App\Services\Stat\DailyOverviewService;
use App\Services\Topic\TopicService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use function Symfony\Component\Translation\t;

class ZjkTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'zjk-test';

    protected $courseId = 446;
    protected $orgId = 32;
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '朱纪坤的测试命令';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->topicDb();
    }

    public function topicDb()
    {
//        $content = "尊敬的张家口，您负责的工单状态已更新。请尽快登录系统查看并处理，确保客户服务的及时性。感谢您的努力与支持！";
//        SmsService::send(15684590097, SmsTemplate::LOGIN, [1234], "");

        $courseSubTable = (new CourseSub())->getTable();
        $sectionTable = (new ContentCourseSection())->getTable();
        $chapterTable = (new ContentCourseChapter())->getTable();

        // 获取所有开启的章节ID
        $enabledChapterQuery = DB::table($courseSubTable)
            ->select($courseSubTable . '.resource_id')
            ->where($courseSubTable . '.course_id', $this->courseId)
            ->where($courseSubTable . '.type', CourseSub::TYPE_CHAPTER);

        if ($this->orgId > 0) {
            $enabledChapterQuery->where($courseSubTable . '.org_id', $this->orgId);
        }

        $enabledChapterIds = $enabledChapterQuery->pluck('resource_id')->toArray();

        // 主查询
        $query = DB::table($courseSubTable)
            ->select([
                $courseSubTable . '.course_id',
                $courseSubTable . '.org_id',
                DB::raw("SUM({$sectionTable}.duration) as total_duration")
            ])
            ->join($sectionTable, $courseSubTable . '.resource_id', '=', $sectionTable . '.id')
            ->join($chapterTable, $sectionTable . '.chapter_id', '=', $chapterTable . '.id')
            ->where($sectionTable . '.status', ContentCourseSection::STATUS_SHOW)
            ->where($chapterTable . '.status', ContentCourseChapter::STATUS_SHOW)
            ->whereIn($sectionTable . '.chapter_id', $enabledChapterIds) // 只计算开启的章
            ->where($courseSubTable . '.course_id', $this->courseId)
            ->where($courseSubTable . '.type', CourseSub::TYPE_SECTION); // 只计算开启的节

        if ($this->orgId > 0) {
            $query->where($courseSubTable . '.org_id', $this->orgId);
        }

        $query->groupBy([$courseSubTable . '.org_id', $courseSubTable . '.course_id'])->chunkById(50, function ($courses) {
            foreach ($courses as $course) {
                $contentCourse = ContentCourse::query()->where('content_id', $course->course_id)->first();
                if (!$contentCourse) {
                    Log::info('课程时长更新失败', [
                        'org_id' => $course->org_id,
                        'course_id' => $course->course_id,
                        'total_duration' => $course->total_duration,
                    ]);
                    continue;
                }

                $contentCourse->studyHour();
                if (!$contentCourse->hour) {
                    $contentCourse->hour = 0;
                }

                $second = $contentCourse->study_second;
                $hour = bcdiv($course->total_duration, $second);

                Course::query()->where('org_id', $course->org_id)
                    ->where('course_id', $course->course_id)
                    ->update(['hour' => $hour]);

                Log::info('课程时长更新成功', [
                    'org_id' => $course->org_id,
                    'course_id' => $course->course_id,
                    'total_duration' => $course->total_duration,
                    'hour' => $hour
                ]);
            }
        }, 'course_id');
    }
}
