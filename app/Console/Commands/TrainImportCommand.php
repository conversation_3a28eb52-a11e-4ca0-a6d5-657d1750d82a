<?php

namespace App\Console\Commands;

use App\Core\Enums\BusinessType;
use App\Models\Attachment\AttachmentRelation;
use App\Models\Train\Chapter;
use App\Models\Train\Example;
use App\Models\Train\Section;
use App\Models\Train\Subject;
use App\Models\Train\SubjectOption;
use App\Models\Train\Test;
use App\Models\Train\TestSubject;
use App\Services\Common\AttachmentService;
use App\Services\Topic\ImportService;
use Illuminate\Console\Command;
use Throwable;

class TrainImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:train-import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dir = storage_path('docs/train-0411');

        $topics = $this->getDirFiles($dir);

        $this->handleImport($topics);

        print_r($topics);
    }

    protected function handleImport($data)
    {
        $importService = new ImportService();

        foreach ($data as $topic) {
            foreach ($topic['children'] as $v) {
                $this->output->writeln("【章】 {$v['name']}");

                // 章处理
                $chapter = new Chapter();
                $chapter->topic_id = $topic['id'];
                $chapter->name = $v['name'];
                $chapter->save();

                $chapterData = json_decode(file_get_contents($v['path']), true);

                foreach ($chapterData as $kk => $vv) {
                    $this->output->writeln("【节】 {$vv['name']}");

                    // 节处理
                    $section = new Section();
                    $section->topic_id = $topic['id'];
                    $section->chapter_id = $chapter->id;
                    $section->name = $vv['name'];
                    $section->save();

                    // 案例
                    $exampleId = 0;
                    if (isset($vv['example'])) {
                        if ($kk == 0) {
                            $chapter->example = 1;
                            $chapter->save();
                        }

                        $example = new Example();
                        $example->topic_id = $topic['id'];
                        $example->chapter_id = $chapter->id;
                        $example->section_id = $section->id;

                        if ($importService->hasImage($vv['example'])) {
                            $example->content = '';
                            $example->save();

                            $example->content = $importService->dataToHtml($vv['example'], $example->id, BusinessType::TrainExample);
                        } else {
                            $example->content = $importService->dataToHtml($vv['example']);
                        }

                        $example->save();

                        $exampleId = $example->id;
                    }

                    foreach ($vv['questions'] as $vvv) {
                        // 题目
                        $subject = new Subject();
                        $subject->topic_id = $topic['id'];
                        $subject->chapter_id = $chapter->id;
                        $subject->section_id = $section->id;
                        $subject->example_id = $exampleId;
                        $subject->type = $vvv['type'];
                        $subject->intro = '';
                        $subject->answer = '';
                        $subject->analysis = '';

                        $hasImage = false;
                        if ($importService->hasImage($vvv['question']) || $importService->hasImage($vvv['answer']) || $importService->hasImage($vvv['analysis'])) {
                            $hasImage = true;
                        }

                        if ($hasImage) {
                            $subject->save();

                            $subject->intro = $importService->dataToHtml($vvv['question'], $subject->id, BusinessType::Subject);
                            $subject->answer = $importService->dataToHtml($vvv['answer'], $subject->id, BusinessType::Subject);
                            $subject->analysis = $importService->dataToHtml($vvv['analysis'], $subject->id, BusinessType::Subject);
                        } else {
                            $subject->intro = $importService->dataToHtml($vvv['question']);
                            $subject->answer = $importService->dataToHtml($vvv['answer']);
                            $subject->analysis = $importService->dataToHtml($vvv['analysis']);
                        }

                        $subject->save();

                        if (in_array($vvv['type'], [Subject::TYPE_SC, Subject::TYPE_MC])) {
                            foreach ($vvv['options'] as $oIndex => $v4) {
                                $subjectOption = new SubjectOption();
                                $subjectOption->subject_id = $subject->id;
                                $subjectOption->name = $importService->dataToHtml(preg_replace('/^[A-Z]\.?/', '', $v4));
                                $subjectOption->is_correct = $importService->toChoiceIsCorrect($vvv['answer'], $oIndex);
                                $subjectOption->save();
                            }
                        }
                    }
                }
            }
        }
    }

    protected function getDirFiles($dir): array
    {
        $result = [];

        // 判断目录是否存在
        if (!is_dir($dir)) {
            return [];
        }

        // 获取目录内容
        $items = scandir($dir);

        foreach ($items as  $item) {
            // 跳过 . 和 ..
            if ($item === '.' || $item === '..') {
                continue;
            }

            $path = $dir . DIRECTORY_SEPARATOR . $item;

            // 如果是目录，递归处理
            if (is_dir($path)) {
                $dirname = preg_replace('/(\d+)\.?/', '', $item);

                preg_match('/(\d+)\.?/', $item, $matches);

                $topicId = $matches[1];

                $result[] = [
                    'id' => $topicId,
                    'name' => $dirname,
                    'children' => $this->getDirFiles($path),
                ];
            } else {
                $filename = preg_replace('/\d+\.?/', '', $item);
                $filename = str_replace('.txt', '', $filename);

                // 如果是文件，直接添加
                $result[] = [
                    'name' => $filename,
                    'path' => $path,
                ];
            }
        }

        return $result;
    }
}
