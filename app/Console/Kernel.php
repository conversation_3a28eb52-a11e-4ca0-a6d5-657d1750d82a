<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        $schedule->command('storage:clear-tmp')->hourly();

        $schedule->command('statistic yesterday')->dailyAt('05:00');
        $schedule->command('statistic today')->everyFifteenMinutes();
        $schedule->command('enrollment:expire')->everyTenMinutes();
        $schedule->command('classes start')->dailyAt('00:10');
        $schedule->command('classes finish')->dailyAt('23:50');
        $schedule->command('course:progress')->everyMinute();

        // 机构订单数据同步
        $schedule->command('sync:order --incremental')->runInBackground()->everyFiveMinutes();

        if (config('app.env') == 'prod') {
            // 每周一的 03:00 执行一次任务
            // $schedule->command('command:chat-recommend')->weeklyOn(1, '03:00');
        }
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
