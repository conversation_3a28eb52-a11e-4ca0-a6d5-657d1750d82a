<?php

namespace App\Core;

use Illuminate\Database\Eloquent\Builder;
use Sqids\Sqids;

/**
 * 用于 Model 增的编码 ID 功能
 *
 * 可以通过定义一个访问器，来自动的将 id 转换为 hash id 值，在模型中添加以下参考代码：
 *
 * protected function id(): Attribute
 * {
 *     return Attribute::make(
 *         get: fn (int $id) => self::encode($id),
 *     );
 * }
 *
 * 此时要获取模型的原始 id 使用 $model->getRawOriginal('id');
 *
 * @method string addHashIdSalt() 添加此方法定制重排 HashId 字母表的盐
 */
trait HashIdAttribute
{

    /**
     * 获取编码字段
     *
     * @return string
     */
    protected static function getHashIdKey(): string
    {
        return 'id';
    }

    /**
     * 获取带有 Hash 内容的查询器
     *
     * @param string $hashId
     * @return Builder|static
     */
    public static function whereSid(string $hashId): Builder|static
    {
        $key = static::getHashIdKey();
        $value = self::sqids()->decode($hashId);
        return static::where($key, $value ? $value[0] : 0);
    }

    /**
     * 虚拟字段：编码后的 hash_id
     *
     * @return string
     */
    public function getHashIdAttribute(): string
    {
        $key = static::getHashIdKey();
        return self::sqids()->encode([$this->$key]);
    }

    /**
     * 别名：sid
     *
     * @return string
     */
    public function getSidAttribute(): string
    {
        return $this->getHashIdAttribute();
    }

    /**
     * 获取模型的 HashIds 实例，可用于直接编解码 ID
     *
     * @return Sqids
     */
    public static function sqids(): Sqids
    {
        $app = app();
        $id = 'hashids.'.static::class;

        if ($app->has($id)) {
            return $app->get($id);
        } else {
            if (method_exists(static::class, 'addHashIdSalt')) {
                //模型特有的加盐
                $seed = config('app.hash_id_salt') . static::addHashIdSalt();
            } else {
                $seed = config('app.hash_id_salt');
            }

            $alphabet = self::rearrange($seed);
            $hashId = new Sqids($alphabet, 8, []);
            return $app->instance($id, $hashId);
        }
    }

    /**
     * 将 id 编码为 sid
     *
     * @param int $id
     * @return string
     */
    public static function encodeId($id)
    {
        return self::sqids()->encode([$id]);
    }

    /**
     * 解析指定 sid
     *
     * @param string $sid
     * @return int
     */
    public static function decodeSid($sid)
    {
        $de = self::sqids()->decode($sid);
        return $de ? $de[0] : 0;
    }

    /**
     * 使用 seed 混淆 Sqids 的字母表
     * @param string $seed
     * @return string
     */
    private static function rearrange($seed)
    {
        $alphabet = Sqids::DEFAULT_ALPHABET;
        $sourceLength = strlen($alphabet);
        $seed = (string)crc32($seed);
        $seedLength = strlen($seed);

        for ($i=0; $i<$sourceLength; ++$i) {
            $swap = $seed[$i % $seedLength] % $sourceLength;
            $temp = $alphabet[$swap];
            $alphabet[$swap] = $alphabet[$i];
            $alphabet[$i] = $temp;
        }

        return $alphabet;
    }

}
