<?php

namespace App\Services;

use App\Exceptions\ServiceException;
use App\Models\SmsRecord;
use App\Services\Common\PhoneCodeService;
use Illuminate\Support\Facades\Log;
use Overtrue\EasySms\EasySms;
use Overtrue\EasySms\Exceptions\NoGatewayAvailableException;
use TencentCloud\Common\Credential;
use TencentCloud\Sms\V20210111\SmsClient;

class SmsService
{
    /**
     * 短信通知
     *
     * @param string $phone 手机号
     * @param string $type 类型
     * @param array $data 数据
     * @param string $content 短息内容
     * @param int $platform 平台
     *
     * @return void
     */
    public static function send(string $phone, string $type, array $data, string $content, int $platform = SmsRecord::PLATFORM_TENCENT): void
    {
        try {
            // 验证手机格式
            PhoneCodeService::checkPhoneNumber($phone);
            $templateId = config('easysms.gateways.qcloud.template_ids.' . $type);

            if (!$templateId) {
                throw new ServiceException("短信模板不存在");
            }

            if ($platform == SmsRecord::PLATFORM_TENCENT) {
                try {
                    app(EasySms::class)->send($phone, ['template' => $templateId, 'data' => $data]);

                } catch (NoGatewayAvailableException $e) {
                    throw new ServiceException($e->getLastException()->getMessage());
                }
            } else {
                throw new ServiceException("短信类型不存在");
            }
        } catch (\Throwable $e) {
            Log::channel('sms')->error($e->getMessage(), ['phone' => $phone, 'type' => $type]);
        }

        // 日志记录
        self::create($phone, $type, $content, request()->getClientIp(), $platform);
    }

    /**
     * 创建短信发送记录
     *
     * @param string $phone 手机号
     * @param string $type 业务类型
     * @param string $content 内容
     * @param string $ip IP
     * @param int $platform 短信平台
     *
     * @return SmsRecord
     */
    public static function create(string $phone, string $type, string $content, string $ip, int $platform): SmsRecord
    {
        $smsRecord = new SmsRecord();
        $smsRecord->phone = $phone;
        $smsRecord->type = $type;
        $smsRecord->ip = $ip;
        $smsRecord->content = $content;
        $smsRecord->platform = $platform;
        $smsRecord->save();

        return $smsRecord;
    }
}
