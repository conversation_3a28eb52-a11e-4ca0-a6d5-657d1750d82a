<?php

namespace App\Services\Stat;

class StatService
{
    public static function getDateRange($startDate = null, $endDate = null): array
    {
        $dateRange = [];
        if ($startDate) {
            $startTime = strtotime($startDate);
            if ($endDate) {
                $endTime = strtotime($endDate);
            } else {
                $endTime = $startTime;
            }

            if ($startTime > $endTime) {
                $startTime = $startTime + $endTime;
                $endTime = $startTime - $endTime;
                $startTime = $startTime - $endTime;
            }

            $dateRange = [date('Y-m-d H:i:s', $startTime), date('Y-m-d H:i:s', $endTime + 86399)];
        }

        return $dateRange;
    }
}
