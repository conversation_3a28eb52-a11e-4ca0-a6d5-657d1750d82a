<?php

namespace App\Services\Admin;

use App\Models\Admin\AdminOperate;

class OperateLogService
{
    public static function create(int $adminId, string $remark, mixed $context): AdminOperate
    {
        $model = new AdminOperate();
        $model->admin_id = $adminId;
        $model->remark = $remark;
        $model->context = is_array($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : $context;
        $model->save();

        return $model;
    }
}
