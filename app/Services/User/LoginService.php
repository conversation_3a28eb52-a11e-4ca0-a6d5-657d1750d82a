<?php

namespace App\Services\User;

use App\Core\Enums\BusinessType;
use App\Exceptions\UnboundUserException;
use App\Exceptions\ServiceException;
use App\Models\PersonalAccessToken;
use App\Models\User;
use App\Models\User\UserBind;
use Carbon\Carbon;
use Hidehalo\Nanoid\Client;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LoginService
{

    public static function userStatusCheck(int $status)
    {
        match ($status) {
            User::STATUS_FORBID => throw new ServiceException('账号已被禁用，无法登录。', 403),
            User::STATUS_DESTROYING, User::STATUS_DELETED => throw new ServiceException('账号不存在。'),
            User::STATUS_NORMAL, User::STATUS_MUTE => null,
            default => throw new ServiceException('账号异常。', 403)
        };
    }

    /**
     * 通过手机号登录，手机号不存在则自动注册
     *
     * @param string $phone 手机号码
     * @param string $ip 登录时的 IP
     * @param string $platform 平台
     * @param string $system 系统
     * @param string $referral 推荐人的 uuid
     */
    public static function loginByPhone($phone, $ip, string $platform, string $system, $referral=null)
    {
        Log::info("loginByPhone->referral $referral");
        $user = User::getUserByPhone($phone);

        if ($user) {
            self::userStatusCheck($user->status);
        }

        if (!$user) {
            $user = User::create($phone);
        } else {
            $user->status = User::STATUS_NORMAL;
        }

        return self::login($user, $ip, $platform, $system, 0, $referral);
    }

    /**
     * 通过第三方平台授权绑定进行登录
     *
     * @param string $platform
     * @param string $openId
     * @param string $ip 登录时的 IP
     * @param string $clientPlatform 客户端平台
     * @param string $clientSystem 客户端系统
     * @param null|string $unionId
     * @param string $referral 推荐人的 uuid
     * @return array
     * @throws UnboundUserException
     * @throws ServiceException
     */
    public static function loginByBind($platform, $openId, $ip, string $clientPlatform, string $clientSystem, $unionId=null, $referral=null)
    {
        $bind = UserBind::query()
            ->where('platform', $platform)
            ->where('open_id', $openId)
            ->first();

        //如果有 union_id，则尝试通过 union_id 找到同平台其它端相同的用户进行绑定登录
        if (!$bind && $unionId) {
            [$provider] = explode('.', $platform, 2);

            $unionBind = UserBind::query()
                ->where('platform', 'like', $provider.'.%')
                ->where('union_id', $unionId)
                ->first();

            if ($unionBind) {
                $bind = new UserBind();
                $bind->user_id = $unionBind->user_id;
                $bind->platform = $platform;
                $bind->open_id = $openId;
                $bind->union_id = $unionId;
                $bind->last_logged_at = Carbon::now();
                $bind->save();
            }
        }

        if (!$bind || !isset($bind->user)) {
            self::throwBindCode($platform, $openId, $unionId);
        }

        self::userStatusCheck($bind->user->status);

        if (!$bind->wasRecentlyCreated) {
            $bind->last_logged_at = Carbon::now();
            $bind->save();
        }

        return self::login($bind->user, $ip, $clientPlatform, $clientSystem, $bind->id, $referral);
    }

    /**
     * 生成 BindCode 并抛出 UnboundUserException 异常
     *
     * @param string $platform
     * @param string $openId
     * @param null|string $unionId
     * @throws UnboundUserException
     */
    public static function throwBindCode($platform, $openId, $unionId=null)
    {
        $bindCode = app('nanoid')->generateId();
        Cache::put('bind-code:'.$bindCode, compact('platform', 'openId', 'unionId'), 900);
        throw new UnboundUserException($bindCode);
    }

    /**
     * 获取绑定数据
     *
     * @param string $bindCode
     * @return array|null
     */
    public static function getBindData($bindCode)
    {
        return Cache::get('bind-code:'.$bindCode);
    }

    /**
     * 移除绑定数据
     *
     * @param string $bindCode
     */
    public static function removeBindData($bindCode)
    {
        Cache::forget('bind-code:'.$bindCode);
    }

    /**
     * 登录到指定用户并处理邀请
     *
     * @param User $user
     * @param string $ip
     * @param string $clientPlatform 客户端平台
     * @param string $clientSystem 客户端系统
     * @param int $bindId 登录时的绑定 ID
     * @param string $referral
     * @return array{user: User, token: string, ttl: int}
     */
    public static function login(User $user, $ip, string $clientPlatform, string $clientSystem, $bindId=0, $referral='')
    {
        $user->last_logged_at = Carbon::now();
        $user->last_logged_ip = $ip;
        $user->last_active_at = Carbon::now();
        $user->last_active_ip = $ip;
        $user->save();

        $isNewRegister = $user->wasRecentlyCreated;

        Log::info("referral: $referral user_id: $user->id isNew: $isNewRegister");

        if ($isNewRegister) {
            //新用户注册奖励
            $registerRewardCredit = config('heguibao.register.reward_credit');
            if ($registerRewardCredit > 0) {
                CreditService::recharge($user->id, $registerRewardCredit, BusinessType::Activity, 0, '新用户奖励');
            }

            //邀请奖励
            if ($referral) {
                InvitationService::invite($referral, $user->id);
            }
        }

        /** @var User $user */
        $user = User::query()->publicFields()->where('id', $user->id)->first();

        $token = $user->createToken($clientSystem, extraData: ['platform' => $clientPlatform]);
        $expiredAt = $user->tokens()->where('platform', $clientPlatform)->value('expires_at');

        return [
            'user' => $user,
            'token' => $token->plainTextToken,
            'ttl' => Carbon::parse($expiredAt)->diffInSeconds(Carbon::now())
        ];
    }

    /**
     * 创建用户
     *
     * @param string $phone 用户手机号
     * @param string $ip 用户ip
     * @param string|null $nickname 用户昵称
     * @return User
     */
    public static function createUser(string $phone, string $ip, string $nickname = null): User
    {
        $user = new User();
        $user->phone = $phone;
        $user->uuid = (new Client())->generateId();
        $user->nickname = $nickname ?: '用户' . mt_rand(1000, 9999);
        $user->last_logged_at = Carbon::now();
        $user->last_logged_ip = $ip;
        $user->last_active_at = Carbon::now();
        $user->last_active_ip = $ip;
        $user->save();

        return $user;
    }

}
