<?php

namespace App\Services\User;

use App\Core\Enums\BusinessType;
use App\Models\User\UserProperty;

/**
 * 用户任务与奖励服务
 */
class UserTaskService
{

    /**
     * 修改头像/昵称 赠送积分
     * @param $uid
     * @param string $nickname
     * @param string $avatar
     * @return void
     */
    public static function editNicknameAvatar($uid, string $nickname = '', string $avatar = '')
    {
        $userProperty = UserProperty::get($uid, ['nickname_edited_at', 'avatar_edited_at']);

        if ($userProperty->nickname_edited_at && $userProperty->avatar_edited_at){ // 已经完成任务了
            return;
        }
        $credit = config('heguibao.update_nickname_avatar.reward_credit');
        if ($nickname && $userProperty->avatar_edited_at){
            CreditService::recharge($uid, $credit, BusinessType::Activity, $uid, '完成修改头像昵称活动赠送积分');
        }elseif ($avatar && $userProperty->nickname_edited_at){
            CreditService::recharge($uid, $credit, BusinessType::Activity, $uid, '完成修改头像昵称活动赠送积分');
        }

        if ($nickname){
            $userProperty->nickname_edited_at = now();
            $userProperty->save();
        }else{
            $userProperty->avatar_edited_at = now();
            $userProperty->save();
        }
    }

}
