<?php

namespace App\Services\User;

use App\Core\Enums\BusinessType;
use App\Models\Cms\Content;
use App\Models\User\UserAttitude;
use Illuminate\Database\Eloquent\Model;

class AttitudeService
{
    /**
     * 获取用户态度
     *
     * @param int|null $userId 用户ID
     * @param Model $business 业务模型对象
     * @return int
     */
    public static function getAttitude(int|null $userId, Model $business): int
    {
        if (!$userId) {
            return 0;
        }
        if ($business instanceof Content) {
            //CMS 的内容有子类型，需要特别获取
            $businessType = $business->getBusinessType();
        } else {
            $businessType = BusinessType::from($business->getMorphClass());
        }

        $attitude = UserAttitude::query()
            ->where('user_id', $userId)
            ->where('business_type', $businessType)
            ->where('business_id', $business->getKey())
            ->value('attitude');

        if (!$attitude) {
            return 0;
        }

        return $attitude;
    }
}
