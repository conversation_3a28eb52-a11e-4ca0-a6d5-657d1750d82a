<?php

namespace App\Services\User;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\User;
use App\Models\User\UserCreditLog;
use Illuminate\Support\Facades\DB;

/**
 * 积分服务
 */
class CreditService
{

    /**
     * 收入积分（充值）
     *
     * @param int $userId 用户ID
     * @param int $credit 要增加的积分数量
     * @param BusinessType $businessType 业务类型
     * @param int $businessId 业务ID
     * @param string $remark 备注
     * @return int 变更后的用户积分
     */
    public static function recharge($userId, $credit, BusinessType $businessType, $businessId, $remark='')
    {
        return self::update($userId, $credit, UserCreditLog::TYPE_RECHARGE, $businessType, $businessId, $remark);
    }

    /**
     * 支出积分（消费）
     *
     * @param int $userId 用户ID
     * @param int $credit 要减少的积分数量
     * @param BusinessType $businessType 业务类型
     * @param int $businessId 业务ID
     * @param string $remark 备注
     * @return int 变更后的用户积分
     */
    public static function consume($userId, $credit, BusinessType $businessType, $businessId, $remark='')
    {
        return self::update($userId, $credit, UserCreditLog::TYPE_CONSUME, $businessType, $businessId, $remark);
    }

    /**
     * 变更积分
     *
     * @param int $userId
     * @param int $credit
     * @param string $type
     * @psalm-param "recharge"|"consume" $type 变更类型，recharge 收入，consume 支出
     * @param BusinessType $businessType
     * @param int $businessId
     * @param string $remark
     * @return int
     */
    public static function update($userId, $credit, $type, BusinessType $businessType, $businessId, $remark='')
    {
        if ($credit == 0) {
            throw new ServiceException('积分没有变化。');
        }

        $user = User::query()
            ->where('id', $userId)
            ->first();

        if ($type == UserCreditLog::TYPE_CONSUME && $user->credit < $credit) {
            throw new ServiceException('积分不足。', 403);
        }

        $originCredit = $user->credit;
        $changeCredit = $type == UserCreditLog::TYPE_RECHARGE ? $credit : -$credit;

        DB::beginTransaction();

        try {
            //变更积分
            $type == UserCreditLog::TYPE_RECHARGE ? $user->increment('credit', $credit) : $user->decrement('credit', $credit);

            //新增记录
            $log = new UserCreditLog();
            $log->user_id = $userId;
            $log->origin_credit = $originCredit;
            $log->change_credit = $changeCredit;
            $log->real_change_credit = $changeCredit;
            $log->type = $type;
            $log->business_type = $businessType;
            $log->business_id = $businessId;
            $log->remark = $remark;
            $log->save();

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            throw new ServiceException('更新积分失败。', previous: $e);
        }

        return $originCredit + $changeCredit;
    }

    /**
     * 通过金额计算对应的积分数量
     *
     * @param int $amount
     * @return int
     */
    public static function calcAmountCredit($amount)
    {
        //Todo: 阶梯式计费或者配置计费
        return bcmul($amount, config('heguibao.recharge.credit_multiple'), 0);
    }

}
