<?php

namespace App\Services\User;

use App\Core\Enums\BusinessType;
use App\Models\Cms\Content;
use App\Models\User\UserFavorite;
use Illuminate\Database\Eloquent\Model;

class FavoriteService
{
    /**
     * 检查是否收藏
     *
     * @param int|null $userId 用户ID
     * @param Model $business 业务模型对象
     * @return bool
     */
    public static function checkFavorite(int|null $userId, Model $business): bool
    {
        if (!$userId) {
            return false;
        }

        if ($business instanceof Content) {
            //CMS 的内容有子类型，需要特别获取
            $businessType = $business->getBusinessType();
        } else {
            $businessType = BusinessType::from($business->getMorphClass());
        }

        return UserFavorite::query()
            ->where('user_id', $userId)
            ->where('business_type', $businessType)
            ->where('business_id', $business->getKey())
            ->exists();
    }
}
