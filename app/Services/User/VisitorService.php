<?php

namespace App\Services\User;

use App\Models\Visitor;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * 访客服务
 */
class VisitorService
{

    /**
     * 为访客 Token 建立链接关系
     *
     * 分以下几种情况
     * 1. 首次没 Token 生成 Token
     * 2. 有 Token 但之前是未登录，此次是登录，则为该 Token 绑定用户ID
     * 3. 有 Token 但之前是有用户ID，此次未登录，则不变
     * 4. 有 Token 但之前是另一个用户，此次登录变了另一个用户，则新建相同 Token 绑定
     *
     * @param string $token
     * @param int $userId
     * @return Visitor
     */
    public static function linkVisitor($token, $userId)
    {
        if (!$token) {
            $token = app('nanoid')->generateId();
        }

        //查找该 Token 已绑定的用户关系
        $visitor = Visitor::query()
            ->where('token', $token)
            ->when($userId, function (Builder $query, $userId) {
                $query->where('user_id', $userId);
            })
            ->orderByDesc('id')
            ->first();

        //情况3

        //情况2
        if (!$visitor && $userId) {
            $visitor = Visitor::query()
                ->where('token', $token)
                ->where('user_id', 0)
                ->first();

            if ($visitor) {
                $visitor->user_id = $userId;
            }
        }

        //else 情况4

        //情况1
        if (!$visitor) {
            $visitor = new Visitor();
            $visitor->token = $token;
            $visitor->user_id = $userId ?: 0;
        }

        $visitor->last_active_at = Carbon::now();
        $visitor->save();

        return $visitor;
    }

    /**
     * 获取所有相关的 Token 和用户 ID
     *
     * @param string $token
     * @param int $userId
     * @param bool $deep 是否深层查找
     * @return array{tokens: string[], userIds: int[]}
     */
    public static function getRelevant($token, $userId, $deep=true)
    {
        /** @var Collection|Visitor[] $visitors */
        $visitors = Visitor::query()
            ->select(['token', 'user_id'])
            ->where('token', $token)
            ->when($userId, function (Builder $query, $userId) {
                $query->orWhere('user_id', $userId);
            })
            ->get();

        $tokens = $userIds = [];

        foreach ($visitors as $visitor) {
            $tokens[] = $visitor->token;
            if ($visitor->user_id) {
                $userIds[] = $visitor->user_id;
            }
        }

        $tokens = array_unique($tokens);
        $userIds = array_unique($userIds);

        if ($deep) {
            $rTokenUserIds = $rUserIdTokens = [];

            //此次关联出的 user_id 的其它 token 所关联的用户 user_id
            if ($tokens) {
                $rTokenUserIds = Visitor::query()
                    ->select(['token', 'user_id'])
                    ->whereIn('token', $tokens)
                    ->when($userIds, function (Builder $query, $userIds) {
                        $query->whereNotIn('user_id', $userIds);
                    })
                    ->pluck('user_id')
                    ->toArray();
            }

            //此次关联出的 token 的其它 user_id 所关联的 token
            if ($userIds) {
                $rUserIdTokens = Visitor::query()
                    ->select(['token', 'user_id'])
                    ->whereIn('user_id', $userIds)
                    ->when($tokens, function (Builder $query, $tokens) {
                        $query->whereNotIn('token', $tokens);
                    })
                    ->pluck('token')
                    ->toArray();
            }

            //以上步骤其实可以一层一层一直查找下云，直到没结果为止，但代价太大，太过于严格。

            if ($rTokenUserIds) {
                $userIds = array_unique(array_merge($userIds, $rTokenUserIds));
            }

            if ($rUserIdTokens) {
                $tokens = array_unique(array_merge($tokens, $rUserIdTokens));
            }
        }

        return compact('tokens', 'userIds');
    }

}
