<?php

namespace App\Services\User;

use App\Exceptions\ServiceException;
use App\Models\Cms\ContentCourse;
use App\Models\User\UserOwnContent;
use Carbon\Carbon;

class OwnContentService
{
    /**
     * 对应内容是否购买过
     *
     * @param int|null $userId 用户ID
     * @param array $contentIds 内容集合
     * @param bool $allInclusive 是否要全包含才认为有权限
     * @param int $enrollId 机构学员学习 ID
     * @return bool
     */
    public static function checkPurchase(int|null $userId, array $contentIds, bool $allInclusive=true, int $enrollId=0): bool
    {
        if (!$userId) {
            return false;
        }

        if (empty($contentIds)) {
            return false;
        }

        $ownContentIds = UserOwnContent::query()
            ->where('user_id', $userId)
            ->whereIn('content_id', $contentIds)
            ->where(function ($query) {
                $query->whereNull('expired_at')->orWhere('expired_at', '>', Carbon::now());
            })
            ->where('enroll_id', $enrollId)
            ->orderByDesc('id')
            ->pluck('content_id')
            ->unique()
            ->toArray();

        if (!$ownContentIds) {
            return false;
        }

        if ($allInclusive) {
            //全包含时，检查的ID必须全部拥有
            return count(array_diff($contentIds, $ownContentIds)) === 0;
        } else {
            //非全包含时，任何一个ID拥有即可
            return count(array_intersect($contentIds, $ownContentIds)) > 0;
        }
    }

    /**
     * 创建用户资料记录
     *
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @param string $classify 分类
     * @param Carbon|null $expiredAt
     * @param int $orgId
     * @param int $enrollId
     * @return UserOwnContent
     */
    public static function create(int $userId, int $contentId, string $classify, Carbon $expiredAt = null, int $orgId = 0, int $enrollId = 0): UserOwnContent
    {
        $userOwnContent = new UserOwnContent();
        $userOwnContent->user_id = $userId;
        $userOwnContent->content_id = $contentId;
        $userOwnContent->classify = $classify;
        $userOwnContent->enroll_id = $enrollId;
        $userOwnContent->org_id = $orgId;
        $userOwnContent->expired_at = $expiredAt ?? Carbon::now()->addYear();
        $userOwnContent->save();

        if ($classify == UserOwnContent::CLASSIFY_COURSE) {
            $course = ContentCourse::query()->where('content_id', $contentId)->first();
            $course?->increment('learning_count');
        }

        return $userOwnContent;
    }

    public static function remove(int $id): void
    {
        $own = UserOwnContent::withTrashed()->find($id);

        if (!$own) {
            throw new ServiceException('记录不存在');
        }

        $own->delete();
    }
}
