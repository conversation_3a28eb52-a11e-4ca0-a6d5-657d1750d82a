<?php

namespace App\Services\User;

use App\Exceptions\ServiceException;
use App\Models\User\UserProperty;
use Illuminate\Support\Facades\Cache;

/**
 * 用户小提示记录服务
 */
class TipService
{

    /**
     * 检查指定键值是否已存在
     *
     * @param int $uid
     * @param string $tip
     * @return bool
     */
    public static function has($uid, $tip)
    {
        $key = self::key($uid);
        $data = Cache::get($key);

        if ($data === null) {
            $data = UserProperty::get($uid, ['tips'])->tips ?? [];
            Cache::put($key, $data, 86400);
        }

        if (!isset($data[$tip])) return false;
        if ($data[$tip] == 0 || $data[$tip] >= time()) return true;

        self::remove($uid, $tip);
        return false;
    }

    /**
     * 设置指定键值为关闭
     *
     * @param int $uid
     * @param string $tip
     * @param string|int $valid 有效期，0代表永久有效
     * 有效期支持三种格式
     * 1. 当天（自然天）有效，设置为 td 代表 this day，即过了今天失效
     * 2. 秒数，指定一个数字代表从当时往后的多少秒有效
     * 3. 混合标识，可用内置的一堆词组合时长，目前支持的有 ymwdhis（年月周日时分秒），如 1y1w2d3h 代表1年1周2天3小时，可自由组合
     * 如果传递了有效期不符合所有规定，则默认为8秒有效期
     */
    public static function set($uid, $tip, $valid=0)
    {
        if ($valid != 0) {
            $valid = strtolower($valid);
            if ($valid == 'td') {
                $valid = strtotime('Today 23:59:59');
            } elseif (ctype_digit($valid)) {
                $valid = time() + (int)$valid;
            } elseif (preg_match_all('/(\d+)([ymwdhis])/', $valid, $match)) {
                $seconds = 0;
                foreach ($match[1] as $i => $v) {
                    switch ($match[2][$i]) {
                        case 'y': $seconds += $v*31536000; break;
                        case 'm': $seconds += $v*2592000; break;
                        case 'w': $seconds += $v*604800; break;
                        case 'd': $seconds += $v*86400; break;
                        case 'h': $seconds += $v*3600; break;
                        case 'i': $seconds += $v+60; break;
                        case 's': $seconds += $v; break;
                    }
                }
                $valid = time() + $seconds;
            } else {
                $valid = time() + 8;
            }
        }

        $props = UserProperty::get($uid, ['tips']);
        $data = $props->tips ?? [];
        $data[$tip] = $valid;
        $props->tips = $data;

        if ($props->save()) {
            Cache::forget(self::key($uid));
        } else {
            throw new ServiceException('保存tips失败');
        }
    }

    /**
     * 移除指定键的标记
     *
     * @param int $uid
     * @param string $tip
     */
    public static function remove($uid, $tip)
    {
        $props = UserProperty::get($uid, ['tips']);
        if (isset($props->tips[$tip])) {
            $data = $props->tips;
            unset($data[$tip]);
            $props->tips = $data;
            if ($props->save()) {
                Cache::forget(self::key($uid));
            } else {
                throw new ServiceException('保存tips失败');
            }
        }
    }

    private static function key($uid)
    {
        return "user:$uid:tips";
    }

}
