<?php

namespace App\Services\Common;

use App\Core\OrderableInterface;
use App\Exceptions\ServiceException;
use App\Models\Order\Order;
use App\Models\Order\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Sqids\Sqids;
use Yansongda\Artful\Contract\LoggerInterface;
use Yansongda\Pay\Pay;

class PaymentService
{

    /**
     * 创建支付单
     *
     * @param Order $order 要支付的订单实例
     * @param int $userId 用户ID
     * @param string $platform 支付平台
     * @param string $client 支付客户端类型
     * @param array $extra 对应平台客户端额外的扩展信息
     * @return array{payment: Payment, parameters: array}
     */
    public static function payment(Order $order, $userId, $platform, $client, $extra=[])
    {
        if ($order->status != Order::STATUS_UNPAID) {
            throw new ServiceException('该订单状态无法发起支付。');
        }

        $amount = $order->total_amount;

        //尝试复用该订单已有的支付单
        $payment = Payment::query()
            ->where('order_id', $order->id)
            ->where('platform', $platform)
            ->where('client', $client)
            ->where('status', Payment::STATUS_UNPAID)
            ->where('amount', $amount)
            ->where('created_at', '>', now()->subHour())
            ->first();

        //创建新的支付单
        if (!$payment) {
            //使用订单编号作为支付单编号，重复则加后缀
            $outTradeNo = $order->order_no;
            $i = 0;

            while (Payment::query()->where('out_trade_no', $outTradeNo)->exists()) {
                $suffix = ++$i;
                if ($suffix > 99) {
                    throw new ServiceException('该订单尝试支付次数过多，无法继续支付，请重新下单。');
                }
                $outTradeNo = $outTradeNo.'-'.$suffix;
            }

            $payment = new Payment();
            $payment->user_id = $userId;
            $payment->order_id = $order->id;
            $payment->out_trade_no = $outTradeNo;
            $payment->platform = $platform;
            $payment->client = $client;
            $payment->amount = $amount;

            // 非生产环境，支付金额处理
            if (strtolower(config('app.env')) !== 'production') {
                $payment->amount = '0.01';
            } elseif (in_array($userId, config('heguibao.test_user_ids', []))) {
                $payment->amount = '0.02';
            }

            $payment->save();
        }

        $order->payment_id = $payment->id;
        $order->save();

        // 是否服务商模式
        $isServiceMode = $order->wechat_pay_mode === 'service';
        $subMchId = $order->sub_mch_id;

//        dd($isServiceMode, $subMchId);
        $parameters = null;

        //生成支付系统参数
        switch ($platform) {
            //微信支付
            case Payment::PLATFORM_WECHAT:
                $order = [
                    'out_trade_no' => $payment->out_trade_no,
                    'description' => $order->title,
                    'amount' => [
                        'total' => intval(bcmul($payment->amount, '100', 2)),
                    ],
                ];

                switch ($client) {
                    case Payment::CLIENT_MP:
                        if (!isset($extra['openid'])) {
                            throw new ServiceException('缺少 openid，无法发起支付。');
                        }
                        $openidKey = $isServiceMode ? 'sp_openid' : 'openid';
                        $order['payer'] = [
                            $openidKey => $extra['openid']
                        ];

                        break;
                    default:
                        throw new ServiceException('暂不支持指定的支付客户端。');
                }

                // 判断支付模式取对应配置进行实例化处理
                if ($isServiceMode) {
                    $wechatServicePayConfig = self::getWechatServicePayConfig($subMchId);
                    Pay::config($wechatServicePayConfig);
                    Pay::set(LoggerInterface::class, Log::channel('pay'));
                    $parameters = Pay::wechat()->mini($order)->toArray();
                } else {
                    $parameters = self::wechat()->mini($order)->toArray();
                }
                break;

            default:
                throw new ServiceException('暂不支持指定的支付平台。');
        }

        return compact('payment', 'parameters');
    }

    /**
     * 初始化支付组件
     * @throws \Yansongda\Pay\Exception\ContainerException
     */
    private static function init()
    {
        static $initialized = false;

        if (!$initialized) {
            Pay::config(config('pay'));

            //使用框架的日志代替掉组件内部的日志，格式统一
            Pay::set(LoggerInterface::class, Log::channel('pay'));

            $initialized = true;
        }
    }

    /**
     * 获取支付组件支付宝实例
     * @return \Yansongda\Pay\Provider\Alipay
     * @throws \Yansongda\Pay\Exception\ContainerException
     */
    public static function alipay()
    {
        self::init();
        return Pay::alipay();
    }

    /**
     * 获取支付组件微信支付实例
     * @return \Yansongda\Pay\Provider\Wechat
     * @throws \Yansongda\Pay\Exception\ContainerException
     */
    public static function wechat()
    {
        self::init();
        return Pay::wechat();
    }

    /**
     * 完成支付与订单处理
     *
     * @param string $outTradeNo 支付单编号
     * @param string $transactionId 支付平台交易单号
     * @param string $payAmount 实际支付金额
     * @param Carbon|null $paymentAt 支付时间
     * @return void
     */
    public static function complete($outTradeNo, $transactionId, $payAmount, ?Carbon $paymentAt=null)
    {
        /** @var Payment $payment */
        $payment = Payment::query()
            ->where('out_trade_no', $outTradeNo)
            ->first();

        if (!$payment || $payment->status != Payment::STATUS_UNPAID) {
            throw new ServiceException("支付单 $outTradeNo 不存在或状态无法继续支付。");
        }

        /** @var Order $order */
        $order = Order::find($payment->order_id);

        if (!$order) {
            throw new ServiceException("支付单 $outTradeNo 的订单不存在。");
        }

        $modelClass = $order->business_type->modelClass();

        //必须在 BusinessType 中为对应资源类型定义了 modelClass 才能进行处理
        if (!$modelClass) {
            throw new ServiceException("订单 {$order->order_no} 资源类型未知。");
        }

        if ($order->business_id) {
            $orderable = $modelClass::find($order->business_id);
        } else {
            $orderable = new $modelClass();
        }

        //对应类型的 modelClass 必须实现了 OrderableInterface 才能进行处理
        if (!$orderable instanceof OrderableInterface) {
            throw new ServiceException("订单 {$order->order_no} 无法进行处理。");
        }

        if ($paymentAt === null) {
            $paymentAt = now();
        }

        DB::beginTransaction();

        try {
            if ($orderable->orderDelivery($order)) {
                $order->payment_amount = $payAmount;
                $order->status = Order::STATUS_PAID;
                $order->payment_method = Order::PAY_METHOD_PAY;
                $order->payment_id = $payment->id;
                $order->payment_at = $paymentAt;
                $order->save();

                $payment->transaction_no = $transactionId;
                $payment->payment_at = $paymentAt;
                $payment->status = Payment::STATUS_PAID;
                $payment->save();

                DB::commit();
            } else {
                throw new ServiceException('支付处理失败，请稍候再试。');
            }
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取微信支付服务商模式配置
     * @param $subMchId
     * @return array
     */
    public static function getWechatServicePayConfig($subMchId): array
    {
        $encodeSubMchId = (new Sqids(minLength: 6))->encode([$subMchId]);
        if (strtolower(config('app.env')) === 'local') {
            $notifyUrl = 'https://b256166b4681.ngrok-free.app/api/payments/notify-service/wechat/' . $encodeSubMchId;
        } else {
            $notifyUrl = config('app.url') . '/api/payments/notify-service/wechat/' . $encodeSubMchId;
        }
        $wechatServicePayConfig = config('wechat_service_pay');
        $wechatServicePayConfig['wechat']['default']['sub_mch_id'] = (string)$subMchId;
        $wechatServicePayConfig['wechat']['default']['notify_url'] = $notifyUrl;
        return $wechatServicePayConfig;
    }

    /**
     * 微信退款
     * @param array $params
     * @param int $sub_mch_id
     * @return \Yansongda\Supports\Collection
     * @throws \Yansongda\Artful\Exception\ContainerException
     * @throws \Yansongda\Artful\Exception\InvalidParamsException
     * @throws \Yansongda\Artful\Exception\ServiceNotFoundException
     */
    public static function wechatRefund(array $params, int $sub_mch_id = 0): \Yansongda\Supports\Collection
    {
        if ($sub_mch_id) {
            $wechatPayConfig = self::getWechatServicePayConfig($sub_mch_id);
            Pay::config($wechatPayConfig);
            Pay::set(LoggerInterface::class, Log::channel('pay'));
        } else {
            self::init();
        }
        return Pay::wechat()->refund($params);
    }
}
