<?php

namespace App\Services\Import;

use App\Exceptions\ServiceException;
use App\Models\Cms\Category;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePack;
use App\Models\OpenCourseBatch;
use App\Models\OpenCourseRecord;
use App\Models\Org;
use App\Models\Train\Topic;
use App\Models\User;
use App\Models\User\UserOwnContent;
use App\Models\User\UserOwnTopic;
use App\Services\OrgService;
use App\Services\User\LoginService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToArray;

class FirstSheetCourseImport implements ToArray
{
    /**
     * @param array $rows
     * @return void
     * @throws \Throwable
     */
    public function array(array $rows): void
    {
        try {
            DB::beginTransaction();

            $count = count($rows) - 2;
            // 保存批次
            $batch = $this->saveBatch($count);

            foreach ($rows as $k => $row) {
                if ($k == 1) {
                    // 检查标题
                    $this->checkTitle($row);
                }
                if ($k > 1) {
                    $line = $k + 1;
                    // 开课
                    $this->save($row, $line, $batch->id);
                }
            }
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 开课
     *
     * @param array $row 列
     * @param int $line 行
     * @param int $batchId 批次ID
     * @return void
     */
    public function save(array $row, int $line, int $batchId): void
    {
        if (empty($row)) {
            return;
        }

        if (empty($row[0])) {
            throw new ServiceException("第 $line 行，姓名不能为空，导入失败");
        }

        if (empty($row[1])) {
            throw new ServiceException("第 $line 行，电话不能为空，导入失败");
        }

        if (!preg_match('/^1\d{10}$/', $row[1])) {
            throw new ServiceException("第 $line 行，电话格式错误，导入失败");
        }

        if (empty($row[2])) {
            throw new ServiceException("第 $line 行，组织机构不能为空，导入失败");
        }

        if (empty($row[4]) && empty($row[6]) && empty($row[8])) {
            throw new ServiceException("第 $line 行，课程ID和课程包ID和题库ID不能同时为空，导入失败");
        }

        if (!empty($row[6]) && (empty($row[7]) || !is_numeric($row[7]))) {
            throw new ServiceException("第 $line 行，题库有效期不能为空或非数字类型，导入失败");
        }
        if (!empty($row[4]) && (empty($row[5]) || !is_numeric($row[5]))) {
            throw new ServiceException("第 $line 行，课程有效期不能为空或非数字类型，导入失败");
        }
        if (!empty($row[8]) && (empty($row[9]) || !is_numeric($row[9]))) {
            throw new ServiceException("第 $line 行，课程包有效期不能为空或非数字类型，导入失败");
        }

        $importCourseIds = [];
        if (!empty($row[4])) {
            $importCourseIds = explode("+", $row[4]);
            if (!$this->isNumericArray($importCourseIds)) {
                throw new ServiceException("第 $line 行，课程ID $row[4] 非数字类型，导入失败");
            }
            $courseIds = ContentCourse::query()->whereIn('content_id', $importCourseIds)->pluck('content_id')->toArray();
            if (count($courseIds) !== count($importCourseIds)) {
                $diffIds = implode(',', array_diff($importCourseIds, $courseIds));
                throw new ServiceException("第 $line 行，课程ID $diffIds 对应课程不存在，导入失败");
            }
        }

        $importCoursePackageIds = [];
        if (!empty($row[8])) {
            $importCoursePackageIds = explode("+", $row[8]);
            if (!$this->isNumericArray($importCoursePackageIds)) {
                throw new ServiceException("第 $line 行，课程ID $row[8] 非数字类型，导入失败");
            }
            $coursePackageIds = ContentCoursePack::query()->whereIn('content_id', $importCoursePackageIds)->pluck('content_id')->toArray();
            if (count($coursePackageIds) !== count($importCoursePackageIds)) {
                $diffIds = implode(',', array_diff($importCoursePackageIds, $coursePackageIds));
                throw new ServiceException("第 $line 行，课程包ID $diffIds 对应课程不存在，导入失败");
            }
        }

        $importTopicIds = [];
        if (!empty($row[6])) {
            $importTopicIds = explode("+", $row[6]);
            if (!$this->isNumericArray($importTopicIds)) {
                throw new ServiceException("第 $line 行，题库ID $row[6] 非数字类型，导入失败");
            }
            $topicIds = Topic::query()->whereIn('id', $importTopicIds)->pluck('id')->toArray();
            if (count($topicIds) !== count($importTopicIds)) {
                $diffIds = implode(',', array_diff($importTopicIds, $topicIds));
                throw new ServiceException("第 $line 行，题库ID $diffIds 对应题库不存在，导入失败");
            }
        }

        $orgId = Org::query()->where('name', $row[2])->value('id');
        if (!$orgId) {
            $org = OrgService::create(['name' => $row[2]]);
            $orgId = $org->id;
        }

        $user = User::query()->where('phone', $row[1])->first();
        if (!$user) {
            $user = LoginService::createUser($row[1], '127.0.0.1', $row[0]);
        }
        if ($user->org_id != $orgId) {
            $user->join_org_at = now();
        }
        $user->org_id = $orgId;
        $user->save();

        // 保存记录
        $record = new OpenCourseRecord();
        $record->batch_id = $batchId;
        $record->user_id = $user->id;
        $record->org_id = $user->org_id;
        $record->course_ids = $importCourseIds;
        $record->topic_ids = $importTopicIds;
        $record->course_pack_ids = $importCoursePackageIds;
        $record->save();

        // 开通课程
        foreach ($importCourseIds as $courseId) {
            UserOwnContent::query()->updateOrCreate([
                'user_id' => $user->id,
                'content_id' => $courseId,
                'classify' => Category::CLASSIFY_COURSE
            ],[
                'expired_at' => Carbon::now()->addMonths($row[5])->toDateTimeString()
            ]);
        }
        // 开通课程包
        foreach ($importCoursePackageIds as $packageId) {
            UserOwnContent::query()->updateOrCreate([
                'user_id' => $user->id,
                'content_id' => $packageId,
                'classify' => Category::CLASSIFY_COURSE_PACK
            ],[
                'expired_at' => Carbon::now()->addMonths($row[9])->toDateTimeString()
            ]);
        }

        // 开通题库
        foreach ($importTopicIds as $topicId) {
            UserOwnTopic::query()->updateOrCreate([
                'user_id' => $user->id,
                'topic_id' => $topicId
            ], [
                'expired_at' => Carbon::now()->addMonths($row[7])->toDateTimeString()
            ]);
        }
    }

    /**
     * 创建批次
     *
     * @param int $count
     * @return OpenCourseBatch
     */
    public function saveBatch(int $count): OpenCourseBatch
    {
        $batch = new OpenCourseBatch();
        $batch->count = $count;
        $batch->admin_id = auth()->id();
        $batch->save();

        return $batch;
    }

    public function checkTitle(array $row): void
    {
        if ($row[0] != '姓名') {
            throw new ServiceException("姓名 区域不存在");
        }
        if ($row[1] != '电话') {
            throw new ServiceException("电话 区域不存在");
        }
        if ($row[2] != '所属机构') {
            throw new ServiceException("所属机构 区域不存在");
        }
        if ($row[3] != '课程类别') {
            throw new ServiceException("课程类别 区域不存在");
        }
        if ($row[4] != '课程ID') {
            throw new ServiceException("课程ID 区域不存在");
        }
        if ($row[5] != '课程有效期（月）') {
            throw new ServiceException("课程有效期（月） 区域不存在");
        }
        if ($row[6] != '题库ID') {
            throw new ServiceException("题库ID 区域不存在");
        }
        if ($row[7] != '题库有效期（月）') {
            throw new ServiceException("题库有效期（月） 区域不存在");
        }
        if ($row[8] != '课程包ID') {
            throw new ServiceException("课程包ID 区域不存在");
        }
        if ($row[9] != '课程包有效期（月）') {
            throw new ServiceException("课程包有效期（月） 区域不存在");
        }

    }

    public function isNumericArray($array)
    {
        return collect($array)->every(function ($value) {
            return is_numeric($value);
        });
    }
}
