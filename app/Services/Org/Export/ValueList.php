<?php

namespace App\Services\Org\Export;

use Illuminate\Contracts\Support\Arrayable;

class ValueList implements \Iterator, Arrayable, \Countable
{

    private \Iterator $iterator;

    /**
     * 可遍历的列表
     *
     * @param \Iterator $items 输入的惰性集合
     * @param \Closure|null $transform 对集合中的每一项的转换函数，转换为 ValueItem
     */
    public function __construct(private \Iterator|\IteratorAggregate|array $items, private ?\Closure $transform)
    {
        if ($items instanceof \IteratorAggregate) {
            $this->iterator = $items->getIterator();
        } elseif (is_array($items)) {
            $this->iterator = new \ArrayIterator($items);
        } else {
            $this->iterator = $items;
        }
    }

    public function toArray()
    {
        if (is_array($this->items)) {
            return $this->items;
        }
        return iterator_to_array($this);
    }

    public function count(): int
    {
        if (is_array($this->items)) {
            return count($this->items);
        }
        return iterator_count($this);
    }

    /**
     * @return array<array-key, ValueItem>|ValueItem
     */
    public function current(): mixed
    {
        $item = $this->iterator->current();
        return $this->transform !== null ? ($this->transform)($item) : $item;
    }

    public function next(): void
    {
        $this->iterator->next();
    }

    public function key(): mixed
    {
        return $this->iterator->key();
    }

    public function valid(): bool
    {
        return $this->iterator->valid();
    }

    public function rewind(): void
    {
        $this->iterator->rewind();
    }

}
