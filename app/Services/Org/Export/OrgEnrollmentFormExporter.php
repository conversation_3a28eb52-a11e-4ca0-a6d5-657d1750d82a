<?php
/**
 * OrgEnrollmentFormExporter.php class file.
 *
 * <AUTHOR>
 * @time 2025/4/8 15:49
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Services\Org\Export;

use App\Models\Org\Enrollment;
use App\Models\Org\Export;
use App\Services\Org\ExportService;

class OrgEnrollmentFormExporter implements ExporterInterface
{
    use ExportTemplateReplacer;

    public function __construct(
        private readonly int $orgId,
        public int $checkedId, // 勾选的报名学员ID
        array $extra
    ){}

    public static function validateRules(): array
    {
        return [];
    }

    public function generateDesc(): string
    {
        // 批量下载
        if ($this->checkedId == 0) {
            return '报名表';
        }

        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()
            ->where('id', $this->checkedId)
            ->first();


        // 报名表-学生姓名
        return "报名表-{$enrollment->getStudent()->name}";
    }

    public function handle(): array
    {
        /** @var Enrollment $enroll */
        $enroll = Enrollment::query()
            ->with([
                'student',
                'archive'
            ])
            ->where('id', $this->checkedId)
            ->first();

        $student = $enroll?->toStudent();

        //基础信息
        $items = [
            ValueItem::text('学号', $enroll->number),
            ValueItem::text('姓名', $enroll->student->name),
            ValueItem::text('手机号', $enroll->student->phone),
            ValueItem::text('身份证号', $enroll->student->id_card_number),
            ValueItem::image('照片', $enroll->student->photo, private: true),
        ];

        //报名表扩展信息
        if ($student->extra) {
            $extra = ExportService::parseExtra($this->orgId, $student->extra);
            $items = array_merge($items, $extra);
        }

        return $this->generateFromTemplate($this->orgId, Export::TYPE_ORG_ENROLLMENT_FORM, $items);
    }
}
