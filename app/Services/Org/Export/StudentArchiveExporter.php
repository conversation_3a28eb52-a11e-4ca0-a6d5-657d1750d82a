<?php

namespace App\Services\Org\Export;
use App\Exceptions\ServiceException;
use App\Models\Org;
use App\Models\Org\Enrollment;
use App\Models\Org\OrgClass;
use App\Models\Org\Template;
use App\Services\Org\EnrollmentService;
use Carbon\Carbon;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\GifWriter;
use Illuminate\Support\Facades\Log;

/**
 * 学生档案导出处理器
 */
class StudentArchiveExporter implements ExporterInterface
{
    use ExportTemplateReplacer;

    public function __construct(
        private readonly int $orgId,
        public int $checkedId, // 勾选的班级ID
        array $extra
    ){}

    public function handle(): array
    {
        $logger = Log::channel('task');
        $logger->info('开始导出学生档案', [
            'org_id' => $this->orgId,
            'checked_id' => $this->checkedId,
        ]);


        /** @var OrgClass $class */
        $class = OrgClass::query()
            ->where('org_id', $this->orgId)
            ->where('id', $this->checkedId)
            ->first();

        if (!$class) {
            throw new ServiceException("班级信息不存在");
        }

        if ($class->status != OrgClass::STATUS_FINISHED) {
            throw new ServiceException("ID为 $this->checkedId 的班级未结束");
        }

        // 填充数据
        $trainTime = $class->start_at && $class->end_at
            ? Carbon::parse($class->start_at)->format('Y年m月d日') . ' - ' . Carbon::parse($class->end_at)->format('Y年m月d日')
            : '';
        $items = [
            ValueItem::text('班级名称', $class->name),
            ValueItem::text('参培人数', $class->total_enrollments),
            ValueItem::text('负责老师', $class->manager->real_name),
            ValueItem::text('培训时间', $trainTime)
        ];

        $EnrollmentBuilder = Enrollment::query()
            ->with([
                'resource' => fn ($query) => $query->publicfields(),
                'student',
                'archive'
            ])
            ->where('org_id', $this->orgId)
            ->where('class_id', $this->checkedId)
            ->where('resource_id', '>', 0)
            ->whereIn('type', [Enrollment::TYPE_COURSE, Enrollment::TYPE_COURSE_PACK]);

        $items[] = ValueItem::list('课时记录', $EnrollmentBuilder->lazy(100), function(Enrollment $enrollment) {
            [$hour, $studyHour, $score, $testLevel, $finish] = EnrollmentService::userStudyInfo($enrollment);
            //二维码
            $qrCode = new QrCode($enrollment->study_record_url, size: 100);
            $writer = new GifWriter();
            $result = $writer->write($qrCode);
            $enrollment->toStudent();

            return [
                ValueItem::text('姓名', $enrollment->student->name),
                ValueItem::text('手机号码', $enrollment->student->phone),
                ValueItem::text('身份证号', $enrollment->student->id_card_number),
                ValueItem::text('工作单位', EnrollmentService::getExtraValue($enrollment, '工作单位')),
                ValueItem::text('课程', $enrollment->resource?->content?->title ?? ''),
                ValueItem::text('应修学时', $hour),
                ValueItem::text('已修学时', $studyHour),
                ValueItem::text('考试成绩', $score),
                ValueItem::text('是否及格', $testLevel),
                ValueItem::text('完成情况', $finish),
                ValueItem::imageDataURI('学习记录', $result->getDataUri()),
            ];
        });

        // 生成临时文件
        return $this->generateFromTemplate($this->orgId, Template::TYPE_STUDENT_ARCHIVE, $items);
    }

    public static function validateRules(): array
    {
        return [];
    }

    public function generateDesc(): string
    {
        $className = OrgClass::query()->where('id', $this->checkedId)->value('name');

        if (!$className) {
            $className = '一期一档';
        }

        return "一期一档-{$className}";
    }
}
