<?php

namespace App\Services\Org\Export;

use App\Exceptions\ServiceException;
use App\Models\Org\Export;
use App\Models\Org\Template;
use App\Services\Org\TemplateReplace\ReplaceService;

/**
 * 数据模板导出替换器
 */
trait ExportTemplateReplacer
{

    /**
     * 从导出的数据源和模板生成最终文件
     *
     * @param int $orgId
     * @param string $type
     * @param ValueItem[] $items
     * @return array
     */
    protected function generateFromTemplate($orgId, $type, $items)
    {
        $template = Template::query()
            ->where('org_id', $orgId)
            ->where('type', $type)
            ->where('is_default', true)
            ->first();

        if ($template) {
            $tpl = $template->tpl_path_url;
        } else {
            //机构没有设置模板时使用平台的默认模板
            $defaultTemplates = config('org.export.default_templates');
            if (!isset($defaultTemplates[$type])) {
                throw new ServiceException("未找到".Export::getTypeName($type)."模板");
            }
            $tpl = public_path($defaultTemplates[$type]);
        }

        $filePath = ReplaceService::replace($tpl, $items);
        $fileName = $this->generateDesc();

        return [$fileName, $filePath];
    }

}
