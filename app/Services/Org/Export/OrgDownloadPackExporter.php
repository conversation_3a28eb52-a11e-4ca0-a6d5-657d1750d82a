<?php
/**
 * OrgDownloadPackExporter.php class file.
 *
 * <AUTHOR>
 * @time 2025/4/14 18:17
 * @copyright 2025 pp.cc All Right Reserved
 */

namespace App\Services\Org\Export;

use App\Exceptions\ServiceException;
use App\Models\Org\Enrollment;
use App\Models\Org\Export;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\File;

class OrgDownloadPackExporter implements ExporterInterface
{

    /**
     * @inheritDoc
     */
    public function __construct(
        private readonly int $orgId,
        public int $enrollId, // 勾选的报名学员ID
        public array $extra
    ){}

    /**
     * @inheritDoc
     */
    public static function validateRules(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function generateDesc(): string
    {
        // 批量下载
        if ($this->enrollId == 0) {
            return '打包下载包';
        }

        $enrollment = Enrollment::query()
            ->with(['classroom' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('id', $this->enrollId)
            ->first();

        // 打包下载包-班级名称-学生姓名
        return "打包下载包-{$enrollment->classroom->name}-{$enrollment->getStudent()->name}";
    }

    /**
     * @inheritDoc
     */
    public function handle(): array|null
    {
        // 打包哪些下载类型
        // 下载学时证明、学习记录、考试试卷、报名表放在一个以个人姓名命名的文件夹中
        $types = [
            Export::TYPE_HOUR_CERT => '学时证明',
            Export::TYPE_HOUR_RECORD => '学习记录',
            Export::TYPE_TEST_PAPER => '考试试卷',
            Export::TYPE_ORG_ENROLLMENT_FORM => '报名表',
        ];

        // 创建个人姓名的文件夹
        /** @var Enrollment $enrollment */
        $enrollment = Enrollment::query()
            ->with(['student', 'archive'])
            ->where('id', $this->enrollId)
            ->first();

        if (!$enrollment) {
            throw new ServiceException("学员 $this->enrollId 不存在。");
        }

        $enrollment->toStudent();

        // 获取临时文件夹目录
        $uniqueFolderName = "OrgDP_{$enrollment->id}_{$enrollment->student->name}_" . uniqid();
        $dirname = "{$enrollment->student->name}($enrollment->number)";
        $tmpDir = AttachmentService::tmpPath($uniqueFolderName);

        if (!File::isDirectory($tmpDir)) {
            File::makeDirectory($tmpDir, 0755, true);
        }

        foreach ($types as $type => $filename) {
            $result = ExportFactory::create($type, $this->orgId, $enrollment->id, $this->extra)->handle();

            if ($result) {
                list(, $filePath) = $result;
                $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                $fileToPath = "$tmpDir/$filename.$extension";
                File::copy($filePath, $fileToPath);
            }
        }

        return [$dirname, $tmpDir];
    }
}
