<?php

namespace App\Services\Org\Export;

use App\Services\Common\AttachmentService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ValueItem implements \IteratorAggregate
{

    /**
     * @param string $type 类型，text: 文本，date: 日期，image: 图像，options: 多选框，list: 可遍历列表
     * @param string $name
     * @param string|array|ValueList $value
     */
    private function __construct(public readonly string $type, public readonly string $name, public readonly array $value)
    {}

    /**
     * 构建文本字段
     *
     * @param string $name
     * @param string $text
     * @return self
     */
    public static function text($name, string $text): self
    {
        return new self('text', $name, compact('text'));
    }

    /**
     * 构建日期字段
     *
     * @param string $name
     * @param string $date
     * @return self
     */
    public static function date($name, string $date): self
    {
        return new self('date', $name, compact('date'));
    }

    /**
     * 构建图片字段
     *
     * @param string $name
     * @param string $path 文件存储路径
     * @param string|null $url
     * @param bool $private 是否是私有文件
     */
    public static function image($name, string $path, string $url=null, bool $private=false): self
    {
        return new self('image', $name, compact('path', 'url', 'private'));
    }

    /**
     * dataURI 图片
     *
     * @param string $name
     * @param string $dataURI Base64 格式的 DataURI，示例：data:image/png;base64,iVBORw
     * @return self
     */
    public static function imageDataURI($name, string $dataURI): self
    {
        return new self('image', $name, compact('dataURI'));
    }

    /**
     * 选项列表
     *
     * @param string $name
     * @param array $options 所有选项
     * @param string|array $selected 选中的选项
     * @return self
     */
    public static function options($name, array $options, string|array $selected): self
    {
        !is_array($selected) && $selected = [$selected];
        return new self('options', $name, compact('selected', 'options'));
    }

    /**
     * 列表格式
     *
     * @param string $name
     * @param \Iterator|\IteratorAggregate|array $items
     * @param \Closure|null $transform 对集合中的每一项的转换函数，转换为 ValueItem
     * @return self
     */
    public static function list($name, \Iterator|\IteratorAggregate|array $items, ?\Closure $transform=null): self
    {
        return new self('list', $name, compact('items', 'transform'));
    }

    private function checkType($type, $method)
    {
        if ($type !== $this->type) {
            $method = substr($method, strrpos($method, '::') + 2);
            throw new \RuntimeException("Type mismatch: {$this->name} 字段不适用于 ".$method);
        }
    }

    /**
     * 获取用于展示的文本
     */
    public function getText(): string
    {
        return $this->__toString();
    }

    public function __toString(): string
    {
        return match($this->type) {
            'text' => $this->value['text'],
            'image' => $this->getLocalPath(),
            'options' => $this->getOptionsText(),
            'date' => $this->getDateText(),
            default => ''
        };
    }

    /**
     * 获取图片本地路径（路径2小时左右有效）
     *
     * 如果图片下载失败，则会返回 false
     */
    public function getLocalPath(): string|false
    {
        $this->checkType('image', __METHOD__);

        if (isset($this->value['dataURI'])) {
            //解析 Base64 的 DataURI，生成文件和路径
            $pos = strpos($this->value['dataURI'], ',');
            $meta = substr($this->value['dataURI'], 0, $pos);
            $typeAsExtension = substr($meta, 11, -7);
            $base64 = substr($this->value['dataURI'], $pos + 1);

            $hash = md5($base64);
            $path = AttachmentService::tmpPath('image-value-'.$hash.'.'.$typeAsExtension);

            if (!is_file($path)) {
                file_put_contents($path, base64_decode($base64));
            }

            return $path;
        }

        if (isset($this->value['url'])) {
            $url = $this->value['url'];
        } elseif (isset($this->value['path'])) {
            $disk = $this->value['private'] ? config('heguibao.storage.priv') : config('heguibao.storage.pub');
            $url = AttachmentService::url(Storage::disk($disk), $this->value['path']);
        } else {
            throw new \RuntimeException('不支持的图片数据');
        }

        //确保相同的图片，文件名不变，临时文件存在时，复用文件
        $cacheName = $this->value['private'] ? 'image-url-private-'.md5($this->value['path']) : 'image-url-public-'.md5($this->value['path']);
        $path = AttachmentService::tmpPath($cacheName.'.'.pathinfo($this->value['path'], PATHINFO_EXTENSION));

        if (is_file($path)) {
            return $path;
        }

        try {
            $tmp = AttachmentService::download($url);
        } catch (\Throwable $e) {
            Log::error("下载图片文件 $url 失败：".$e->getMessage());
            return false;
        }

        if (!rename($tmp['local_path'], $path)) {
            throw new \RuntimeException('无法保存图片文件');
        }

        return $path;
    }

    /**
     * 获取选项列表
     *
     * @return array{text:string, selected:bool}[]
     */
    public function getOptions()
    {
        $this->checkType('options', __METHOD__);

        $arr = [];

        foreach ($this->value['options'] as $text) {
            $arr[] = ['text' => $text, 'selected' => in_array($text, $this->value['selected'])];
        }

        return $arr;
    }

    /**
     * 获取多选框的文本表达
     */
    private function getOptionsText(): string
    {
        $arr = [];

        foreach ($this->value['options'] as $text) {
            $selected = in_array($text, $this->value['selected']);
            $arr[] = '['.($selected ? '✔' : ' ').']'.$text;
        }

        return join('  ', $arr);
    }

    private function getDateText(): string
    {
        return $this->value['date'] ? Carbon::createFromTimestamp(strtotime($this->value['date'].' 00:00:00'))->format('Y年n月j日') : '';
    }

    public function getIterator(): \Traversable
    {
        $this->checkType('list', __METHOD__);
        return new ValueList($this->value['items'], $this->value['transform']);
    }
}
