<?php

namespace App\Services\Org;

use App\Exceptions\ServiceException;
use App\Models\Cms\ContentCourse;
use App\Models\Org\OrgClass;
use App\Models\Org\Topic;
use App\Models\Train\Topic as TrainTopic;
use Carbon\Carbon;

class TopicService
{
    /**
     * 添加机构题库
     *
     * @param int $oid
     * @param array $topicIds
     * @param array $topicsData
     * @return bool
     */
    public static function add(int $oid,  array $topicIds, array $topicsData)
    {
        $topics = [];
        $trainTopics = TrainTopic::query()->whereIn('id', $topicIds)->get()->keyBy('id');
        $orgTopicIds = Topic::query()
            ->select(['topic_id', 'org_id'])
            ->where('org_id', $oid)
            ->whereIn('topic_id', $topicIds)
            ->get()
            ->pluck('topic_id')
            ->toArray();

        foreach ($topicIds as $topicId) {
            if (!in_array($topicId, $orgTopicIds)) {
                $price30 = $topicsData[$topicId]['price30'] ?? 15;
                $price60 = $topicsData[$topicId]['price60'] ?? 25;
                $topics[] = [
                    'org_id' => $oid,
                    'topic_id' => $topicId,
                    'price_original_30' => $price30,
                    'price_original_60' => $price60,
                    'price_sell_30' => $price30,
                    'price_sell_60' => $price60,
                    'status' => Topic::STATUS_VISIBLE,
                    'exam_time' => $trainTopics[$topicId]->exam_time,
                    'pass_score' => $trainTopics[$topicId]->pass_score,
                    'exam_config' => isset($trainTopics[$topicId]->exam_config) ? json_encode($trainTopics[$topicId]->exam_config, JSON_UNESCAPED_UNICODE) : null,
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        }

        return empty($topics) || Topic::query()->insert($topics);
    }

    public static function update(int $id, int $orgId, array $params): Topic
    {
        /** @var Topic $topic */
        $topic = Topic::query()->where('org_id', $orgId)->find($id);

        if (!$topic) {
            throw new ServiceException('题库不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $topic->getFillable())) {
                $topic->{$key} = $val;
            }
        }

        $topic->save();

        return $topic;
    }

    /**
     * 删除机构题库
     *
     * @param int $oid
     * @param array $topicIds
     * @return mixed
     */
    public static function delete(int $oid, array $topicIds)
    {
        return Topic::query()
            ->where('org_id', $oid)
            ->whereIn('topic_id', $topicIds)
            ->delete();
    }

    /**
     * 更新机构题库价格
     *
     * @param int $orgId
     * @param array $topicsData
     * @return void
     */
    public static function updatePrices(int $orgId, array $topicsData)
    {
        // // 获取当前机构的所有题库ID
        //        $currentTopicIds = Topic::query()
        //            ->where('org_id', $oid)
        //            ->pluck('topic_id')
        //            ->toArray();
        //
        //        // 获取请求中的题库ID列表
        //        $newTopicIds = array_column($params['topics'], 'id');
        //
        //        // 需要删除的题库
        //        $deleteTopicIds = array_diff($currentTopicIds, $newTopicIds);
        //        if (!empty($deleteTopicIds)) {
        //            TopicService::delete($oid, $deleteTopicIds);
        //        }
        //
        //        // 需要添加的题库
        //        $coursesData = collect($params['topics'])->keyBy('id')->map(function($course) {
        //            return [
        //                'price' => $course['price']
        //            ];
        //        })->all();
        //
        //        $insertCourseIds = array_diff($newTopicIds, $currentTopicIds);
        //        if (!empty($insertCourseIds)) {
        //            $insertCourses = array_intersect_key($coursesData, array_flip($insertCourseIds));
        //            TopicService::add($oid, $insertCourseIds, $insertCourses);
        //        }
        //
        //        // 更新现有课程的价格
        //        $updateCourseIds = array_intersect($newTopicIds, $currentTopicIds);
        //        if (!empty($updateCourseIds)) {
        //            $updateCourses = array_intersect_key($coursesData, array_flip($updateCourseIds));
        //            TopicService::updatePrices($oid, $updateCourses);
        //        }
        //
        //        return $this->success();


        foreach ($topicsData as $topicId => $data) {
            Topic::query()->where('org_id', $orgId)
                ->where('course_id', $topicId)
                ->update([
                    'price_original' => $data['price']
                ]);
        }
    }

    public static function getOrgTopics(int $orgId): array
    {
        $topicList = Topic::query()
            ->with(['topic'])
            ->where('org_id', $orgId)
            ->where('status', Topic::STATUS_VISIBLE)
            ->get();

        $topics = [];

        /** @var Topic $item */
        foreach ($topicList as $item) {
            $topics[] = [
                'id' => $item->id,
                'topic_id' => $item->topic_id,
                'name' => $item->topic->name,
                'price_original' => $item->price_original_30,
                'price_sell' => $item->price_sell_30,
            ];
        }

        return $topics;
    }

    /**
     * 检测并创建机构题库
     * @param int $orgId
     * @param TrainTopic $topic
     * @return bool
     */
    public static function checkCreate(int $orgId, TrainTopic $topic)
    {
        $orgTopicExists = Topic::query()
            ->where('org_id', $orgId)
            ->where('topic_id', $topic->id)
            ->exists();

        if ($orgTopicExists) {
            return false;
        }

        $orgTopic = new Topic();
        $orgTopic->org_id = $orgId;
        $orgTopic->topic_id = $topic->id;
        $orgTopic->status = Topic::STATUS_VISIBLE;
        $orgTopic->price_original_30 = $topic->amount > 0 ? $topic->amount : 15;
        $orgTopic->price_original_60 = $topic->amount > 0 ? $topic->amount * 2 : 25;
        $orgTopic->price_sell_30 = $topic->amount > 0 ? $topic->amount : 15;
        $orgTopic->price_sell_60 = $topic->amount > 0 ? $topic->amount * 2 : 25;
        $orgTopic->exam_time = $topic->exam_time;
        $orgTopic->pass_score = $topic->pass_score;
        $orgTopic->exam_config = $topic->exam_config;
        return $orgTopic->save();
    }

    /**
     * 获取及格分数
     * @param OrgClass $class 班级
     * @return int
     */
    public static function getPassScore(OrgClass $class): int
    {
        if ($class->type != 'topic') {
            $topic = $class->resource->topic;
            $topicId = $topic?->id;
        } else {
            $topicId = $class->resource_id;
            $topic = $class->resource;
        }

        /** @var Topic $orgTopic */
        $orgTopic = Topic::query()->where('org_id', $class->org_id)->where('topic_id', $topicId)->first();

        if ($orgTopic) {
            $passScore = $orgTopic->pass_score;
        } else {
            if (!$topic) {
                throw new ServiceException('题库不存在');
            }

            $passScore = $topic->pass_score;
        }

        return $passScore;
    }
}
