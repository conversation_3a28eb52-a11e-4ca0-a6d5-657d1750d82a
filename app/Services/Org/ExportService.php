<?php

namespace App\Services\Org;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Org\EnrollmentForm;
use App\Models\Org\Export;
use App\Services\Admin\AttachmentRelationService;
use App\Services\Org\Export\ValueItem;

class ExportService
{
    public static function remove($id, int $orgId): void
    {
        /** @var Export $export */
        $export = Export::query()->where('org_id', $orgId)->find($id);

        if (!$export) {
            throw new ServiceException('下载记录不存在');
        }

        $export->delete();

        AttachmentRelationService::removeAttachment(BusinessType::Export, [$id]);
    }

    /**
     * 将用户的报名表扩展信息解析成用于导出数据的 ValueItems[] 结构
     *
     * @param int $orgId
     * @param array $extra
     * @return ValueItem[]
     */
    public static function parseExtra(int $orgId, array $extra)
    {
        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::query()
            ->where('org_id', $orgId)
            ->first();

        $fieldsById = [];
        $fieldsByName = [];

        foreach ($form->fields as $row) {
            $fieldsById[$row['id']] = $row;
            $fieldsByName[$row['name']] = $row;
        }

        $items = [];

        foreach ($extra as $item) {
            $form = $fieldsById[$item['id']] ?? $fieldsByName[$item['name']] ?? [];
            $config = $form['config'] ?? [];

            $items[] = match ($item['type']) {
                'text', 'textarea', 'radio', 'work_unit' => ValueItem::text($item['name'], $item['value']),
                'date' => $item['value'] ? ValueItem::date($item['name'], $item['value']) : ValueItem::text($item['name'], ''),
                'select', 'checkbox' => ValueItem::options($item['name'], $config['options'] ?? (is_array($item['value']) ? $item['value'] : [$item['value']]), $item['value']),
                'region' => ValueItem::text($item['name'], $item['display_text'] ?? $item['value']),
                'cascade' => ValueItem::text($item['name'], $item['display_text'] ?: implode('->', $item['value'])),
                'sign' => ValueItem::imageDataURI($item['name'], $item['value']),
                'photo', 'pic' => ValueItem::image($item['name'], $item['value'][0]['path'], private: true), //Todo: 多图支持
                default => ValueItem::text($item['name'], '') //文件暂不在此处支持
            };
        }

        return $items;
    }

}
