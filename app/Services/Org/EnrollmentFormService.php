<?php

namespace App\Services\Org;

use App\Exceptions\ServiceException;
use App\Models\Org\EnrollmentForm;

class EnrollmentFormService
{
    public static function update(int $orgId, string $title, array $fields): EnrollmentForm
    {
        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::query()->where('org_id', $orgId)->first();

        if (!$form) {
            $form = new EnrollmentForm();
            $form->org_id = $orgId;
        }

        $form->title = $title;
        $form->fields = $fields;
        $form->save();

        return $form;
    }

    /**
     * 表单验证
     *
     * @param int $orgId
     * @param array $extras 扩展表单项
     * @return void
     */
    public static function validateForm(int $orgId, array $extras): void
    {
        /** @var EnrollmentForm $form */
        $form = EnrollmentForm::publicFields()->where('org_id', $orgId)->first();

        if (!$form) {
            return;
        }

        $fields = array_column($form->fields, null, 'id');

        if (array_diff(array_keys($extras), array_keys($fields))) {
            throw new ServiceException("表单项异常");
        }

        foreach ($extras as $id => $value) {
            $type = $fields[$id]['type'];
            $config = $fields[$id]['config'] ?? [];
            $name = $fields[$id]['name'];
            $required = $fields[$id]['required'];

            if (empty($value) && $required) {
                throw new ServiceException("{$name}不能为空");
            }

            switch ($type) {
                case EnrollmentForm::TYPE_TEXT:
                case EnrollmentForm::TYPE_WORK_UNIT:
                case EnrollmentForm::TYPE_CASCADE:
                case EnrollmentForm::TYPE_TEXTAREA:
                case EnrollmentForm::TYPE_DATE:
                case EnrollmentForm::TYPE_SIGN:
                case EnrollmentForm::TYPE_PHOTO:
                    $min = $config['min'] ?? null;
                    $max = $config['max'] ?? null;
                    if ($min && $max) {
                        if (mb_strlen($value) < $min || mb_strlen($value) > $max) {
                            throw new ServiceException("{$name}长度不能小于{$min}位且不能大于{$max}位");
                        }
                    }
                    break;
                case EnrollmentForm::TYPE_PIC:
                case EnrollmentForm::TYPE_FILE:
                    if (!is_array($value) || count($value) > $config['limit']) {
                        throw new ServiceException("{$name}数量不能超过{$config['limit']}");
                    }
                    break;
                case EnrollmentForm::TYPE_SELECT:
                case EnrollmentForm::TYPE_RADIO:
                    if (!in_array($value, $config['options'])) {
                        throw new ServiceException("{$name}值异常");
                    }
                    break;
                case EnrollmentForm::TYPE_CHECKBOX:
                    foreach ($value as $option) {
                        if (!in_array($option, $config['options'])) {
                            throw new ServiceException("{$name}值异常");
                        }
                    }
                    break;
                case EnrollmentForm::TYPE_REGION:
                    if (empty($value['area_code']) || empty($value['area_text'])) {
                        throw new ServiceException("{$name}值异常");
                    }
                    break;
                default:
                    throw new ServiceException("表单项类型异常");
            }
        }
    }
}
