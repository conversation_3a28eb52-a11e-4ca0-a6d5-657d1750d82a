<?php

namespace App\Services\Org;

use App\Exceptions\ServiceException;
use App\Models\Org;
use App\Models\Org\BalanceRecord;
use Illuminate\Support\Facades\DB;

/**
 * 机构余额服务
 */
class BalanceService
{

    /**
     * 收入余额（充值）
     *
     * @param int $orgId 机构ID
     * @param int|float|string $amount 要增加的金额(精确到两位小数点，小数时请传字符串)
     * @param int $enrollId 报名 ID
     * @param string $remark 备注
     * @return BalanceRecord 产生的余额记录
     * @throws \Throwable
     */
    public static function recharge($orgId, $amount, $enrollId=0, $remark=''): BalanceRecord
    {
        return self::update($orgId, $amount, BalanceRecord::TYPE_INCOME, $enrollId, $remark);
    }

    /**
     * 支出余额（消费）
     *
     * @param int $orgId 机构ID
     * @param int|float|string $amount 要减少的金额（精确到两位小数点）
     * @param int $enrollId 报名 ID
     * @param string $remark 备注
     * @return BalanceRecord 产生的余额记录
     * @throws \Throwable
     */
    public static function consume($orgId, $amount, $enrollId = 0, $remark=''): BalanceRecord
    {
        return self::update($orgId, $amount, BalanceRecord::TYPE_EXPENSE, $enrollId, $remark);
    }

    /**
     * 退款
     *
     * 将关联的支出记录标记为已退款状态，并新增一条退款的收入记录
     *
     * @param int $orgId 机构ID
     * @param int $enrollId 报名 ID
     * @param string|null $remark 更新备注，null 则保持不变
     * @return BalanceRecord 被退款的记录
     * @throws \Throwable
     */
    public static function refund($orgId, $enrollId, $remark=null): BalanceRecord
    {
        /** @var BalanceRecord $expenseRecord */
        $expenseRecord = BalanceRecord::query()
            ->where('enroll_id', $enrollId)
            ->where('org_id', $orgId)
            ->where('type', BalanceRecord::TYPE_EXPENSE)
            ->where('status', BalanceRecord::STATUS_NORMAL)
            ->first();

        if (!$expenseRecord) {
            throw new ServiceException('未找到收款记录或已退款，无法继续退款。');
        }

        $rr = self::update($orgId, abs($expenseRecord->amount), BalanceRecord::TYPE_REFUND, $enrollId, $remark, $expenseRecord->id, false);

        $expenseRecord->status = BalanceRecord::STATUS_REFUND;
        $expenseRecord->ref_id = $rr->id;
        $expenseRecord->save();

        DB::commit();

        return $expenseRecord;
    }

    /**
     * 变更积分
     *
     * @param int $orgId
     * @param int $amount
     * @param string $type
     * @psalm-param "income"|"expenses"|"refund" $type 变更类型，income 收入，expenses 支出，refund 退款（退款的收入）
     * @param int $enrollId 关联的报名ID
     * @param string $remark
     * @param int $autoCommit 是否自动提交，为 false 时调用处加上自己的数据库操作再一起提交
     * @param int $refId 关联的记录 ID（比如发生退款记录的ID）
     * @return BalanceRecord
     * @throws ServiceException|\Throwable
     */
    public static function update($orgId, $amount, $type, $enrollId, $remark='', $refId=0, $autoCommit=true)
    {
        if ($amount == 0) {
            throw new ServiceException('积分没有变化。');
        }

        /** @var Org $org */
        $org = Org::query()
            ->where('id', $orgId)
            ->first();

        if ($type == BalanceRecord::TYPE_EXPENSE && $org->balance < $amount) {
            throw new ServiceException('余额不足。', 403);
        }

        $originBalance = $org->balance;
        $changeBalance = $type != BalanceRecord::TYPE_EXPENSE ? $amount : -$amount;

        DB::beginTransaction();

        try {
            //变更积分
            $type == BalanceRecord::TYPE_EXPENSE ? $org->decrement('balance', $amount) : $org->increment('balance', $amount);

            //新增记录
            $log = new BalanceRecord();
            $log->org_id = $orgId;
            $log->origin_balance = $originBalance;
            $log->amount = $changeBalance;
            $log->type = $type;
            $log->enroll_id = $enrollId;
            $log->remark = $remark;
            $log->ref_id = $refId;
            $log->save();

            $autoCommit && DB::commit();

            return $log;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw new ServiceException('更新余额失败。'.$e->getMessage(), previous: $e);
        }
    }

}
