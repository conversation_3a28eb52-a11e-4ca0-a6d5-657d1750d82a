<?php

namespace App\Services\Org;

use App\Models\Org\Course;
use App\Services\Cms\ContentCourseService;

class CourseService
{

    /**
     * 添加机构课程
     *
     * @param int $orgId
     * @param array $courseIds
     * @param array $coursesData
     * @return mixed
     */
    public static function add(int $orgId, array $courseIds, array $coursesData = [])
    {
        // 批量获取所有课程的课时
        $coursesHour = ContentCourseService::getCoursesHour($courseIds);

        foreach ($courseIds as $courseId) {
            $model = new Course();
            $model->org_id = $orgId;
            $model->course_id = $courseId;
            $model->price_original = $coursesData[$courseId]['price'] ?? 0;
            $model->price_sell = $coursesData[$courseId]['price'] ?? 0;
            $model->hour = $coursesHour[$courseId] ?? 0;
            $model->save();  // 只有 save 会触发 created 事件
        }

        return true;
    }

    /**
     * 更新机构课程价格
     *
     * @param int $orgId
     * @param array $coursesData
     * @return void
     */
    public static function updatePrices(int $orgId, array $coursesData)
    {
        foreach ($coursesData as $courseId => $data) {
            Course::query()->where('org_id', $orgId)
                ->where('course_id', $courseId)
                ->update([
                    'price_original' => $data['price']
                ]);
        }
    }

    /**
     * 课程信息
     *
     * @param int $orgId
     * @param int $courseId
     * @return Course|null
     */
    public static function getCourse(int $orgId, int $courseId): ?Course
    {
        return Course::publicFields()
            ->where('org_id', $orgId)
            ->where('course_id', $courseId)
            ->first();
    }

    public static function getOrgCourses(int $orgId): array
    {
        $list = Course::query()
            ->with(['contentCourse', 'contentCourse.content', 'contentCourse.topic' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('org_id', $orgId)
            ->where('status', Course::STATUS_VISIBLE)
            ->get();

        $courses = [];

        /** @var Course $item */
        foreach ($list as $item) {
            $topic = $item->contentCourse->topic;
            if ($topic) {
                $topic->setHidden([]);
            }

            $courses[] = [
                'id' => $item->id,
                'course_id' => $item->course_id,
                'name' => $item->contentCourse->content->title,
                'price_original' => $item->price_original,
                'price_sell' => $item->price_sell,
                'topic' => $topic,
            ];
        }

        return $courses;
    }
}
