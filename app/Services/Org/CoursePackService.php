<?php

namespace App\Services\Org;

use App\Models\Cms\ContentCoursePackList;
use App\Models\Org\Course;
use App\Models\Org\CoursePack;
use App\Services\Cms\ContentCourseService;

class CoursePackService
{
    /**
     * 添加机构课程
     *
     * @param int $orgId
     * @param array $coursePacks
     * @return void
     */
    public static function add(int $orgId, array $coursePacks = [])
    {
        // 批量获取所有课程的总时长
        foreach ($coursePacks as $coursePackId => $coursePack) {
            $model = new CoursePack();
            $model->org_id = $orgId;
            $model->course_pack_id = $coursePackId;
            $model->status = CoursePack::STATUS_VISIBLE;
            $model->price_original = $coursePack['price'] ?? 0;
            $model->price_sell = $coursePack['price'] ?? 0;
            $model->save();
        }
    }

    public static function getOrgCoursePacks(int $orgId): array
    {
        $list = CoursePack::query()
            ->with(['contentCoursePack', 'contentCoursePack.content', 'contentCoursePack.topic' => function ($query) {
                $query->select(['id', 'name']);
            }])
            ->where('org_id', $orgId)
            ->where('status', CoursePack::STATUS_VISIBLE)
            ->get();

        $courses = [];

        /** @var CoursePack $item */
        foreach ($list as $item) {
            $topic = $item->contentCoursePack->topic;
            if ($topic) {
                $topic->setHidden([]);
            }

            $courses[] = [
                'id' => $item->id,
                'course_pack_id' => $item->course_pack_id,
                'name' => $item->contentCoursePack->content->title,
                'price_original' => $item->price_original,
                'price_sell' => $item->price_sell,
                'topic' => $topic,
            ];
        }

        return $courses;
    }

    /**
     * 计算课程包课时
     *
     * @param int $orgId
     * @param int $coursePackId
     * @return int
     */
    public static function calCoursePackHour(int $orgId, int $coursePackId)
    {
        $courseIds = ContentCoursePackList::query()
            ->where('content_id', $coursePackId)
            ->pluck('course_id')
            ->toArray();
        $inOrgCourseIds = [];

        if ($orgId > 0) {
            $inOrgCourseIds = Course::query()
                ->where('org_id', $orgId)
                ->whereIn('course_id', $courseIds)
                ->pluck('course_id')
                ->toArray();
        }

        $notInOrgCourseIds = array_diff($courseIds, $inOrgCourseIds);

        $inCourseHour = 0;
        $notInCourseHour = 0;

        if (!empty($inOrgCourseIds)) {
            $inCourseHour = Course::query()
                ->where('org_id', $orgId)
                ->whereIn('course_id', $inOrgCourseIds)
                ->sum('hour');
        }

        if (!empty($notInOrgCourseIds)) {
            $coursesHour = ContentCourseService::getCoursesHour($notInOrgCourseIds);

            foreach ($coursesHour as $courseHour) {
                $notInCourseHour += $courseHour;
            }
        }

        return $inCourseHour + $notInCourseHour;
    }
}
