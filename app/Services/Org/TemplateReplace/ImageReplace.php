<?php

namespace App\Services\Org\TemplateReplace;

class ImageReplace extends ReplaceBase
{

    public function replace()
    {
        $search = $this->getSearchName();

        //放在闭包中时，只有在字段实际发生替换时才会调用
        $this->processor->setImageValue($search, function() {
            $path = $this->value->getLocalPath();

            if ($path !== false) {
                $imageData = @getimagesize($path);
                if (is_array($imageData)) {
                    return ['path' => $path];
                }
            }

            //图片没有或者加载失败，使用一个默认图代替
            return ['path' => public_path('static/images/image-not-exists.jpg')];
        });
    }

}
