<?php

namespace App\Services\Inspect;

use App\Exceptions\ServiceException;
use App\Models\Inspect\Device;
use App\Models\Inspect\TaskDevicesRecord;

/**
 * 巡检设备服务
 */
class InspectDeviceService
{
    /**
     * 创建设备
     *
     * @param string $name 设备名称
     * @param string $image_url 设备图片
     * @param string $remark 设备描述
     * @return Device
     */
    public function create(string $name, $image_url, $remark): Device
    {
        if (Device::query()->where('user_id', auth()->id())->where('name', $name)->exists()) {
            throw new ServiceException('该设备已存在');
        }

        $device = new Device();
        $device->user_id = auth()->id();
        $device->name = $name;
        $device->image_url = $image_url;
        $device->remark = $remark;
        $device->save();

        return $device;
    }

    public function update(int $id, array $params): Device
    {
        $device = Device::query()->find($id);
        if (!$device) {
            throw new ServiceException("设备不存在");
        }

        if (Device::query()->where('user_id', auth()->id())->where('name', $params['name'])->where('id', '!=', $device->id)->exists()) {
            throw new ServiceException('该设备名称已存在');
        }
        

        foreach ($params as $key => $val) {
            if (in_array($key, $device->getFillable())) {
                $device->{$key} = $val;
            }
        }

        $device->save();

        return $device;
    }

    /**
     * 通过设备ID获取设备巡检项
     * @param int $deviceID
     * @return array
     */
    public function getTaskDeviceItemsByDeviceID(int $deviceID): array
    {
        try {
            if (!$deviceID) {
                throw new ServiceException("设备ID不能为空");
            }
            $device = Device::query()->findOrFail($deviceID);
            return $device->deviceItem->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                ];
            })->toArray();
        } catch (ServiceException $e) {
            logger()->error("获取设备巡检项失败", [
                'user_id' => auth()->id(),
                'device_id' => $deviceID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取设备巡检项失败");
        }
    }

    /**
     * 获取设备巡检记录
     * @param int $recordID
     * @return array
     */
    public function getTaskDeviceRecordByRecordID(int $recordID): array
    {
        try {
            if (!$recordID) {
                throw new ServiceException("设备巡检记录ID不能为空");
            }

            $record = TaskDevicesRecord::query()
                ->with(['deviceItem', 'deviceItemsRecord', 'deviceItemsRecord.deviceItem'])
                ->findOrFail($recordID);

            // 构建 deviceItems 数据
            $isInspected = false;
            if ($record->deviceItemsRecord->count()) {
                $isInspected = true;
                $deviceItems = $record->deviceItemsRecord
                    // TODO::过滤掉已删除的巡检项，后面增加软删除之后去除代码，关联模型增加withTrashed获取软删数据
                    ->filter(function ($itemRecord) {
                        return $itemRecord->deviceItem !== null;
                    })
                    ->values()
                    ->map(function ($itemRecord) {
                        return [
                            'id' => $itemRecord->item_id,
                            'name' => $itemRecord->deviceItem->name,
                            'status' => $itemRecord->status,
                        ];
                    });
            } else {
                $deviceItems = $record->deviceItem
                    // TODO::过滤掉已删除的巡检项，后面增加软删除之后去除代码，关联模型增加withTrashed获取软删数据
                    ->filter(function ($item) {
                        return $item !== null;
                    })
                    ->values()
                    ->map(function ($item) {
                        return [
                            'id' => $item->id,
                            'name' => $item->name,
                            'status' => '',
                        ];
                    });
            }


            return [
                'isInspected' => $isInspected,
                'deviceItems' => $deviceItems->toArray(),
                'imageUrl' => $record->image_url,
                'question' => $record->question,
                'suggestion' => $record->suggestion
            ];

        } catch (ServiceException $e) {
            logger()->error("获取设备巡检记录失败", [
                'user_id' => auth()->id(),
                'task_device_record_id' => $recordID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取设备巡检记录失败");
        }
    }

}
