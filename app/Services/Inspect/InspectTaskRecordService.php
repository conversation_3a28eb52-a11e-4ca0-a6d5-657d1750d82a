<?php

namespace App\Services\Inspect;

use App\Exceptions\ServiceException;
use App\Models\Inspect\DeviceItemsRecord;
use App\Models\Inspect\Task;
use App\Models\Inspect\TaskDevicesRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * 巡检记录服务
 */
class InspectTaskRecordService
{
    /**
     * 生成任务设备记录表
     * @param Task|Model $task
     */
    public function generateTaskDeviceRecordTable(Task|Model $task): void
    {
        try {
            $today = Carbon::today()->toDateString();

            $task->devices()->each(function ($device) use ($task, $today) {
                TaskDevicesRecord::query()->firstOrCreate([
                    'task_id' => $task->id,
                    'device_id' => $device->id,
                    'checked_at' => $today
                ]);
            });

        } catch (\Exception $e) {
            logger()->error("生成巡检任务设备记录表失败", [
                'error' => $e->getMessage(),
                'task' => $task->toJson(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException('生成巡检任务设备记录表失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新巡检任务设备项记录表
     * @param int $taskDeviceRecordID 巡检任务设备记录ID
     * @param array $deviceItemStatus 设备项状态
     * @param array $img 图片
     * @param string $desc 描述
     * @param string $suggest 建议
     * @return TaskDevicesRecord|Model|null
     */
    public function updateTaskDeviceRecord(
        int $taskDeviceRecordID,
        array $deviceItemStatus,
        array $img,
        string $desc = '',
        string $suggest = ''
    ): TaskDevicesRecord|Model|null
    {
        try {
            if (!$taskDeviceRecordID) {
                throw new ServiceException('参数错误, 任务设备记录ID不能为空');
            }
            if (!$deviceItemStatus) {
                throw new ServiceException('参数错误, 设备项状态不能为空');
            }
            if (!$img) {
                throw new ServiceException('参数错误, 图片不能为空');
            }
            $record = TaskDevicesRecord::query()->with(['deviceItemsRecord'])->findOrFail($taskDeviceRecordID);
            // 设备巡检项是否全部正常
            $abnormalCount = 0;
            foreach ($deviceItemStatus as $item) {
                if ($item['status'] == DeviceItemsRecord::ABNORMAL_STATUS) {
                    $abnormalCount++;
                }
                // 创建巡检项记录
                DeviceItemsRecord::query()->updateOrCreate([
                    'task_devices_record_id' => $taskDeviceRecordID,
                    'item_id' => $item['id'],
                ], [
                    'status' => $item['status'],
                ]);
            }
            // 更新任务设备记录表
            $record->fill([
                'image_url' => $img,
                'question' => $desc,
                'suggestion' => $suggest,
                'status' => $abnormalCount > 0 ? TaskDevicesRecord::PENDING_APPROVAL : TaskDevicesRecord::INSPECTED,
                'abnormal_count' => $abnormalCount,
            ]);
            $record->save();
            return $record;
        } catch (ServiceException $e) {
            logger()->error("填写巡检任务设备记录表失败", [
                'userID' => auth('api')->id(),
                'taskDeviceRecordID' => $taskDeviceRecordID,
                'deviceItemStatus' => $deviceItemStatus,
                'img' => $img,
                'desc' => $desc,
                'suggest' => $suggest,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException('填写巡检任务设备记录表失败');
        }
    }
}
