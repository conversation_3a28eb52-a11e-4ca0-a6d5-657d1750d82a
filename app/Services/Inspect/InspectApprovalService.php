<?php

namespace App\Services\Inspect;

use App\Exceptions\ServiceException;
use App\Models\Inspect\TaskDevicesRecord;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

/**
 * 巡检审批服务
 */
class InspectApprovalService
{
    /**
     * 获取用户待审批列表
     * @param int $userID
     * @return array
     */
    public function getApprovalListByUserID(int $userID): array
    {
        try {
            if (!$userID) {
                throw new ServiceException("用户ID获取失败");
            }

            $approvalRecords = TaskDevicesRecord::query()
                ->with(['task', 'device'])
                ->whereHas('task', function ($query) use ($userID) {
                    $query->where('user_id', $userID);
                })
                ->whereIn('status', [TaskDevicesRecord::PENDING_APPROVAL, TaskDevicesRecord::APPROVED])
                ->orderBy('checked_at', 'desc')
                ->get()
                ->mapToGroups(function ($item) {
                    $status = 'waitApproval';
                    if ($item->status === TaskDevicesRecord::APPROVED) {
                        $status = 'approved';
                    }
                    $records = [
                        'id' => $item->id,
                        'device_id' => $item->device_id,
                        'task_name' => $item->task->name,
                        'device_name' => $item->device?->deleted_at ? $item->device?->name . '(设备已删除)' : $item->device?->name,
                        'abnormal_count' => $item->abnormal_count,
                        'submit_at' => $item->checked_at->toDateString(),
                        'approver_at' => $item->status === TaskDevicesRecord::APPROVED ? $item->approver_at->toDateString() : '--',
                    ];
                    return [$status => $records];
                });

            return [
                'waitApproval' => $approvalRecords->get('waitApproval', collect())->values()->all(),
                'approved' => $approvalRecords->get('approved', collect())->values()->all(),
            ];

        } catch (\Exception $e) {
            logger()->error("获取用户审批列表失败", [
                'user_id' => $userID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取用户审批列表失败");
        }
    }

    /**
     * 根据任务ID获取审批列表
     * @param int $taskID
     * @return array
     */
    public function getApprovalByTaskID(int $taskID): array
    {
        try {
            if (!$taskID) {
                throw new ServiceException("参数错误");
            }
            $approvalRecords = TaskDevicesRecord::query()
                ->with(['task', 'device'])
                ->where('task_id', $taskID)
                ->whereIn('status', [TaskDevicesRecord::PENDING_APPROVAL, TaskDevicesRecord::APPROVED])
                ->orderBy('checked_at', 'desc')
                ->get()
                ->mapToGroups(function ($item) {
                    $status = 'waitApproval';
                    if ($item->status === TaskDevicesRecord::APPROVED) {
                        $status = 'approved';
                    }
                    $records = [
                        'id' => $item->id,
                        'device_id' => $item->device_id,
                        'task_name' => $item->task->name,
                        'device_name' => $item->device?->deleted_at ? $item->device?->name . '(设备已删除)' : $item->device?->name,
                        'abnormal_count' => $item->abnormal_count,
                        'submit_at' => $item->checked_at->toDateString(),
                        'approver_at' => $item->status === TaskDevicesRecord::APPROVED ? $item->approver_at->toDateString() : '--',
                    ];
                    return [$status => $records];
                });

            return [
                'waitApproval' => $approvalRecords->get('waitApproval', collect())->values()->all(),
                'approved' => $approvalRecords->get('approved', collect())->values()->all(),
            ];

        } catch (\Exception $e) {
            logger()->error("获取任务审批列表失败", [
                'task_id' => $taskID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取任务审批列表失败");
        }
    }

    /**
     * 获取巡检记录审批详情
     * @param int $taskDeviceRecordID
     * @return array
     */
    public function getApprovalByTaskDeviceRecordID(int $taskDeviceRecordID): array
    {
        try {
            if (!$taskDeviceRecordID) {
                throw new ServiceException("巡检记录审批ID不能为空");
            }

            $record = TaskDevicesRecord::query()
                ->with(['task.user', 'deviceItemsRecord', 'deviceItemsRecord.deviceItem'])
                ->findOrFail($taskDeviceRecordID);

            // 构建 deviceItems 数据
            if ($record->deviceItemsRecord->count()) {
                $deviceItems = $record->deviceItemsRecord
                    // TODO::过滤掉已删除的巡检项，后面增加软删除之后去除代码，关联模型增加withTrashed获取软删数据
                    ->filter(function ($itemRecord) {
                        return $itemRecord->deviceItem !== null;
                    })
                    ->values()
                    ->map(function ($itemRecord) {
                        return [
                            'name' => $itemRecord->deviceItem->name,
                            'status' => $itemRecord->status,
                        ];
                    });
            }

            // 构建提交和审批数据
            $submitter = [
                [
                    'avatar' => $record->task->user->avatar,
                    'nickname' => $record->task->user->nickname,
                    'time' => $record->created_at->toDateString(),
                    'text' => '提交'
                ]
            ];
            $approver = [];
            if ($record->status === TaskDevicesRecord::APPROVED) {
                $approverUser = User::query()->find($record->approver_id ?? 0);
                if ($approverUser) {
                    $approver = [
                        [
                            'avatar' => $approverUser->avatar,
                            'nickname' => $approverUser->nickname,
                            'time' => $record->approver_at->toDateString(),
                            'text' => '审批 ' . ($record->approver_status === TaskDevicesRecord::APPROVE_WAIT ? '暂不处理' : '已处理')
                        ]
                    ];
                }
            }

            $handleProcess = array_merge($approver, $submitter);

            // 是否可以审批，目前仅记录状态为待审批且当前用户不是创建者时可以审批
            $canApprove = false;
            if ($record->status === TaskDevicesRecord::PENDING_APPROVAL && $record->task->user_id !== Auth::id()) {
                $canApprove = true;
            }
            return [
                'isCanApprove' => $canApprove,
                'deviceItems' => $deviceItems->toArray(),
                'imageUrl' => $record->image_url,
                'status' => $record->status,
                'question' => $record->question,
                'suggestion' => $record->suggestion,
                'handleProcess' => $handleProcess,
            ];

        } catch (ServiceException $e) {
            logger()->error("获取巡检记录审批失败", [
                'user_id' => Auth::id(),
                'task_device_record_id' => $taskDeviceRecordID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("获取巡检记录审批失败");
        }
    }

    /**
     * 审批
     * @param int $taskDeviceRecordID
     * @param int $approvalStatus
     * @return void
     */
    public function approve(int $taskDeviceRecordID, int $approvalStatus): void
    {
        try {
            $record = TaskDevicesRecord::query()
                ->where('status', TaskDevicesRecord::PENDING_APPROVAL)
                ->findOrFail($taskDeviceRecordID);

            $currentUserID = Auth::id();
            if ($record->task->user_id === $currentUserID) {
                throw new ServiceException("不能审批自己提交的记录");
            }

            $record->fill([
                'approver_id' => $currentUserID,
                'approver_status' => $approvalStatus,
                'approver_at' => now(),
                'status' => TaskDevicesRecord::APPROVED,
            ]);
            $record->save();
        } catch (ServiceException $e) {
            logger()->error("审批失败", [
                'approval_user_id' => Auth::id(),
                'task_device_record_id' => $taskDeviceRecordID,
                'approvalStatus' => $approvalStatus,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new ServiceException("审批失败," . $e->getMessage());
        }
    }
}
