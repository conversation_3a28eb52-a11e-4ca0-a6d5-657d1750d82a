<?php

namespace App\Services\Inspect;

use App\Models\Inspect\Device;
use App\Models\Inspect\DeviceItem;
use Illuminate\Support\Facades\DB;

class InspectDeviceItemService
{

    /**
     * 批量插入巡检项
     *
     * @param int $device_id 设备ID
     * @param array $names 巡检项名称
     * @return array
     */
    public static function batchAssign(int $deviceId, array $names): void
    {
        if (empty($names)) {
            return;
        }

        $names = array_map('trim', $names);

        $existingNames = DeviceItem::query()
            ->where('device_id', $deviceId)
            ->pluck('name')
            ->toArray();
    
        $newNames = array_diff($names, $existingNames);
        $delNames = array_diff($existingNames, $names);

        $total = count($newNames) - count($delNames);

        foreach ($newNames as $name) {
            self::create($deviceId, $name);
        }

        DeviceItem::query()
            ->where('device_id', $deviceId)
            ->whereIn('name', $delNames)
            ->delete();

        if ($total == 0) {
            return;
        }

        Device::query()->where('id', $deviceId)->update([
            'inspection_item_count' => DB::raw('inspection_item_count + ' . $total)
        ]);
    }

    /**
     * 创建设备巡检项
     *
     * @param int $device_id 设备ID
     * @param string $name 巡检项名称
     * @return DeviceItem
     */
    public static function create(int $deviceId, string $name): DeviceItem
    {
        $device = new DeviceItem();
        $device->device_id = $deviceId;
        $device->name = $name;

        $device->save();

        return $device;
    }
}