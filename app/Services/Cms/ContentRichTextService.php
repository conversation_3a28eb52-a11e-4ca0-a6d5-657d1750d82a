<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Attachment\AttachmentFile;
use App\Models\Cms\ContentRichText;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Storage;

class ContentRichTextService
{
    /**
     * 保存文章
     * @param int $id
     * @param array $params
     * @param bool $isSend
     * @return ContentRichText
     * @throws ServiceException
     */
    public static function update(int $id, array $params, bool $isSend = false): ContentRichText
    {
        /** @var ContentRichText $richText */
        $richText = ContentRichText::query()->where('content_id', $id)->first();

        if (!$richText) {
            $richText = new ContentRichText();
            $richText->content_id = $id;
        }

        $richText->content = $params['content'];
        $richText->save();

        if ($isSend) {
            ContentService::release($richText->content_id);
        }

        return $richText;
    }

    public static function saveAttachment(int $contentId, string $filepath): AttachmentFile
    {
        $groupDir = 'content';
        $targetType = BusinessType::Content;

        $file = AttachmentService::store($filepath, $groupDir, $targetType, $contentId);

        $file->path = AttachmentService::url(Storage::disk(), $file->path);

        return $file;
    }
}
