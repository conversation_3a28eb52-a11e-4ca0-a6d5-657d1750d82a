<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Cms\ContentDoc;
use App\Services\Admin\AttachmentRelationService;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Storage;

class ContentDocService
{
    /**
     * 保存文档
     * @param int $id
     * @param array $params
     * @param bool $isSend
     * @return ContentDoc
     * @throws ServiceException
     */
    public static function update(int $id, array $params, bool $isSend = false): ContentDoc
    {
        $groupDir = 'content';
        $targetType = BusinessType::Content;

        /** @var ContentDoc $doc */
        $doc = ContentDoc::query()->where('content_id', $id)->first();

        if (!$doc) {
            $doc = new ContentDoc();
            $doc->content_id = $id;
        }

        $doc->page_count = $params['page_count'];
        $doc->filename = $params['filename'];

        // 资料处理
        if (isset($params['filepath']) && $doc->filepath != $params['filepath']) {
            $filepath =  Storage::disk()->path($params['filepath']);
            $format = self::getFileType($filepath);

            if (!$format) {
                throw new ServiceException('上传的文件格式不正确');
            }


            if ($doc->filepath) {
                AttachmentService::removeRelations($targetType, $doc->content_id, $doc->filepath);
            }

            $attachment = AttachmentService::store($params['filepath'], $groupDir, $targetType, $doc->content_id);

            $checkSame = ContentDoc::query()
                ->where('filepath', $attachment->path)
                ->where('filename', $params['filename'])
                ->where('content_id', '<>', $id)
                ->first();
            if ($checkSame) {
                throw new ServiceException('该文件已存在，请勿重复上传');
            }

            $doc->format = $format;
            $doc->filepath = $attachment->path;
            $doc->filesize = $attachment->filesize;
        }

        // 处理预览图
        $previewImages = $doc->preview_images;
        $newPreviewImages = [];
        $intersectPreviewImages = [];
        foreach ($params['preview_images'] as $index => $newValue) {
            $newPreviewImages[$index] = $newValue;
            foreach ($previewImages as $oldValue) {
                if (str_contains($newValue, $oldValue)) {
                    $intersectPreviewImages[] = $oldValue;
                    $newPreviewImages[$index] = $oldValue;
                    break;
                }
            }
        }

        // 删除历史预览图
        $removePreviewImages = array_diff($previewImages, $intersectPreviewImages);
        if (!empty($removePreviewImages)) {
            foreach ($removePreviewImages as $previewImage) {
                AttachmentService::removeRelations($targetType, $doc->content_id, $previewImage);
            }
        }

        // 保存最新的预览图
        foreach ($newPreviewImages as $index => $previewImage) {
            if (str_contains($previewImage, AttachmentRelationService::UPLOAD_KEY)) {
                $attachment = AttachmentService::store($previewImage, $groupDir, $targetType, $doc->content_id);

                $newPreviewImages[$index] = $attachment->path;
            }
        }

        $doc->preview_images = $newPreviewImages;

        if (isset($params['download_count_add'])) {
            $doc->download_count_add = $params['download_count_add'];
        }

        $doc->save();

        if ($isSend) {
            ContentService::release($doc->content_id);
        }

        return $doc;
    }


    /**
     * 文件类型
     * @param string $filePath
     * @return string
     */
    public static function getFileType($filePath): string
    {
        return match (pathinfo($filePath, PATHINFO_EXTENSION)) {
            'doc', 'docx' => 'word',
            'xls', 'xlsx' => 'excel',
            'ppt', 'pptx' => 'ppt',
            'pdf' => 'pdf',
            default => ''
        };
    }
}
