<?php

namespace App\Services\Cms;

use App\Exceptions\ServiceException;
use App\Libs\Utils\Helpers;
use App\Models\Cms\Content;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCourseSection;

class ContentCourseChapterService
{
    /**
     * 创建
     * @param int $contentId
     * @param string $name
     * @param int $status
     * @param int $sort
     * @return ContentCourseChapter
     * @throws ServiceException
     */
    public static function create(int $contentId, string $name, int $status, int $sort = 0): ContentCourseChapter
    {
        if (!Content::query()->find($contentId)) {
            throw new ServiceException('内容不存在');
        }

        //确保有课程，否则在第二步没有点保存或发布时直接创建章节会出现没有 course 数据，但有 chapter 和 section 数据的情况，数据的关联路径就会断开
        ContentCourse::query()->firstOrCreate([
            'content_id' => $contentId
        ]);

        $chapter = new ContentCourseChapter();
        $chapter->content_id = $contentId;
        $chapter->name = $name;
        $chapter->status = $status;
        $chapter->sort = $sort;
        $chapter->save();

        return $chapter;
    }

    /**
     * 修改
     * @param int $id
     * @param array $params
     * @return ContentCourseChapter
     * @throws ServiceException
     */
    public static function update(int $id, array $params): ContentCourseChapter
    {
        /** @var ContentCourseChapter $chapter */
        $chapter = ContentCourseChapter::query()->find($id);

        if (!$chapter) {
            throw new ServiceException('该章不存在');
        }

        foreach ($params as $key => $val) {
            if (in_array($key, $chapter->getFillable())) {
                $chapter->{$key} = $val;
            }
        }

        $chapter->save();

        return $chapter;
    }

    /**
     * 删除
     * @param int $id
     * @return void
     * @throws ServiceException
     */
    public static function remove(int $id): void
    {
        /** @var ContentCourseChapter $chapter */
        $chapter = ContentCourseChapter::query()->with(['sections'])->find($id);

        if (!$chapter) {
            throw new ServiceException('该章不存在');
        }

        if (!$chapter->sections->isEmpty()) {
            throw new ServiceException('请先删除节');
        }

        $chapter->delete();
    }

    public static function children($chapters): array
    {
        $list = [];

        /** @var ContentCourseChapter $chapter */
        foreach ($chapters as $chapter) {
            $temp = [
                'id' => $chapter->id,
                'course_id' => $chapter->content_id,
                'index' => strval($chapter->id),
                'type' => 'chapter',
                'name' => $chapter->name,
                'status' => $chapter->status,
                'sort' => $chapter->sort,
                'created_at' => $chapter->created_at,
                'updated_at' => $chapter->updated_at,
                'duration' => 0,
                'children' => []
            ];

            if ($chapter->sections) {
                /** @var ContentCourseSection $section */
                foreach ($chapter->sections as $section) {
                    $temp['children'][] = [
                        'id' => $section->id,
                        'course_id' => $chapter->content_id,
                        'index' => "$chapter->id:$section->id",
                        'type' => 'section',
                        'ref_video_id' => $section->ref_video_id,
                        'name' => $section->name,
                        'status' => $section->status,
                        'sort' => $section->sort,
                        'filepath' => $section->filepath,
                        'filepath_src' => $section->filepath_src,
                        'video' => $section->video,
                        'duration' => $section->duration,
                        'created_at' => $section->created_at,
                        'updated_at' => $section->updated_at,
                    ];

                    if ($section->status) {
                        $temp['duration'] += $section->duration;
                    }
                }
            }

            $list[] = $temp;
        }

        return $list;
    }

    /**
     * 生成章节序号
     * @param array $list
     * @return array
     */
    public static function generateSerialNo(array $list): array
    {
        $chapterIndex = 0;
        foreach ($list as &$item) {
            $item['name_desc'] = $item['name'];

            if ($item['status']) {
                $chapterDesc = Helpers::numberToChinese(++$chapterIndex);
                $item['name_desc'] = "第{$chapterDesc}章 {$item['name']}";
            }

            $sectionIndex = 0;
            foreach ($item['children'] as &$child) {
                $child['name_desc'] = $child['name'];

                if ($child['status']) {
                    $sn = sprintf('%02d', ++$sectionIndex);
                    $child['name_desc'] = "{$sn} {$child['name']}";
                }
            }
        }

        return $list;
    }
}
