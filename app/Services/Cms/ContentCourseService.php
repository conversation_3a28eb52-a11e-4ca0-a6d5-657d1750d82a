<?php

namespace App\Services\Cms;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Jobs\CourseUpdateJob;
use App\Libs\AsyncTasks\ContentCourseSectionTranscodeTask;
use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCoursePack;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Cms\ContentCourseSection;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentVideo;
use App\Models\Org\CoursePack;
use App\Services\Common\AsyncTaskService;
use App\Services\Org\CourseService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class ContentCourseService
{
    /**
     * 保存课程信息
     * @param int $id
     * @param array $params
     * @param bool $isSend
     * @return ContentCourse
     * @throws Throwable
     */
    public static function update(int $id, array $params, bool $isSend = false): ContentCourse
    {
        /** @var ContentCourse $course */
        $course = ContentCourse::query()->with(['chapters', 'chapters.sections'])->where('content_id', $id)->first();

        if (!$course) {
            $course = new ContentCourse();
            $course->content_id = $id;
        }

        $course->teacher_name = $params['teacher_name'] ?? '';
        $course->try_view_count = $params['try_view_count'];
        $course->learning_count_add = $params['learning_count_add'];
        $course->hour_per_minutes = $params['hour_per_minutes'];
        $course->topic_id = $params['topic_id'];
        $course->save();

        if ($course->getOriginal('hour_per_minutes')) {
            $course->hour = $course->studyHour($course->duration, false);
            $course->save();

            CourseUpdateJob::dispatch($course->content_id);
        }

        if ($isSend) {
            // 是否有视频需要转码
            $hasVideo = false;

            foreach ($course->chapters as $chapter) {
                if ($chapter->sections->isEmpty()) {
                    throw new ServiceException('有未处理的课程章节');
                }
                foreach ($chapter->sections as $section) {
                    if ($section->status == ContentCourseSection::STATUS_PADDING) {
                        $hasVideo = true;
                        break;
                    }
                }
            }

            // 视频需要转码
            if ($hasVideo) {
                ContentService::processing($course->content_id, '视频转码中', true);

                foreach ($course->chapters as $chapter) {
                    foreach ($chapter->sections as $section) {
                        if ($section->status == ContentCourseSection::STATUS_PADDING) {
                            try {
                                // 更新课程状态，防止重复转码
                                $section->status = ContentCourseSection::STATUS_PROCESSING;
                                $section->save();

                                $task = new ContentCourseSectionTranscodeTask($section->id, addWatermark: true);
                                AsyncTaskService::start($task, BusinessType::CmsCourse, $id, "章 $chapter->id 节 $section->id 转码中..");
                            } catch (Throwable $e) {
                                Log::error('转码任务启动失败: '.$e->getMessage(), ['content_id'=>$id, 'chapter_id'=>$chapter->id, 'section_id'=>$section->id]);
                                throw $e;
                            }
                        }
                    }
                }
            } else {
                ContentService::release($course->content_id);
            }
        }

        return $course;
    }

    /**
     * 更新课程节数
     * @param int $contentId
     * @return ContentCourse
     * @throws ServiceException
     */
    public static function updateSectionCount(int $contentId): ContentCourse
    {
        /** @var ContentCourse $course */
        $course = ContentCourse::query()->with(['chapters', 'chapters.sections'])->where('content_id', $contentId)->first();

        if (!$course) {
            throw new ServiceException('课程不存在');
        }

        $sectionsCount = 0;
        foreach ($course->chapters as $chapter) {
            $chapter->sections_count = count($chapter->sections);
            $chapter->save();

            $sectionsCount += $chapter->sections_count;
        }

        $course->sections_count = $sectionsCount;
        $course->save();

        return $course;
    }

    /**
     * 更新课程包的课程数据
     *
     * @param int $contentId 课程包 ID
     * @param array $courseIds 关联和课程 ID 列表，按照列表中的ID顺序排序
     * @param int $topicId 关联的题库ID
     * @param bool $isSend 是否发布
     * @return ContentCoursePack
     */
    public static function updatePack(int $contentId, array $courseIds, int $topicId = 0, bool $isSend=false)
    {
        $pack = ContentCoursePack::updateOrCreate([
            'content_id' => $contentId
        ]);

        $relations = ContentCoursePackList::query()->where('content_id', $contentId)->get();

        $oldCourseIds = $relations->pluck('course_id')->toArray();
        $removeCourseIds = array_diff($oldCourseIds, $courseIds);
        $newCourseIds = array_diff($courseIds, $oldCourseIds);

        $orders = array_flip($courseIds);

        // 删除关联内容
        if ($removeCourseIds) {
            $relations->whereIn('course_id', $removeCourseIds)->each(fn($relation) => $relation->delete());
        }

        // 检查关联内容是否都存在
        if ($newCourseIds) {
            $newCourseCount = ContentCourse::query()->whereIn('content_id', $newCourseIds)->count();

            if ($newCourseCount != count($newCourseIds)) {
                throw new ServiceException('选择的课程不存在或已删除');
            }

            foreach ($newCourseIds as $courseId) {
                $model = new ContentCoursePackList();
                $model->content_id = $contentId;
                $model->course_id = $courseId;
                $model->order = $orders[$courseId];
                $model->save();
            }
        }

        // 判定是否要更新原有的排序
        $existsCourseIds = array_intersect($courseIds, $oldCourseIds);

        if ($existsCourseIds) {
            $relationsByKey = $relations->keyBy('course_id');
            foreach ($existsCourseIds as $courseId) {
                $model = $relationsByKey->get($courseId);
                $order = $orders[$courseId];
                if ($model->order != $order) {
                    $model->order = $order;
                    $model->save();
                }
            }
        }

        if ($isSend) {
            ContentService::release($contentId);
        }

        $pack->topic_id = $topicId;
        $pack->save();

        // 更新机构课程包的hour和duration
        $orgIds = CoursePack::query()->where('course_pack_id', $contentId)->pluck('org_id')->toArray();
        if ($orgIds) {
            $contentCourses = ContentCourse::query()
                ->select(['hour_per_minutes', 'content_id'])
                ->whereIn('content_id', $courseIds)
                ->get()
                ->keyBy('content_id');
    
            foreach ($orgIds as $oid) {
                $totalHour = 0;
                $totalDuration = 0;

                foreach ($courseIds as $courseId) {
                    list($courseHour, $courseDuration) = CourseService::getCourseHourAndDuration($contentCourses->get($courseId), $oid);
                    $totalHour += $courseHour;
                    $totalDuration += $courseDuration;
                }

                CoursePack::query()
                    ->where('org_id', $oid)
                    ->where('course_pack_id', $contentId)
                    ->update([
                        'duration' => $totalDuration,
                        'hour' => (int)$totalHour
                    ]);
            }
        }

        return $pack;
    }

    /**
     * 批量获取所有课程的总时长
     * ContentCourseSection的duration优先使用视频时长
     *
     * @param array $courseIds
     * @return array
     */
    public static function getTotalDurations(array $courseIds): array
    {
        $chapterTable = (new ContentCourseChapter())->getTable();
        $sectionTable = (new ContentCourseSection())->getTable();
        $videoTable = (new ContentVideo())->getTable();

        // 使用子查询计算每个章节的实际时长
        $subQuery = DB::table($sectionTable)
            ->select([
                DB::raw("CASE WHEN {$sectionTable}.ref_video_id > 0 THEN {$videoTable}.duration ELSE {$sectionTable}.duration END AS actual_duration"),
                $sectionTable . '.content_id',
                $sectionTable . '.chapter_id'
            ])
            ->leftJoin($videoTable, $sectionTable . '.ref_video_id', '=', $videoTable . '.content_id')
            ->where($sectionTable . '.status', ContentCourseSection::STATUS_SHOW);

        return DB::table($chapterTable)
            ->select([
                'sections.content_id',
                DB::raw('SUM(actual_duration) as total_duration')
            ])
            ->fromSub($subQuery, 'sections')
            ->leftJoin($chapterTable, $chapterTable . '.id', '=', 'sections.chapter_id')
            ->where($chapterTable . '.status', ContentCourseChapter::STATUS_SHOW)
            ->whereIn($chapterTable . '.content_id', $courseIds)
            ->groupBy('sections.content_id')
            ->pluck('total_duration', 'content_id')
            ->all();
    }

    /**
     * 获取课程的课时
     *
     * @param array $courseIds
     * @return array
     */
    public static function getCoursesHour(array $courseIds): array
    {
        /** @var Collection<int, ContentCourse> $contentCourses */
        $contentCourses = ContentCourse::query()
            ->select(['hour_per_minutes', 'content_id'])
            ->whereIn('content_id', $courseIds)
            ->get()
            ->keyBy('content_id');

        $courseDurations = self::getTotalDurations($courseIds);
        $coursesHour = [];

        foreach ($courseIds as $courseId) {
            $coursesHour[$courseId] = $contentCourses->get($courseId)->studyHour($courseDurations[$courseId] ?? 0, false);
        }

        return $coursesHour;
    }

    /**
     * @param $resourceId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     */
    public static function getCourseSecond($resourceId)
    {
        $contentCourse = ContentCourse::query()->where('content_id', $resourceId)->first();
        if (!$contentCourse) {
            throw new ServiceException("课程 content_id {$resourceId} 不存在。");
        }

        return $contentCourse;
    }

    /**
     * 获取课程的章节数
     * @param $courseId
     * @return int
     */
    public static function getChapterCount($courseId)
    {
        return ContentCourseChapter::query()
            ->where('content_id', $courseId)
            ->where('status', ContentCourseChapter::STATUS_SHOW)
            ->count();
    }

    /**
     * 获取课程的节数
     * @param $courseId
     * @return int
     */
    public static function getSectionCount($courseId)
    {
        return ContentCourseSection::query()
            ->where('content_id', $courseId)
            ->where('status', ContentCourseSection::STATUS_SHOW)
            ->count();
    }

    /**
     * 获取课程的已显示的资源id与名称映射
     * @param int $courseId
     * @return array
     */
    public static function getShowResourceIds(int $courseId): ?array
    {
        $contentCourse = ContentCourse::where('content_id', $courseId)
                ->with(['chapters' => function($q) {
                    $q->select('id', 'name', 'status', 'content_id')->where('status', ContentCourseChapter::STATUS_SHOW);
                }])
                ->with(['sections' => function($q) {
                    $q->select('id', 'name', 'status', 'content_id')->where('status', ContentCourseSection::STATUS_SHOW);
                }])
                ->first();

        $chapterMap = $contentCourse->chapters->pluck('name', 'id')->toArray();
        $sectionMap = $contentCourse->sections->pluck('name', 'id')->toArray();

        return [$chapterMap, $sectionMap];
    }
}