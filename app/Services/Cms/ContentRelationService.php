<?php

namespace App\Services\Cms;

use App\Exceptions\ServiceException;
use App\Models\Cms\Content;
use App\Models\Cms\ContentRelation;

class ContentRelationService
{
    public static function create(Content $content, array $relationIds): void
    {
        $oldRelationIds = $content->relationList->pluck('related_id')->toArray();

        $removeRelationIds = array_diff($oldRelationIds, $relationIds);

        $createRelationIds = array_diff($relationIds, $oldRelationIds);

        // 删除关联内容
        if (!empty($removeRelationIds)) {
            ContentRelation::query()->where('content_id', $content->id)->whereIn('related_id', $removeRelationIds)->delete();
        }

        // 检查关联内容是否都存在
        if (!empty($createRelationIds)) {
            $createContentCount = Content::query()->whereIn('id', $createRelationIds)->count();

            if ($createContentCount != count($createRelationIds)) {
                throw new ServiceException('选择的内容不存在或已删除');
            }

            foreach ($createRelationIds as $value) {
                $model = new ContentRelation();
                $model->content_id = $content->id;
                $model->related_id = $value;
                $model->save();
            }
        }
    }
}
