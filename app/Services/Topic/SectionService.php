<?php

namespace App\Services\Topic;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Train\Chapter;
use App\Models\Train\Example;
use App\Models\Train\Section;
use App\Models\Train\Subject;
use App\Services\Common\AttachmentService;
use Illuminate\Support\Facades\Log;
use Throwable;

class SectionService
{
    public static function create(int $chapterId, string $name, array $params = []): Section
    {
        /** @var Chapter $chapter */
        $chapter = Chapter::query()->find($chapterId);

        if (!$chapter) {
            throw new ServiceException('选择的章不存在');
        }

        if ($chapter->example && empty($params['example_content'])) {
            throw new ServiceException('请填写案例内容');
        }

        $section = new Section();
        $section->topic_id = $chapter->topic_id;
        $section->chapter_id = $chapter->id;
        $section->name = $name;
        $section->save();

        // 案例章节
        if ($chapter->example) {
            $example = new Example();
            $example->topic_id = $chapter->topic_id;
            $example->chapter_id = $chapter->id;
            $example->section_id = $section->id;
            $example->content = $params['example_content'];
            $example->save();

            self::handleAttachments($example, $params);
        }

        return $section;
    }

    public static function update(int $id, string $name, array $params): Section
    {
        /** @var Section $section */
        $section = Section::query()->find($id);

        if (!$section) {
            throw new ServiceException('该节不存在');
        }

        $section->name = $name;
        $section->save();

        // 案例节修改
        if (!empty($params['example_content'])) {
            $example = $section->example;
            $example->content = $params['example_content'];
            $example->save();

            self::handleAttachments($example, $params);
        }

        return $section;
    }

    public static function remove(int $id): void
    {
        /** @var Section $section */
        $section = Section::query()->find($id);

        if (!$section) {
            throw new ServiceException('该节不存在');
        }

        if (Subject::query()->where('section_id', $section->id)->count() > 0) {
            throw new ServiceException('请先删除该节下面的所有题目');
        }

        $section->delete();

        Example::query()->where('section_id', $section->id)->delete();
    }

    protected static function handleAttachments(Example $example, array $params): void
    {
        if (empty($params['upload_files'])) {
            return;
        }

        $targetType = BusinessType::TrainExample;
        $groupDir = 'topic';

        $uploadData = [];
        foreach ($params['upload_files'] as $file) {
            if (!$file['type_id']) {
                try {
                    $uploadData[$file['url']] = AttachmentService::store($file['filepath'], $groupDir, $targetType, $example->id);
                } catch (Throwable) {
                    Log::error("案例{$example->id}保存附件失败: {$file['filepath']}");
                }
            }
        }

        if (empty($uploadData)) {
            return;
        }

        foreach ($uploadData as $url => $attachment) {
            $example->content = str_replace($url, $attachment->url, $example->content);
            $example->save();
        }
    }
}
