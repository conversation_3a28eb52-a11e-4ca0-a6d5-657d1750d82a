<?php

namespace App\Services\Topic;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Org\Enrollment;
use App\Models\Org\Topic;
use App\Models\Train\Chapter;
use App\Models\Train\Example;
use App\Models\Train\Section;
use App\Models\Train\Subject;
use App\Models\Train\SubjectOption;
use App\Models\Train\Test;
use App\Models\Train\TestSubject;
use App\Models\User;
use App\Models\User\UserOwnTopic;
use App\Services\Org\OrgClassService;
use Illuminate\Support\Collection;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TopicService
{
    public static function remove(int $id): void
    {
        /** @var \App\Models\Train\Topic $topic */
        $topic = \App\Models\Train\Topic::query()->with(['subjects'])->find($id);

        if (!$topic) {
            throw new ServiceException("题库不存在");
        }

        if (Test::query()->where('topic_id', $id)->exists()) {
            throw new ServiceException('题库已使用');
        }

        if (Topic::query()->where('topic_id', $id)->exists()) {
            throw new ServiceException('题库已使用');
        }

        // 试题
        foreach ($topic->subjects as $subject) {
            $subject->options()->delete();
        }

        $topic->subjects()->delete();

        // 章节
        Chapter::query()->where('topic_id', $id)->delete();
        Section::query()->where('topic_id', $id)->delete();

        // 案例
        Example::query()->where('topic_id', $id)->delete();

        // 考试记录
        Test::query()->where('topic_id', $id)->delete();
        TestSubject::query()->where('topic_id', $id)->delete();

        // 机构关联
        Topic::query()->where('topic_id', $id)->delete();

        $topic->delete();
    }

    /**
     * @param $uid
     * @param $testId
     * @param $subjectId
     * @param $optionId
     * @param string $answer
     * @return void
     */
    public static function processing($uid, $testId, $subjectId, $optionId, string|null $answer = "")
    {
        $test = Test::query()->where('user_id', $uid)->where('id', $testId)->first();
        if (!$test) {
            throw new NotFoundHttpException("考试或者练习不存在");
        }

        $subject = Subject::query()->where('id', $subjectId)->first();
        if (!$subject) {
            throw new NotFoundHttpException("题目不存在");
        }

        $testSubject = TestSubject::query()
            ->where('user_id', $uid)
            ->where('test_id', $test->id)
            ->where('subject_id', $subjectId)
            ->first();

        if ($testSubject && $testSubject->option_id) {
            //throw new NotFoundHttpException("该题已作答");
        }
        $correct = SubjectOption::CORRECT_NOT;
        $params = [];
        $exam = TopicService::examConfig($test->topic_id, $test->org_id);

        $score = 0;
        if ($subject->type == Subject::TYPE_MC) {
            $score = $exam->exam_config['multiple_choice']['score'];
            $correctId = SubjectOption::query()->where('subject_id', $subjectId)
                ->where('is_correct', SubjectOption::CORRECT)
                ->orderBy('id')
                ->get(['id', 'is_correct'])
                ->pluck('id');
            $correctId = $correctId->toArray();
            $optionArrId = [];
            foreach ($optionId as $k => $v) {
                $optionArrId[] = SubjectOption::decodeSid($v);
            }
            sort($optionArrId);
            if ($correctId == $optionArrId) {
                $correct = SubjectOption::CORRECT;
            } else {
                if (collect($optionArrId)->diff($correctId)->count()) { //检查用户提交的选项 是否存在错误选项
                    $score = 0;
                } else {
                    $score = count($optionArrId) * 0.5;
                    $test->increment('score', $score);
                    $test->save();
                }
            }
            $params['option_id'] = collect($optionArrId)->join(',');
        } elseif ($subject->type == Subject::TYPE_QA) {
            $params['option_id'] = 0;
        } else {
            if ($subject->type == Subject::TYPE_SC) {
                $score = $exam->exam_config['single_choice']['score'];

                $correctId = SubjectOption::query()->where('subject_id', $subjectId)
                    ->where('is_correct', SubjectOption::CORRECT)
                    ->value('id');
                $params['option_id'] = SubjectOption::decodeSid($optionId[0]);
                if ($correctId == $params['option_id']) {
                    $correct = SubjectOption::CORRECT;
                }
            } else {
                $score = $exam->exam_config['judge']['score'];
                $params['option_id'] = $optionId[0];
                if ($subject->judge_correct == $optionId[0]) {
                    $correct = SubjectOption::CORRECT;
                }
            }
        }

        Test::query()->where('user_id', $uid)->where('id', $testId)->increment('subject_completed_count');

        if ($correct == TestSubject::CORRECT_RIG && $subject->type != Subject::TYPE_QA) {
            $test->subject_correct_count++;
            $test->increment('score', $score);
            $test->save();
        }

        if ($test->type == Test::TYPE_EMU || $test->type == Test::TYPE_EXAM) {
            if ($testSubject) {
                $testSubject->correct = $correct;
                $testSubject->answer = $answer;
                $testSubject->option_id = $params['option_id'];
                $testSubject->save();
            } else {
                throw new NotFoundHttpException("该题不存在");
            }
        } else {
            $testSubjectArr = [
                'user_id' => $uid,
                'topic_id' => $test->topic_id,
                'test_id' => $testId,
                'subject_id' => $subjectId,
                'answer' => $answer,
                'option_id' => $params['option_id'],
                'correct' => $correct,
            ];
            TestSubject::query()->create($testSubjectArr);
        }
    }

    /**
     * 获取测试头尾题目数据
     * @param $topId
     * @param $endId
     * @param $topicId
     * @param $testId
     * @return array
     */
    public static function headTail($topId, $endId, $testId, $test, $uid): array
    {
        $top = Subject::query()
            ->with('option')
            ->with('favorite')
            ->with('testOption', function ($query) use ($testId) {
                $query->where('test_id', $testId)->select(['test_id', 'subject_id', 'option_id', 'topic_id', 'id', 'correct']);
            })->where('topic_id', $test->topic_id);

        if ($test->type == Test::TYPE_SINGLE || $test->type == Test::TYPE_MULTI || $test->type ==
            Test::TYPE_JUDGE) {

            $type = match ($test->type) {
                Test::TYPE_SINGLE => Subject::TYPE_SC,
                Test::TYPE_MULTI => Subject::TYPE_MC,
                Test::TYPE_JUDGE => Subject::TYPE_TF,
                default => Subject::TYPE_SC
            };
            $top->where('type', $type);
        } else if ($test->type == Test::TYPE_COLL) { //收藏练习
            $top->whereIn('id', function ($query) use ($test, $uid) {
                $query->from('user_favorites')
                    ->where('user_id', $uid)
                    ->where('business_type', BusinessType::Subject)
                    ->select('business_id');
            });
        } else if ($test->type == Test::TYPE_ERR) { //错误练习
            $top->whereIn('id', function ($query) use ($test) {
                $query->from('train_test_subjects')->where('topic_id', $test->topic_id)
                    ->where('option_id', '<>', 0)
                    ->where('correct', Test::CORRECT_ERR)
                    ->where('wrong_removed', Test::WRONG_REMOVED_NOT)
                    ->select('subject_id');
            });
        }

        $top = $top->where('id', '<', $topId)
            ->orderByDesc('id')->first();


        $end = Subject::query()
            ->with('option')
            ->with('favorite')
            ->with('testOption', function ($query) use ($testId) {
                $query->where('test_id', $testId)->select(['test_id', 'subject_id', 'option_id', 'topic_id', 'id', 'correct']);
            })->where('topic_id', $test->topic_id);

        if ($test->type == Test::TYPE_SINGLE || $test->type == Test::TYPE_MULTI || $test->type ==
            Test::TYPE_JUDGE) {

            $type = match ($test->type) {
                Test::TYPE_SINGLE => Subject::TYPE_SC,
                Test::TYPE_MULTI => Subject::TYPE_MC,
                Test::TYPE_JUDGE => Subject::TYPE_TF,
                default => Subject::TYPE_SC
            };
            $end->where('type', $type);
        } else if ($test->type == Test::TYPE_COLL) { //收藏练习
            $end->whereIn('id', function ($query) use ($test, $uid) {
                $query->from('user_favorites')
                    ->where('user_id', $uid)
                    ->where('business_type', BusinessType::Subject)
                    ->select('business_id');
            });
        } else if ($test->type == Test::TYPE_ERR) { //错误练习
            $end->whereIn('id', function ($query) use ($test) {
                $query->from('train_test_subjects')->where('topic_id', $test->topic_id)
                    ->where('option_id', '<>', 0)
                    ->where('correct', Test::CORRECT_ERR)
                    ->where('wrong_removed', Test::WRONG_REMOVED_NOT)
                    ->select('subject_id');
            });
        }
        $end = $end->where('id', '>', $endId)
            ->orderBy('id')
            ->first();
        return ['top' => $top, 'end' => $end];
    }

    /**
     * 是否购买题库
     * @param $uid
     * @param $topicId
     * @return bool
     */
    public static function isBuy($uid, $topicId): bool
    {
        $isPermanent = UserOwnTopic::query()
            ->where('user_id', $uid)
            ->where('topic_id', $topicId)
            ->where('expired_at', null)
            ->exists();
        if ($isPermanent) {
            return true;
        }

        $isBuy = UserOwnTopic::query()
            ->where('user_id', $uid)
            ->where('topic_id', $topicId)
            ->where('expired_at', '>', now())
            ->exists();
        if ($isBuy) {
            return true;
        }
        return false;
    }


    /**
     * 是否有购买的题库
     * @param $uid
     * @return Collection
     */
    public static function whetherPurchase($uid)
    {
        return UserOwnTopic::query()
            ->where('user_id', $uid)
            ->where(function ($query) {
                $query->where('expired_at', null)->orWhere('expired_at', '>', now());
            })
            ->get(['topic_id'])->pluck('topic_id');
    }

    /**
     * 创建考试
     * @return void
     */
    public static function createExam($uid, $topicId, Test $test)
    {
        $topic = TopicService::examConfig($topicId, $test->org_id);
        $examConfig = $topic->exam_config;
        $subjectTf = collect([]);
        $subjectSc = collect([]);
        $subjectMc = collect([]);
        if ($examConfig['judge']['count']) {
            $subjectTf  = Subject::query()
                ->where('topic_id', $topicId)
                ->inRandomOrder()
                ->where('type',Subject::TYPE_TF)
                ->limit($examConfig['judge']['count'])->get();
        }

        if ($examConfig['single_choice']['count']) {
            $subjectSc  = Subject::query()
                ->where('topic_id', $topicId)
                ->inRandomOrder()
                ->where('type',Subject::TYPE_SC)
                ->limit($examConfig['single_choice']['count'])->get();

        }

        if ($examConfig['multiple_choice']['count']) {
            $subjectMc  = Subject::query()
                ->where('topic_id', $topicId)
                ->inRandomOrder()
                ->where('type', Subject::TYPE_MC)
                ->limit($examConfig['multiple_choice']['count'])->get();
        }

        $addData = [];
        $now = now();
        $subjectTf = $subjectTf->merge($subjectSc);
        $subjectTf = $subjectTf->merge($subjectMc);
        foreach ($subjectTf as $k) {
            $install = [
                'test_id' => $test->id,
                'subject_id' => $k->id,
                'topic_id' => $topicId,
                'option_id' => 0,
                'user_id' => $uid,
                'created_at' => $now,
                'updated_at' => $now,
            ];
            $addData[] = $install;
        }

        TestSubject::query()->insert($addData);
    }

    /**
     * @param User $user
     * @param string $orgSid
     * @return
     */
    public static function getExam(User $user, string $orgId, $topicId, $enrollId)
    {
        $enroll = Enrollment::query()->where("user_id", $user->id)->where('id', $enrollId)->first();
        if (!$enroll) {
            throw new NotFoundHttpException("考试不存在");
        }
        $exams = OrgClassService::currentExam($user, $enroll, 'miniProgram');

        return $exams;
    }

    /**
     * 是否可以考试
     * @return void
     */
    public static function isExam($exam)
    {
        if ($exam->exam_limit_count != -1 && $exam->exam_limit_count > 0) {
            throw new ServiceException('当前无法考试，考试次数不足');
        }

        if ($exam->exam_time != -1) {
            $examTime = \Carbon\Carbon::parse($exam->exam_time);
            $currentTime = \Carbon\Carbon::now();

            if ($examTime->isBefore($currentTime)) {
                throw new ServiceException('当前无法考试，考试时间已过');
            }

            if (!$examTime->isSame($currentTime, 'day')) {
                throw new ServiceException('当前无法考试，考试时间不是今天');
            }
        }

//        if (data.exam_mode != all && data.exam_mode != 'pc_only') {
//            dialog.error({
//                content: '当前无法考试，考试模式不是电脑模式',
//            });
//            return;
//        }
    }

    /**
     * 考试配置
     *
     */
    public static function examConfig($topicId, $orgId = "")
    {
        $topic = Topic::query()->where('topic_id', $topicId)->where('org_id', $orgId)->first();
        if ($topic) {
            $topic->name = \App\Models\Train\Topic::query()->where('id', $topicId)->value('name');
        }
        if (!$topic || ($topic && !$topic->exam_config)) {
            $topic = \App\Models\Train\Topic::query()->where('id', $topicId)->first();
        }

        $examConfig = $topic->exam_config ?? Topic::getExamConfig();

        $totalScore = 0;

        foreach ($examConfig as $k => $v) {
            if ($v['count']) {
                $totalScore += $v['score'] * $v['count'];
            }
        }

        $topicCount = collect($examConfig)->sum('count');
        $topic->topic_count = $topicCount;
        $topic->total_score = $totalScore;
        $topic->exam_config = $examConfig;
        $topic->limit_time = $topic->exam_time;
        $topic->sid = \App\Models\Train\Topic::encodeId($topicId);
        return $topic;

    }
}
