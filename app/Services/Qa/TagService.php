<?php

namespace App\Services\Qa;

use App\Models\Qa\Tag;
use App\Models\Qa\TagRelation;
use Illuminate\Database\Eloquent\Collection;

class TagService
{

    /**
     * 从文本中匹配出标签名称
     *
     * @param string $text
     * @return string[]
     */
    public static function matchTagsFromText($text)
    {
        if (preg_match_all('/#([a-zA-Z0-9_\x{4e00}-\x{9fa5}]+)(?=\s|#|\b|$)/u', $text, $matches)) {
            return array_unique($matches[1]);
        } else {
            return [];
        }
    }

    /**
     * 检索出给定的标签模型
     *
     * @param string[] 标签名称
     * @return Tag[]|Collection
     */
    public static function retrieveTags($tags)
    {
        $existsTags = Tag::query()
            ->whereIn('name', $tags)
            ->get()
            ->keyBy('name');

        $newTags = [];

        foreach ($tags as $name) {
            if (!$existsTags->get($name)) {
                $tag = new Tag([
                    'name' => $name
                ]);
                $tag->save();
                $newTags[$name] = $tag;
            }
        }

        return $existsTags->merge(new Collection($newTags));
    }

}
