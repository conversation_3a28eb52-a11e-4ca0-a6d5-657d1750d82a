<?php

namespace App\Services\Ers;

use App\Services\Ers\Modules\FormModule;
use App\Services\Ers\Modules\ModuleConfigure;
use App\Services\Ers\Modules\ModuleInterface;
use App\Services\Ers\Modules\PaymentModule;
use App\Services\Ers\Modules\PaymentFinalModule;
use App\Services\Ers\Modules\PaymentStageModule;
use App\Services\Ers\Modules\SolutionDownloadModule;
use App\Services\Ers\Modules\SolutionPreviewModule;

class ModuleService
{

    /**
     * 已注册的全部模块
     * @return class-string<ModuleInterface>[]
     */
    public static function registerModules()
    {
        return [
            FormModule::class,
            PaymentModule::class,
            PaymentStageModule::class,
            PaymentFinalModule::class,
            SolutionPreviewModule::class,
            SolutionDownloadModule::class
        ];
    }

    /**
     * 获取缓存的 ModuleConfigure 实例，不要每次都实例化
     * @param class-string<ModuleInterface> $class
     * @return ModuleConfigure
     */
    protected static function conf($class)
    {
        static $cache = [];
        $cache[$class] ??= $class::configure();
        return $cache[$class];
    }

    /**
     * 模块标识与类映射列表
     *
     * @return array<string, class-string<ModuleInterface>>
     */
    public static function getModulesMap()
    {
        $map = [];

        foreach (self::registerModules() as $class) {
            $map[self::conf($class)->t] = $class;
        }

        return $map;
    }

    public static function getModuleNames()
    {
        $names = [];

        foreach (self::getModulesMap() as $name => $module) {
            $names[$name] = $module::name();
        }

        return $names;
    }

    /**
     * 获取指定名称的模块
     *
     * @param string $t
     * @return class-string<ModuleInterface>
     */
    public static function getModule($t)
    {
        foreach (self::registerModules() as $class) {
            if (self::conf($class)->t == $t) {
                return $class;
            }
        }

        throw new \Exception("No found module '$t'.");
    }

    /**
     * 模块推进
     *
     * @param $t
     * @return void
     */
    public static function moduleAdvance($t)
    {

    }
}
