<?php

namespace App\Services\Ers\Modules;

use App\Libs\Wechat\EasyWechatFactory;
use App\Libs\Wechat\MpTemplate;
use App\Models\Ers\FormOrderData;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Models\Ers\SolutionOrderPreview;
use App\Models\User\UserMpAccount;
use App\Models\WechatNoticeRecord;
use App\Services\Ers\ServiceOrderService;
use App\Services\WechatService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SolutionPreviewModule implements ModuleInterface
{

    public static function configure(): ModuleConfigure
    {
        return new ModuleConfigure('solution_preview', '方案预览模块', '方案预览', SolutionOrderPreview::class);
    }

    public static function convertOrderStepData(ServiceOrder $order, ServiceOrderStep $step): ?array
    {
        if (!$step->data) {
            return null;
        }

        return [
            'files' => FormOrderData::fileUrls($step->data->files)
        ];
    }

    public static function initOrderStep(ServiceOrder $order, ServiceOrderStep $step)
    {
        //要求后台上传预览方案
        $step->status = ServiceOrderStep::STATUS_ADMIN_PENDING;
        $step->save();
    }

    public static function subFlows(ServiceOrder $order, ServiceOrderStep $step)
    {
        return [
            new SubFlow(
                '上传方案',
                SubFlow::status($step, SubFlow::WHO_ADMIN),
                $order->operator,
                'solution_preview:upload',
                $step->last_admin_handled_at
            ),
            new SubFlow(
                '确认方案',
                SubFlow::status($step, SubFlow::WHO_ADMIN, SubFlow::WHO_USER),
                $order->user,
                'solution_preview:confirm',
                $step->last_user_handled_at
            )
        ];
    }

    public static function messageNotice(ServiceOrder $order, ServiceOrderStep $step)
    {
        switch ($step->status) {
            case ServiceOrderStep::STATUS_USER_PENDING:
                // 微信通知
                self::confirmSolutionNotice($order, $step);
                break;
            case ServiceOrderStep::STATUS_ADMIN_PENDING:
                // 短信通知
                ServiceOrderService::sendFlowMessage($order->admin_id);
                break;
        }
    }

    /**
     * 用户方案确认微信消息通知
     *
     * @param ServiceOrder $order
     * @param ServiceOrderStep $step
     * @return void
     */
    public static function confirmSolutionNotice(ServiceOrder $order, ServiceOrderStep $step): void
    {
        $data = [
            'character_string1' => ['value' => $order->sid],
            'phrase7' => ['value' => ServiceOrderStep::statusLabel($step->status)],
            'phrase8' => ['value' => '方案待确认'],
            'thing3' => ['value' => "方案已准备就绪，请及时查看并确认"],
            'date2' => ['value' => Carbon::now()->toDateTimeString()]
        ];

        $templateId = config('easywechat.mp.template_ids')[MpTemplate::SERVICE_ORDER];

        $messageSample = MpTemplate::messageSample($data, MpTemplate::SERVICE_ORDER);

        $page = "pages/services/detail?sid=$order->sid&project_id=$order->project_id&title={$order->project->title}";

        WechatService::commonNotice($order->user_id,  $templateId, $data, $messageSample, WechatNoticeRecord::TYPE_SOLUTION_CONFIRM, $page);
    }

}
