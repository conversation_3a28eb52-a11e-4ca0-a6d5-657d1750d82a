<?php

namespace App\Services\Ers\Modules;

use App\Models\Ers\PaymentOrderPayment;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;

class PaymentStageModule extends PaymentModule
{

    public static function configure(): ModuleConfigure
    {
        return new ModuleConfigure('payment_stage', '预付款模块', '预付款', PaymentOrderPayment::class);
    }

    public static function subFlows(ServiceOrder $order, ServiceOrderStep $step)
    {
        $flows = self::preSubFlows($order, $step);

        $flows[] = new SubFlow(
            '预付款',
            SubFlow::status($step, SubFlow::WHO_ADMIN, SubFlow::WHO_USER),
            $order->user,
            'payment:pay',
            $step->last_user_handled_at
        );

        return $flows;
    }

}
