<?php

namespace App\Services\Ers\Modules;

use App\Exceptions\ServiceException;
use App\Libs\Wechat\MpTemplate;
use App\Models\Ers\PaymentOrderPayment;
use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;
use App\Models\WechatNoticeRecord;
use App\Services\Ers\ServiceOrderService;
use App\Services\Ers\ServiceOrderStepService;
use App\Services\WechatService;
use Carbon\Carbon;

class PaymentModule implements ModuleInterface
{

    public static function configure(): ModuleConfigure
    {
        return new ModuleConfigure('payment', '支付模块', '支付', PaymentOrderPayment::class);
    }

    public static function convertOrderStepData(ServiceOrder $order, ServiceOrderStep $step): ?array
    {
        /** @var ?PaymentOrderPayment $payment */
        $payment = $step->data;

        if ($payment) {
            //预付款尾款模式
            if ($step->module == 'final') {
                //追溯预付款模块
                $advanceStep = ServiceOrderStepService::traceBackStep($step, PaymentStageModule::configure()->t);

                if (!$advanceStep) {
                    throw new ServiceException('未找到预付款流程，无法支付尾款。');
                }

                //尾款模块的数据基于预付款模块的价格而来
                $payment->total_amount = $advanceStep->data->total_amount;
                $payment->pay_amount = $advanceStep->data->total_amount - $advanceStep->data->pay_amount;
                $payment->save();
            }

            return [
                'type' => $payment->type,
                'total_amount' => $payment->total_amount,
                'pay_amount' => $payment->pay_amount,
                'pay_order' => $payment->payOrder
            ];
        }

        return null;
    }

    public static function initOrderStep(ServiceOrder $order, ServiceOrderStep $step)
    {
        if ($step->module == 'payment' || $step->module == 'payment_stage') {
            if ($step->status == ServiceOrderStep::STATUS_PENDING) {
                //后台还未定价的情况下要求后台定价，否则直接扭转给用户
                $step->status = !$step->data_id ? ServiceOrderStep::STATUS_ADMIN_PENDING : ServiceOrderStep::STATUS_USER_PENDING;
                $step->save();
            }
        } else {
            if ($step->status != ServiceOrderStep::STATUS_PENDING) {
                return;
            }

            //如果是到付尾款的流程，则需要追溯到前面预付款流程，以确定尾款价格
            $advanceStep = ServiceOrderStepService::traceBackStep($step, PaymentStageModule::configure()->t);

            if (!$advanceStep) {
                throw new ServiceException('未找到预付款流程，无法支付尾款。');
            }

            //要求用户支付尾款
            $step->status = ServiceOrderStep::STATUS_USER_PENDING;

            if (!$step->data) {
                $payment = new PaymentOrderPayment();
                $payment->order_id = $order->id;
                $payment->order_step_id = $step->id;
                $payment->type = 'final';
                $payment->total_amount = $advanceStep->data->total_amount;
                $payment->pay_amount = bcsub($advanceStep->data->total_amount, $advanceStep->data->pay_amount, 2);
                $payment->save();

                $step->data_id = $payment->id;
            }

            $step->save();
        }
    }

    protected static function preSubFlows(ServiceOrder $order, ServiceOrderStep $step)
    {
        return [
            new SubFlow(
                '服务定价',
                SubFlow::status($step, SubFlow::WHO_ADMIN),
                $order->operator,
                'payment:set',
                $step->last_admin_handled_at
            )
        ];
    }

    public static function subFlows(ServiceOrder $order, ServiceOrderStep $step)
    {
        $flows = self::preSubFlows($order, $step);

        $flows[] = new SubFlow(
            '付款',
            SubFlow::status($step, SubFlow::WHO_ADMIN, SubFlow::WHO_USER),
            $order->user,
            'payment:pay',
            $step->last_user_handled_at
        );

        return $flows;
    }

    public static function messageNotice(ServiceOrder $order, ServiceOrderStep $step)
    {
        switch ($step->status) {
            case ServiceOrderStep::STATUS_USER_PENDING:
                // 微信通知
                if ($step->module == 'payment') {// 全款
                    $message = "请尽快支付款项";
                } elseif ($step->module == 'payment_stage') {// 预付款
                    $message = "资料审核已顺利通过，请尽快支付预付款";
                } else {// 尾款
                    $message = "请尽快支付尾款";
                }
                self::paymentNotice($order, $step, $message);
                break;
            case ServiceOrderStep::STATUS_ADMIN_PENDING:
                // 短信通知
                ServiceOrderService::sendFlowMessage($order->admin_id);
                break;
        }
    }

    /**
     * 用户待付款微信消息通知
     *
     * @param ServiceOrder $order
     * @param ServiceOrderStep $step
     * @param string $message
     * @return void
     */
    public static function paymentNotice(ServiceOrder $order, ServiceOrderStep $step, string $message)
    {
        $data = [
            'character_string1' => ['value' => $order->sid],
            'phrase7' => ['value' => ServiceOrderStep::statusLabel($step->status)],
            'phrase8' => ['value' => '待付款'],
            'thing3' => ['value' => $message],
            'date2' => ['value' => Carbon::now()->toDateTimeString()]
        ];

        $templateId = config('easywechat.mp.template_ids')[MpTemplate::SERVICE_ORDER];

        $messageSample = MpTemplate::messageSample($data, MpTemplate::SERVICE_ORDER);

        $page = "pages/services/detail?sid=$order->sid&project_id=$order->project_id&title={$order->project->title}";

        WechatService::commonNotice($order->user_id,  $templateId, $data, $messageSample, WechatNoticeRecord::TYPE_PAYMENT, $page);
    }

}
