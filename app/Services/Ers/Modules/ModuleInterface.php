<?php

namespace App\Services\Ers\Modules;

use App\Models\Ers\ServiceOrder;
use App\Models\Ers\ServiceOrderStep;

interface ModuleInterface
{

    /**
     * 模块基础配置
     */
    public static function configure(): ModuleConfigure;

    /**
     * 将工单的流程步骤相关的数据转换为前台显示的数组格式
     */
    public static function convertOrderStepData(ServiceOrder $order, ServiceOrderStep $step): ?array;

    /**
     * 当轮到该流程时，初始工单步骤的状态
     */
    //public static function initOrderStepStatus(): int;

    /**
     * 工单流程初始化，当上一个步骤完成轮到此步时，此步骤有必要执行的一些操作，如确定步骤状态，初始化步骤数据等等。
     * 注意这里只初始化 $step 的状态，非特殊情况不需要主动处理 $order 的状态。
     *
     * @param ServiceOrder $order 所在工单
     * @param ServiceOrderStep $step 要初始化的工单步骤
     */
    public static function initOrderStep(ServiceOrder $order, ServiceOrderStep $step);

    /**
     * 获取子流程列表，返回一个模块在流程中所展示出的每个子流程状态列表，
     *
     * @param ServiceOrder $order
     * @param ServiceOrderStep $step
     * @return SubFlow[]
     */
    public static function subFlows(ServiceOrder $order, ServiceOrderStep $step);

    /**
     * 消息通知
     *
     * @param ServiceOrder $order
     * @param ServiceOrderStep $step
     */
    public static function messageNotice(ServiceOrder $order, ServiceOrderStep $step);
}
