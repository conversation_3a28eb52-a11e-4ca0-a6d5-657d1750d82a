<?php

namespace App\Services\Ers;

use App\Exceptions\ServiceException;
use App\Models\Ers\FlowStep;
use App\Models\Ers\FormProjectForm;
use App\Models\Ers\Project;

class FormProjectFormService
{
    /**
     * 获取表单
     * @param int $projectId
     * @param int $stepId
     * @param int $industryId
     * @param int $enterpriseId
     * @return FormProjectForm|null
     */
    public static function getProjectForm(int $projectId, int $stepId, int $industryId, int $enterpriseId): ?FormProjectForm
    {
        /** @var FormProjectForm $model */
        $model = FormProjectForm::query()
            ->where('project_id', $projectId)
            ->where('step_id', $stepId)
            ->where('industry_id', $industryId)
            ->where('enterprise_id', $enterpriseId)
            ->first();

        return $model;
    }

    public static function create(array $params): FormProjectForm
    {
        /** @var Project $project */
        $project = Project::query()->find($params['project_id']);

        if (!$project) {
            throw new ServiceException('服务项目不存在');
        }

        /** @var FlowStep $flowModule */
        $flowModule = FlowStep::query()->find($params['step_id']);

        if (!$flowModule) {
            throw new ServiceException('流程中不存在表单设置');
        }

        /** @var FormProjectForm $model */
        $model = FormProjectForm::query()->firstOrCreate([
            'project_id' => $project->id,
            'step_id' => $flowModule->id,
            'industry_id' => $params['industry_id'],
            'enterprise_id' => $params['enterprise_id'],
        ], [
            'flow_id' => $flowModule->flow_id,
        ]);

        return $model;
    }
}
