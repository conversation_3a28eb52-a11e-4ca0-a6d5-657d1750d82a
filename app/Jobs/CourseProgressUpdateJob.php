<?php

namespace App\Jobs;

use App\Models\Cms\ContentCourseProgressBuffer;
use App\Models\Org\Enrollment;
use App\Services\Org\CourseService;
use DragonCode\Contracts\Queue\ShouldQueue;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CourseProgressUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // 每次处理的最大记录数
    protected $batchSize = 500;

    public function __construct()
    {
        //
    }

    public function handle(): void
    {
        $startTime = microtime(true);
        Log::info('-------------------CourseProgressUpdateJob started');

        try {
            $recordIds = ContentCourseProgressBuffer::query()->pluck('id')->toArray();
            
            if (empty($recordIds)) {
                Log::channel('task')->info("No records to process");
                return;
            }
            
            Log::info('-------------------Found ' . count($recordIds) . ' records to process');
            
            DB::transaction(function () use ($recordIds) {
                // 只处理之前获取的ID对应的记录
                ContentCourseProgressBuffer::query()
                    ->whereIn('id', $recordIds)
                    ->select([
                        'user_id',
                        'content_id',
                        'enroll_id',
                        'org_id',
                    ])
                    ->groupBy(['user_id', 'content_id', 'enroll_id', 'org_id'])
                    ->orderBy('user_id')
                    ->chunk($this->batchSize, function ($groups) {
                        foreach ($groups as $group) {
                            $this->processGroup($group);
                        }
                    });

                ContentCourseProgressBuffer::query()
                    ->whereIn('id', $recordIds)
                    ->delete();
            });

            $duration = round(microtime(true) - $startTime, 2);
            Log::channel('task')->info("CourseProgressUpdateJob completed in {$duration} seconds");
        } catch (\Exception $e) {
            Log::channel('task')->error('CourseProgressUpdateJob failed: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            throw $e;
        }
    }

    protected function processGroup($group): void
    {
        try {
            // 更新学习时长
            CourseService::updateLearnedDuration(
                $group->org_id,
                $group->content_id,
                $group->user_id,
                $group->enroll_id
            );
            
            Log::debug('------------------Updated learned duration', [
                'user_id' => $group->user_id,
                'content_id' => $group->content_id,
                'enroll_id' => $group->enroll_id,
            ]);

        } catch (\Exception $e) {
            Log::channel('task')->error('Failed to process group', [
                'group' => $group,
                'error' => $e->getMessage()
            ]);
        }
    }
}