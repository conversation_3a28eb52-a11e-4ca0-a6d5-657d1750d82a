<?php

namespace App\Jobs;

use App\Core\Enums\BusinessType;
use App\Exceptions\ServiceException;
use App\Models\Org\Export;
use App\Services\Common\AttachmentService;
use App\Services\Org\Export\ExportFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Throwable;
use ZipArchive;

/**
 * 处理导出任务的队列任务
 */
class ProcessExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param int $exportId 导出任务ID
     */
    public function __construct(public int $exportId)
    {
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws Throwable
     */
    public function handle(): void
    {
        $logger = Log::channel('task');
        $export = Export::query()->findOrFail($this->exportId);
        $errors = [];
        $handlerResults = [];

        // 1. 处理导出数据
        $checkedIds = $export->extra['checked_ids'] ?? [0];
        if (empty($checkedIds)) {
            $checkedIds = [0];
        }

        foreach ($checkedIds as $checkedId) {
            try {
                $result = $this->exporter($export->type, $export->org_id, $checkedId, $export->extra);
                if ($result !== null) {
                    $handlerResults[] = $result;
                }
            } catch (Throwable $e) {
                $errors[] = $e->getMessage();
                $logger->error('导出任务部分失败', [
                    'export_id' => $export->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        // 2. 如果有成功结果则处理文件
        if (!empty($handlerResults)) {
            try {
                list($fileName, $tempPath) = $this->handleZipFile($handlerResults, $export->desc);

                // 3. 上传文件并更新状态
                $extension = pathinfo($tempPath, PATHINFO_EXTENSION);
                $time = now()->format('YmdHis');
                $fileName = $fileName . "_{$export->admin_id}_$time.$extension";
                $diskName = config('heguibao.storage.priv');

                $file = AttachmentService::storeFromLocal(
                    $tempPath,
                    $diskName,
                    'exports',
                    BusinessType::Export,
                    $this->exportId,
                    $fileName
                );

                $export->update([
                    'status' => Export::STATUS_COMPLETED,
                    'file' => $file->path,
                ]);

                $logger->info('导出任务完成', [
                    'export_id' => $export->id,
                    'file_path' => $file->path,
                ]);
            } catch (Throwable $e) {
                $errors[] = $e->getMessage();
                $logger->error('导出文件处理失败', [
                    'export_id' => $export->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // 3. 更新最终状态
        $status = !empty($handlerResults) ? (!empty($errors) ? Export::STATUS_PARTLY_COMPLETED : Export::STATUS_COMPLETED) : Export::STATUS_FAILED;
        $export->update([
            'status' => $status,
            'error' => !empty($errors) ? implode(' ', $errors) : ''
        ]);
    }

    /**
     * 处理ZIP文件压缩
     *
     * @param array $handlerResults 处理结果
     * @param string $description 导出描述
     * @return array [fileName, tempPath]
     * @throws ServiceException
     */
    private function handleZipFile(array $handlerResults, string $description): array
    {
        // 单文件且不是目录，直接返回
        if (count($handlerResults) === 1) {
            list($fileName, $tempPath) = $handlerResults[0];

            if (!File::isDirectory($tempPath)) {
                return [$fileName, $tempPath];
            }
        }

        // 创建ZIP文件
        $fileName = date('YmdHis') . uniqid() . '.zip';
        $zipFilePath = AttachmentService::tmpPath($fileName);
        $zip = new ZipArchive();

        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            throw new ServiceException('无法创建 ZIP 文件');
        }

        try {
            // 多文件情况
            if (count($handlerResults) > 1) {
                foreach ($handlerResults as $result) {
                    list($fileName, $filePath) = $result;
                    $this->addFileToZip($zip, $filePath, $fileName);
                }

                return [$description, $zipFilePath];
            }

            // 单目录情况
            list(, $dirPath) = $handlerResults[0];
            $files = glob($dirPath . '/*');
            foreach ($files as $file) {
                $zip->addFile($file, basename($file));
            }
            return [$description, $zipFilePath];
        } finally {
            $zip->close();
        }
    }

    /**
     * 添加文件到ZIP
     *
     * @param ZipArchive $zip
     * @param string $path
     * @param string $entryName
     * @return void
     */
    private function addFileToZip(ZipArchive $zip, string $path, string $entryName): void
    {
        if (File::isDirectory($path)) {
            $zip->addEmptyDir($entryName);
            $files = glob($path . '/*');
            foreach ($files as $file) {
                $zip->addFile($file, $entryName . '/' . basename($file));
            }
        } elseif (file_exists($path)) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            $uniqueName = $entryName . '_' . uniqid() . ".$extension";
            $zip->addFile($path, $uniqueName);
        } else {
            throw new ServiceException("文件不存在: $path");
        }
    }

    /**
     * 创建导出处理器并生成导出文件
     *
     * @param string $type
     * @param int $orgId
     * @param int $checkedId
     * @param array $extra
     * @return array|null 返回 null 代表条件不足
     */
    protected function exporter(string $type, int $orgId, int $checkedId, array $extra): array|null
    {
        $handler = ExportFactory::create($type, $orgId, $checkedId, $extra);
        return $handler->handle();
    }
}
