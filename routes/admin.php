<?php

use App\Http\Controllers\Admin\Admin\AdminController;
use App\Http\Controllers\Admin\Admin\MenuController;
use App\Http\Controllers\Admin\Admin\RoleController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\Chat\ChatMessageController;
use App\Http\Controllers\Admin\Chat\ChatSessionController;
use App\Http\Controllers\Admin\Cms\CategoryController;
use App\Http\Controllers\Admin\Cms\ContentController;
use App\Http\Controllers\Admin\Cms\ContentCourseChapterController;
use App\Http\Controllers\Admin\Cms\ContentCourseController;
use App\Http\Controllers\Admin\Cms\ContentCoursePackageController;
use App\Http\Controllers\Admin\Cms\ContentCourseDocController;
use App\Http\Controllers\Admin\Cms\ContentCourseSectionController;
use App\Http\Controllers\Admin\Cms\ContentCourseStatisticController;
use App\Http\Controllers\Admin\Cms\ContentDocController;
use App\Http\Controllers\Admin\Cms\ContentRichTextController;
use App\Http\Controllers\Admin\Cms\ContentVideoController;
use App\Http\Controllers\Admin\Cms\SpecialController;
use App\Http\Controllers\Admin\Ers\CustomerCluesController;
use App\Http\Controllers\Admin\Ers\EnterpriseController;
use App\Http\Controllers\Admin\Ers\FlowController;
use App\Http\Controllers\Admin\Ers\FormLibraryController;
use App\Http\Controllers\Admin\Ers\FormOrderController;
use App\Http\Controllers\Admin\Ers\FormProjectFormController;
use App\Http\Controllers\Admin\Ers\FormProjectInputController;
use App\Http\Controllers\Admin\Ers\IndustryController;
use App\Http\Controllers\Admin\Ers\PaymentOrderController;
use App\Http\Controllers\Admin\Ers\ProjectController;
use App\Http\Controllers\Admin\Ers\ServiceOrderController;
use App\Http\Controllers\Admin\Ers\SolutionDownloadOrderController;
use App\Http\Controllers\Admin\Ers\SolutionPreviewOrderController;
use App\Http\Controllers\Admin\ExpertController;
use App\Http\Controllers\Admin\InvitationController;
use App\Http\Controllers\Admin\Order\OrderController;
use App\Http\Controllers\Admin\OrgController;
use App\Http\Controllers\Admin\Punish\TestController as PunishTestController;
use App\Http\Controllers\Admin\Punish\TestSubjectController as PunishTestSubjectController;
use App\Http\Controllers\Admin\Qa\AnswerController;
use App\Http\Controllers\Admin\Qa\QuestionController;
use App\Http\Controllers\Admin\Stat\DailyOverviewController;
use App\Http\Controllers\Admin\Stat\DailyPromoterController;
use App\Http\Controllers\Admin\System\AttachmentFileController;
use App\Http\Controllers\Admin\System\AttachmentRelationController;
use App\Http\Controllers\Admin\System\SettingBoothController;
use App\Http\Controllers\Admin\System\UploadController;
use App\Http\Controllers\Admin\Train\ChapterController;
use App\Http\Controllers\Admin\Train\SectionController;
use App\Http\Controllers\Admin\Train\SubjectController;
use App\Http\Controllers\Admin\Train\TopicController;
use App\Http\Controllers\Admin\User\BalanceRecordController;
use App\Http\Controllers\Admin\User\CreditLogController;
use App\Http\Controllers\Admin\User\OpenCourseRecordController;
use App\Http\Controllers\Admin\User\OwnContentController;
use App\Http\Controllers\Admin\User\OwnTopicController;
use App\Http\Controllers\Admin\User\UserController;
use App\Http\Controllers\Admin\Org\AdminController as OrgAdminController;
use App\Http\Controllers\Admin\Org\CourseController as OrgCourseController;
use App\Http\Controllers\Admin\Org\TopicController as OrgTopicController;
use App\Http\Controllers\Admin\Org\CoursePackController as OrgCoursePackController;
use App\Http\Controllers\Admin\Org\BalanceController as OrgBalanceController;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'auth'], function () {
    Route::post('login', [AuthController::class, 'login'])->name('admin.login');
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('me', [AuthController::class, 'me']);
    Route::post('menus', [AuthController::class, 'menus']);
});

Route::middleware('auth.admin')->group(function () {
    Route::get('admins', [AdminController::class, 'index']);
    Route::get('admins/{id}', [AdminController::class, 'show'])->whereNumber('id');
    Route::post('admins', [AdminController::class,  'store']);
    Route::put('admins/{id}', [AdminController::class, 'update'])->whereNumber('id');
    Route::put('admins/{id}/updatePassword', [AdminController::class, 'updatePassword'])->whereNumber('id');

    Route::get('roles', [RoleController::class, 'index']);
    Route::get('roles/search', [RoleController::class, 'search']);
    Route::post('roles', [RoleController::class, 'store']);
    Route::put('roles/{id}', [RoleController::class, 'update'])->whereNumber('id');
    Route::delete('roles/{id}', [RoleController::class, 'destroy'])->whereNumber('id');

    Route::get('menus', [MenuController::class, 'index']);
    Route::post('menus', [MenuController::class, 'store']);
    Route::put('menus/{id}', [MenuController::class, 'update'])->whereNumber('id');
    Route::delete('menus/{id}', [MenuController::class, 'destroy'])->whereNumber('id');

    Route::prefix('system')->group(function () {
        Route::post('upload/config', [UploadController::class, 'config']);
        Route::get('attachmentFiles', [AttachmentFileController::class, 'index']);
        Route::get('attachmentRelations', [AttachmentRelationController::class, 'index']);

        Route::get('booths', [SettingBoothController::class, 'index']);
        Route::post('booths', [SettingBoothController::class, 'store']);
        Route::put('booths/{id}', [SettingBoothController::class, 'update'])->whereNumber('id');
        Route::delete('booths/{id}', [SettingBoothController::class, 'destroy'])->whereNumber('id');
    });

    Route::prefix('stat')->group(function () {
        Route::get('dailyOverview/permission', [DailyOverviewController::class, 'permission']);
        Route::get('dailyOverview/permission/stat', [DailyOverviewController::class, 'permissionStat']);
        Route::get('dailyOverview/getSubtotalData', [DailyOverviewController::class, 'getSubtotalData']);
        Route::get('dailyOverview/getDateList', [DailyOverviewController::class, 'getDateList']);
        Route::get('dailyPromoter/promoters', [DailyPromoterController::class, 'promoter']);
        Route::get('dailyPromoter/getSubtotalData', [DailyPromoterController::class, 'getSubtotalData']);
        Route::get('dailyPromoter/getDateList', [DailyPromoterController::class, 'getDateList']);
        Route::get('rankList', [DailyPromoterController::class, 'rankList']);
        Route::get('adminContentRank', [DailyPromoterController::class, 'adminContentRank']);
    });

    Route::get('users', [UserController::class, 'index']);
    Route::get('users/{id}', [UserController::class, 'show'])->whereNumber('id');
    Route::put('users/{id}', [UserController::class, 'update'])->whereNumber('id');
    Route::get('users/search', [UserController::class, 'search']);
    Route::put('users/{id}/credit', [UserController::class, 'credit']);
    Route::put('users/{id}/balance', [UserController::class, 'changeBalance']);

    Route::prefix('user')->group(function () {
        Route::get('ownContents', [OwnContentController::class, 'index']);
        Route::delete('ownContents/{id}', [OwnContentController::class, 'destroy'])->whereNumber('id');
        Route::get('ownTopics', [OwnTopicController::class, 'index']);
        Route::delete('ownTopics/{id}', [OwnTopicController::class, 'destroy'])->whereNumber('id');
        Route::get('creditLogs', [CreditLogController::class, 'index']);
        Route::get('balanceRecords', [BalanceRecordController::class, 'index']);
        Route::get('invitations', [InvitationController::class, 'index']);
        Route::get('openCourseRecords', [OpenCourseRecordController::class, 'index']);
        Route::get('openCourseBatches', [OpenCourseRecordController::class, 'batch']);
        Route::post('openCourseRecords/import', [OpenCourseRecordController::class, 'import']);
        Route::get('openCourseRecords/template', [OpenCourseRecordController::class, 'template']);
    });

    Route::get('orgs', [OrgController::class, 'index']);
    Route::post('orgs', [OrgController::class, 'store']);
    Route::put('orgs/{id}', [OrgController::class, 'update'])->whereNumber('id');
    Route::delete('orgs/{id}', [OrgController::class, 'destroy'])->whereNumber('id');
    Route::get('orgs/search', [OrgController::class, 'search']);

    Route::prefix('org/{oid}')->group(function () {
        // 机构余额记录添加
        Route::post('balances', [OrgBalanceController::class, 'store']);
        Route::get('balances', [OrgBalanceController::class, 'index']);

        // 机构管理员-主账号
        Route::post('admins', [OrgAdminController::class, 'store']);
        Route::put('admins/password/{id}', [OrgAdminController::class, 'updatePassword'])->whereNumber('id');

        // 机构课程
        Route::apiResource('courses', OrgCourseController::class);

        // 机构题库
        Route::apiResource('topics', OrgTopicController::class);

        // 机构课程包
        Route::apiResource('course-packs', OrgCoursePackController::class);
    })->whereNumber('oid');

    Route::prefix('cms')->group(function () {
        Route::get('categories', [CategoryController::class, 'index']);
        Route::post('categories', [CategoryController::class, 'store']);
        Route::put('categories/{id}', [CategoryController::class, 'update'])->whereNumber('id');
        Route::delete('categories/{id}', [CategoryController::class, 'destroy'])->whereNumber('id');

        Route::get('contents', [ContentController::class, 'index']);
        Route::get('contents/search', [ContentController::class, 'search']);
        Route::post('contents', [ContentController::class, 'store']);
        Route::get('contents/{id}', [ContentController::class, 'show'])->whereNumber('id');
        Route::put('contents/{id}', [ContentController::class, 'update'])->whereNumber('id');
        Route::delete('contents/{id}', [ContentController::class, 'destroy'])->whereNumber('id');
        Route::delete('contents/batchDelete', [ContentController::class, 'batchDestroy']);

        Route::get('contentDocs/{id}', [ContentDocController::class, 'show'])->whereNumber('id');
        Route::put('contentDocs/{id}', [ContentDocController::class, 'update'])->whereNumber('id');
        Route::post('contentDocs/createScreenshotTask', [ContentDocController::class, 'createScreenshotTask']);
        Route::post('contentDocs/getScreenshotResult', [ContentDocController::class, 'getScreenshotResult']);

        Route::get('contentVideos/search', [ContentVideoController::class, 'search']);
        Route::get('contentVideos/{id}', [ContentVideoController::class, 'show'])->whereNumber('id');
        Route::put('contentVideos/{id}', [ContentVideoController::class, 'update'])->whereNumber('id');

        Route::get('contentRichTexts/{id}', [ContentRichTextController::class, 'show'])->whereNumber('id');
        Route::put('contentRichTexts/{id}', [ContentRichTextController::class, 'update'])->whereNumber('id');
        Route::post('contentRichTexts/saveAttachment', [ContentRichTextController::class, 'saveAttachment']);

        Route::get('contentCourses/{id}', [ContentCourseController::class, 'show'])->whereNumber('id');
        Route::put('contentCourses/{id}', [ContentCourseController::class, 'update'])->whereNumber('id');
        Route::get('contentCourseChapters/children', [ContentCourseChapterController::class, 'children']);
        Route::post('contentCourseChapters', [ContentCourseChapterController::class, 'store']);
        Route::put('contentCourseChapters/{id}', [ContentCourseChapterController::class, 'update'])->whereNumber('id');
        Route::delete('contentCourseChapters/{id}', [ContentCourseChapterController::class, 'destroy'])->whereNumber('id');
        Route::post('contentCourseSections', [ContentCourseSectionController::class, 'store']);
        Route::put('contentCourseSections/{id}', [ContentCourseSectionController::class, 'update'])->whereNumber('id');
        Route::delete('contentCourseSections/{id}', [ContentCourseSectionController::class, 'destroy'])->whereNumber('id');

        Route::get('content/coursePackages/{id}', [ContentCoursePackageController::class, 'show'])->whereNumber('id');
        Route::put('content/coursePackages/{id}', [ContentCoursePackageController::class, 'update'])->whereNumber('id');

        Route::get('specials', [SpecialController::class, 'index']);
        Route::post('specials', [SpecialController::class, 'store']);
        Route::get('specials/{id}', [SpecialController::class, 'show'])->whereNumber('id');
        Route::put('specials/{id}', [SpecialController::class, 'update'])->whereNumber('id');
        Route::put('specials/{id}/recommend', [SpecialController::class, 'recommend']);
        Route::delete('specials/{id}', [SpecialController::class, 'destroy'])->whereNumber('id');

        Route::get('contentCourseDocs', [ContentCourseDocController::class, 'index']);
        Route::post('contentCourseDocs', [ContentCourseDocController::class, 'store']);
        Route::put('contentCourseDocs/{id}', [ContentCourseDocController::class, 'update'])->whereNumber('id');
        Route::delete('contentCourseDocs/{id}', [ContentCourseDocController::class, 'destroy'])->whereNumber('id');

        Route::get('contentCourseStatistics', [ContentCourseStatisticController::class, 'index']);
        Route::get('contentCourseStatistics/statistic', [ContentCourseStatisticController::class, 'statistic']);
        Route::get('contentCourseStatistics/export', [ContentCourseStatisticController::class, 'export']);
    });

    Route::prefix('order')->group(function () {
        Route::get('orders', [OrderController::class, 'index']);
    });

    Route::prefix('qa')->group(function () {
        Route::get('questions', [QuestionController::class, 'index']);
        Route::get('questions/{id}/answers', [QuestionController::class, 'answers'])->whereNumber('id');
        Route::put('questions/{id}/recommend', [QuestionController::class, 'recommend'])->whereNumber('id');
        Route::delete('questions/{id}', [QuestionController::class, 'destroy'])->whereNumber('id');
        Route::delete('questions/batchDelete', [QuestionController::class, 'batchDestroy']);

        Route::get('answers', [AnswerController::class, 'index']);
        Route::delete('answers/{id}', [AnswerController::class, 'destroy'])->whereNumber('id');
        Route::delete('answers/batchDelete', [AnswerController::class, 'batchDestroy']);
    });

    Route::prefix('chat')->group(function () {
        Route::get('sessions', [ChatSessionController::class, 'index']);

        Route::get('messages', [ChatMessageController::class, 'index']);
    });

    Route::prefix('train')->group(function () {
        Route::get('topics', [TopicController::class, 'index']);
        Route::get('topics/search', [TopicController::class, 'search']);
        Route::post('topics', [TopicController::class, 'store']);
        Route::put('topics/{id}', [TopicController::class, 'update'])->whereNumber('id');
        Route::put('topics/{id}/import', [TopicController::class, 'import'])->whereNumber('id');

        Route::get('chapters', [ChapterController::class, 'index']);
        Route::get('chapters/search', [ChapterController::class, 'search']);
        Route::post('chapters', [ChapterController::class, 'store']);
        Route::put('chapters/{id}', [ChapterController::class, 'update'])->whereNumber('id');
        Route::delete('chapters/{id}', [ChapterController::class, 'destroy'])->whereNumber('id');

        Route::get('sections', [SectionController::class, 'index']);
        Route::post('sections', [SectionController::class, 'store']);
        Route::put('sections/{id}', [SectionController::class, 'update'])->whereNumber('id');
        Route::delete('sections/{id}', [SectionController::class, 'destroy'])->whereNumber('id');

        Route::get('subjects', [SubjectController::class, 'index']);
        Route::post('subjects', [SubjectController::class, 'store']);
        Route::get('subjects/{id}', [SubjectController::class, 'show'])->whereNumber('id');
        Route::put('subjects/{id}', [SubjectController::class, 'update'])->whereNumber('id');
        Route::delete('subjects/{id}', [SubjectController::class, 'destroy'])->whereNumber('id');
        Route::delete('subjects/batchDelete', [SubjectController::class, 'batchDestroy']);
        Route::get('subjects/getTypeCount', [SubjectController::class, 'getTypeCount']);
    });

    Route::prefix('punish')->group(function () {
        Route::get('tests', [PunishTestController::class, 'index']);
        Route::get('testSubjects', [PunishTestSubjectController::class, 'index']);
    });

    Route::prefix('ers')->group(function () {
        Route::get('industryCategories/search', [IndustryController::class, 'search']);
        Route::post('industryCategories', [IndustryController::class, 'store']);
        Route::put('industryCategories/{id}', [IndustryController::class, 'update'])->whereNumber('id');
        Route::put('industryCategories/move', [IndustryController::class, 'move']);
        Route::delete('industryCategories/{id}', [IndustryController::class, 'destroy'])->whereNumber('id');

        Route::get('enterpriseCategories/search', [EnterpriseController::class, 'search']);
        Route::post('enterpriseCategories', [EnterpriseController::class, 'store']);
        Route::put('enterpriseCategories/{id}', [EnterpriseController::class, 'update'])->whereNumber('id');
        Route::put('enterpriseCategories/move', [EnterpriseController::class, 'move']);
        Route::delete('enterpriseCategories/{id}', [EnterpriseController::class, 'destroy'])->whereNumber('id');

        Route::get('formLibraries/search', [FormLibraryController::class, 'search']);
        Route::get('formLibraries', [FormLibraryController::class, 'index']);
        Route::post('formLibraries', [FormLibraryController::class, 'store']);
        Route::put('formLibraries/{id}', [FormLibraryController::class, 'update'])->whereNumber('id');
        Route::delete('formLibraries/{id}', [FormLibraryController::class, 'destroy'])->whereNumber('id');

        Route::get('flows/search', [FlowController::class, 'search']);

        Route::get('projects', [ProjectController::class, 'index']);
        Route::get('projects/search', [ProjectController::class, 'search']);
        Route::get('projects/{id}', [ProjectController::class, 'show'])->whereNumber('id');
        Route::post('projects', [ProjectController::class, 'store']);
        Route::put('projects/{id}', [ProjectController::class, 'update'])->whereNumber('id');
        Route::delete('projects/{id}', [ProjectController::class, 'destroy'])->whereNumber('id');

        Route::post('projectForms/getForm', [FormProjectFormController::class, 'getForm']);

        Route::post('projectInputs', [FormProjectInputController::class, 'store']);
        Route::put('projectInputs/{id}', [FormProjectInputController::class, 'update'])->whereNumber('id');
        Route::delete('projectInputs/{id}', [FormProjectInputController::class, 'destroy'])->whereNumber('id');

        Route::get('serviceOrders', [ServiceOrderController::class, 'index']);
        Route::get('serviceOrders/{id}', [ServiceOrderController::class, 'show'])->whereNumber('id');
        Route::post('serviceOrders/getOperators', [ServiceOrderController::class, 'getOperators']);
        Route::post('serviceOrders/updateOperator', [ServiceOrderController::class, 'updateOperator']);
        Route::post('serviceOrders/{id}/finish', [ServiceOrderController::class, 'finish']);

        Route::post('serviceOrders/{orderId}/form/{stepId}', [FormOrderController::class, 'store'])->whereNumber('orderId')->whereNumber('stepId');
        Route::post('serviceOrders/{orderId}/payment/{stepId}', [PaymentOrderController::class, 'store'])->whereNumber('orderId')->whereNumber('stepId');
        Route::get('serviceOrders/{orderId}/solutionPreview/{stepId}/uploadForm', [SolutionPreviewOrderController::class, 'uploadForm'])->whereNumber('orderId')->whereNumber('stepId');
        Route::post('serviceOrders/{orderId}/solutionPreview/{stepId}', [SolutionPreviewOrderController::class, 'store'])->whereNumber('orderId')->whereNumber('stepId');
        Route::post('serviceOrders/{orderId}/solutionDownload/{stepId}', [SolutionDownloadOrderController::class, 'store'])->whereNumber('orderId')->whereNumber('stepId');

        // 客户线索
        Route::get('customClues', [CustomerCluesController::class, 'index']);
    });

    Route::get('experts', [ExpertController::class, 'index']);
    Route::get('experts/config', [ExpertController::class, 'config']);
    Route::post('experts', [ExpertController::class, 'store']);
    Route::put('experts/{id}', [ExpertController::class, 'update'])->whereNumber('id');
    Route::put('experts/{id}/reject', [ExpertController::class, 'reject'])->whereNumber('id');
    Route::put('experts/{id}/resolve', [ExpertController::class, 'resolve'])->whereNumber('id');

    Route::get('sms', [\App\Http\Controllers\Admin\User\SmsController::class, 'index']);

});
