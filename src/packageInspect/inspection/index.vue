<template>
  <view class="container">
    <!-- Background image and header part -->
    <view class="header">
      <image class="bg-image" src="/static/images/public/industrial-bg.jpg" mode="aspectFill"></image>
      <view class="overlay"></view>
      <view class="back-btn" @click="goBack">
        <image src="@/static/images/icon/arrow-left-s-line.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- Menu options grid -->
    <view class="menu-container">
      <view class="menu-grid">
        <view class="menu-item" @click="navigateTo('task/index')">
          <view class="icon-box">
            <image class="menu-icon" src="/static/images/icon/create.png" mode="aspectFit"></image>
          </view>
          <text class="menu-text">任务创建</text>
        </view>
        <view class="menu-item" @click="navigateTo('device/index')">
          <view class="icon-box">
            <image class="menu-icon" src="/static/images/icon/list.png" mode="aspectFit"></image>
          </view>
          <text class="menu-text">设备列表</text>
        </view>
        <view class="menu-item" @click="navigateTo('approve/index')">
          <view class="icon-box">
            <image class="menu-icon" src="/static/images/icon/approve.png" mode="aspectFit"></image>
          </view>
          <text class="menu-text">审批进度</text>
        </view>
        <view class="menu-item" @click="navigateTo('hidden-danger/index')">
          <view class="icon-box">
            <image class="menu-icon" src="/static/images/icon/camera.png" mode="aspectFit"></image>
          </view>
          <text class="menu-text">随手拍</text>
        </view>
      </view>
    </view>

    <!-- My Tasks section -->
    <view class="tasks-section">
      <view class="section-header">
        <text class="section-title">我的任务</text>
        <text class="section-more" @click="navigateTo('task/my-task')">查看更多 >></text>
      </view>

      <!-- 没有任务时显示 -->
      <view v-if="tasks.length === 0" class="empty-tasks">
        <button class="create-task-btn" @click="navigateTo('task/index')">创建任务</button>
        <text class="empty-text">任务空空的 ~</text>
      </view>

      <!-- 有任务时显示 -->
      <view class="task-card" v-for="(task, index) in tasks" :key="index" v-else>
        <view class="task-header">
          <text class="task-title">{{task.name}}</text>
          <text class="task-record" @click="navigateTo('task-device/record?task_id=' + task.id + '&task_name=' + task.name)">巡检记录</text>
        </view>
        <view class="task-info">
          <view class="info-item" @click="navigateTo('task-device/index?task_id=' + task.id + '&task_name=' + task.name)">
            <text class="info-value">{{task.device_count}}</text>
            <text class="info-label">设备数</text>
          </view>
          <view class="info-item" @click="navigateTo('approve/index?task_id=' + task.id)">
            <text class="info-value">{{task.pending_approval_count}}</text>
            <text class="info-label">审批项</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api.js";
import urls  from "@/lib/urls";
import {alert} from "@/lib/utils";
import { useUserStore } from "@/store/user";
import {getAppName} from "@/lib/context";
import {mapState} from "pinia";

export default {
    data() {
        return {
            appName: getAppName(),
            tasks: [],
        }
    },
    onShareAppMessage() {
        return {
            title: this.appName,
            path: "/packageInspect/inspection/index",
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
        };
    },
    onShow() {
      this.goLogin();
      this.loadTasks();
    },
    onLoad() {
    },
    onPullDownRefresh() {
        this.loadTasks();
    },
    computed: {
        ...mapState(useUserStore, ['user']),
    },

    methods: {
        goLogin(){
          if (!this.user) {
            setTimeout(() => {
              uni.showToast({
                title: '请先登录, 跳转中...',
                icon: 'none'
              });
            }, 500);

            setTimeout(() => {
              uni.navigateTo({
                url: '/pages/login/index'
              });
            }, 1500);
          }
        },
        
        goBack() {
          uni.navigateBack({
            delta: 1
          })
        },

        loadTasks() {
            api.get(urls.taskList,  {limit: 3}).then(res => {
                console.log(res)
                this.tasks = res;
            }).catch(err => {
                console.log(err.message)
            });
        },
        navigateTo(page) {
            // Navigate to different pages based on menu selection
            uni.navigateTo({
                url: '/packageInspect/inspection/' + page
            });
        },
        viewTaskRecord(taskId) {
          // Navigate to task record page
          uni.navigateTo({
            url: '/packageInspect/taskRecord/taskRecord?id=' + taskId
          });
        }
    }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  position: relative;
  height: 350rpx;
}

.bg-image {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.menu-container {
  margin-top: -70rpx;
  padding: 30rpx 30rpx 0;
  z-index: 3;
  position: relative;
}

.menu-grid {
  display: flex;
  background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  padding: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.menu-item {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 0;
  transition: all 0.3s ease;
}

.menu-item:active {
  transform: scale(0.96);
}

.icon-box {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #3388ff 0%, #66a6ff 100%);
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(51, 136, 255, 0.2);
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  display: block;
  filter: brightness(0) invert(1);
}

.menu-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  position: relative;
}

.menu-text::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 0;
  width: 0;
  height: 2rpx;
  background-color: #3388ff;
  transition: width 0.3s ease;
}

.menu-item:active .menu-text::after {
  width: 100%;
}

.tasks-section {
  padding: 30rpx;
  margin-top: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #999;
}

.task-card {
  background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.task-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
  margin-bottom: 16rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5rpx;
}

.task-record {
  font-size: 26rpx;
  color: #3388ff;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  background-color: rgba(51, 136, 255, 0.1);
}

.task-info {
  display: flex;
  padding-top: 8rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50%;
  padding: 8rpx 0;
}

.info-value {
  font-size: 38rpx;
  font-weight: 700;
  color: #3388ff;
  margin-bottom: 6rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.empty-tasks {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300rpx;
  padding: 0;
}

.create-task-btn {
  background-color: #3388ff;
  color: #ffffff;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  margin-bottom: 18rpx;
  border: none;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  color: #999;
}

.back-btn {
  position: absolute;
  top: 100rpx;
  left: 30rpx;
  z-index: 4;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.back-btn image {
  width: 50rpx;
  height: 50rpx;
  filter: brightness(0) invert(1);
}

.back-btn:active {
  transform: scale(0.9);
  opacity: 0.8;
}
</style>