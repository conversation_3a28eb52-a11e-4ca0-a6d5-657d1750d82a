<template>
  <view class="container">
    <view class="form-panel">
      <view class="form-header">
        <view class="title-area">
          <text class="form-title">AI辅助创建</text>
        </view>
        <view class="close-btn" @click="closeDialog">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="form-content">
        <view class="form-item">
          <text class="form-label">设备名称:</text>
          <input type="text" class="form-input" v-model="deviceName" placeholder="请输入设备名称" />
        </view>
        
        <view class="form-item">
          <text class="form-label">建议巡检项:</text>
          <view class="form-input-group">
            <view v-for="item in inspectionItems" :key="item.id" class="form-input-container">
              <input 
                type="text" 
                class="form-input" 
                v-model="item.name" 
                @blur="validateInput(item)"
              />
              <button 
                class="delete-btn" 
                @click="removeInspectionItem(item.id)"
                :hover-class="'delete-btn-hover'"
              >
                <text class="input-icon">×</text>
              </button>
            </view>
            
            <view class="add-item-btn" @click="addInspectionItem">
              <text class="add-item-text">+ 添加巡检项目</text>
            </view>
          </view>
        </view>
        
        <view class="form-footer">
          <button class="submit-btn" type="primary" @click="applyChanges">应用</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api";
import urls from "@/lib/urls";

export default {
  components: {
  },
  data() {
    return {
      deviceName: '',
      inspectionItems: [],
      backgroundImage: ''
    }
  },
  
  onLoad(options) {
    // 确保前一个页面的loading已关闭
    uni.hideLoading();
    
    // 短暂延迟后显示新loading
    setTimeout(() => {
      try {
        if (options.data) {
          const data = JSON.parse(decodeURIComponent(options.data));
          this.deviceName = data['设备名称'] || '';
          
          this.inspectionItems = data['巡检项目'].map((item, index) => ({
            id: index + 1,
            name: item
          }));
        }
        
        if (options.imageUrl) {
          this.backgroundImage = decodeURIComponent(options.imageUrl);
        }
        
        // 数据加载完成后短暂显示成功提示
        setTimeout(() => {
          uni.hideLoading();
          uni.showToast({
            title: '加载完成',
            icon: 'success',
            duration: 1000
          });
        }, 300);
      } catch (e) {
        console.error('加载数据出错:', e);
        uni.hideLoading();
        uni.showToast({
          title: '加载数据失败: ' + (e.message || '数据格式错误'),
          icon: 'none',
          duration: 1500
        });
        
        // 失败后延迟返回
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    }, 300);
  },

  methods: {
    testClick() {
      console.log('Test click received!');
      uni.showToast({
        title: '点击事件测试成功',
        icon: 'none'
      });
    },
    removeInspectionItem(id) {
      this.inspectionItems = this.inspectionItems.filter(item => item.id !== id);
      uni.showToast({
        title: '已删除巡检项',
        icon: 'success'
      });
    },
    addInspectionItem() {
      // 检查最后一项是否为空或重复
      if (this.inspectionItems.length > 0) {
        const lastItem = this.inspectionItems[this.inspectionItems.length - 1];
        if (!lastItem.name.trim()) {
          uni.showToast({
            title: '请先填写当前巡检项',
            icon: 'none'
          });
          return;
        }
        
        // 检查是否与已有巡检项重复
        const duplicate = this.inspectionItems.slice(0, -1).some(
          item => item.name === lastItem.name
        );
        if (duplicate) {
          return; // validateInput已经处理了提示和清空
        }
      }
      
      const newId = this.inspectionItems.length > 0 
        ? Math.max(...this.inspectionItems.map(item => item.id)) + 1
        : 1;
      this.inspectionItems.push({
        id: newId,
        name: ''
      });
    },
    applyChanges() {
      // 检查所有巡检项是否有效
      let hasEmpty = false;
      let hasDuplicate = false;
      
      for (let i = 0; i < this.inspectionItems.length; i++) {
        const item = this.inspectionItems[i];
        if (!item.name.trim()) {
          hasEmpty = true;
          break;
        }
        
        // 检查是否与已有巡检项重复
        const duplicate = this.inspectionItems.slice(0, i).some(
          other => other.name === item.name
        );
        if (duplicate) {
          hasDuplicate = true;
          break;
        }
      }
      
      if (hasEmpty) {
        uni.showToast({
          title: '请填写所有巡检项',
          icon: 'none'
        });
        return;
      }
      
      if (hasDuplicate) {
        return; // validateInput已经处理了提示
      }
      
      const params = {
        name: this.deviceName,
        item_names: this.inspectionItems.map(item => item.name),
        image_url: this.backgroundImage
      };
      
      api.post(urls.aiIdentify, params).then(res => {
        uni.showToast({
          title: '应用成功',
          icon: 'success'
        });

        uni.$emit('refreshDeviceList');
        
        // 成功后返回上一页
        setTimeout(() => {
          uni.navigateBack({ delta: 2 })
        }, 1000);
      }).catch(err => {
        uni.showToast({
          title: '保存失败: ' + (err.message || '网络错误'),
          icon: 'none'
        });
        console.error('API调用失败:', err);
      });

      
    },
    closeDialog() {
      uni.navigateBack()
    },
    validateInput(item) {
      // 过滤首尾空格
      item.name = item.name.trim();
      
      if (!item.name) {
        uni.showToast({
          title: '巡检项不能为空',
          icon: 'none'
        });
        
        // 使用uniapp的API聚焦输入框
        this.$nextTick(() => {
          const query = uni.createSelectorQuery().in(this);
          query.select('.form-input-with-icon:last-child input').boundingClientRect();
          query.exec((res) => {
            if (res[0]) {
              uni.pageScrollTo({
                scrollTop: res[0].top - 50,
                duration: 300
              });
            }
          });
        });
        return;
      }
      
      // 检查是否与已有巡检项重复
      const duplicate = this.inspectionItems.some(
        i => i.id !== item.id && i.name === item.name
      );
      
      if (duplicate) {
        uni.showToast({
          title: '不能添加重复的巡检项',
          icon: 'none'
        });
        // item.name = '';
      }
    }
  }
}
</script>

<style>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-image: v-bind("'url(' + backgroundImage + ')'");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.form-panel {
  width: 90%;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
}

.form-title {
  font-size: 18px;
  font-weight: bold;
}

.close-icon {
  font-size: 24px;
  color: #999;
}

.form-content {
  padding: 15px;
}

.form-item {
  margin-bottom: 15px;
}

.form-label {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.form-input-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-input-container {
  position: relative;
}

.form-input {
  width: 100%;
  height: 40px;
  padding: 0 40px 0 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  background-color: #fff;
  line-height: 40px;
  box-sizing: border-box;
}

.delete-btn {
  position: absolute;
  right: 0;
  top: 0;
  width: 40px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  z-index: 10;
}

.delete-btn-hover {
  background: rgba(255,0,0,0.1) !important;
}

.input-icon {
  color: #f56c6c;
  font-size: 18px;
  font-weight: bold;
}

.add-item-btn {
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}

.add-item-text {
  color: #666;
  font-size: 14px;
}

.form-footer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.submit-btn {
  width: 120px;
  height: 40px;
  background-color: #1989fa;
  color: #ffffff;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}
</style>