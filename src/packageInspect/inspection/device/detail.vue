<template>
  <view class="container">
    <view class="device-info">
      <view class="info-row">
        <text class="info-label">设备名称</text>
        <text class="info-value">{{deviceName}}</text>
      </view>
      
      <view class="device-image-section">
        <text class="info-label">设备图片</text>
        <image v-if="deviceImage" :src="deviceImage" mode="aspectFit" class="device-image" @click="previewImage" />
        <image v-else src="@/images/empty.png" mode="aspectFit" class="image-placeholder"></image>
      </view>
      
      <view class="divider"></view>
      
      <view class="remarks-section" v-if="deviceRemark">
        <text class="remarks-label">备注</text>
        <text class="remarks-content">{{deviceRemark}}</text>
      </view>
      
      <view class="options-section">
        <text class="options-label">巡检项</text>
        <view class="options-table">
          <view class="table-row" v-for="(item, index) in optionItems" :key="item.id">
            <view class="table-cell number-cell">
              <text>{{index + 1}}</text>
            </view>
            <view class="table-cell content-cell">
              <text>{{item.name}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="button-group">
      <view class="btn delete-btn" @click="deleteDevice">
        <text>删除</text>
      </view>
      <view class="btn edit-btn" @click="editDevice">
        <text>编辑</text>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api";
import urls from "@/lib/urls";

export default {
  data() {
    return {
      optionItems: [],
      deviceId: 0,
      deviceName: '',
      deviceImage: '',
      deviceRemark: ''
    };
  },

  onLoad(options) {
    this.deviceId = options.id;
    this.getDetail();
  },

  methods: {
    editDevice() {
      uni.navigateTo({
        url: `/packageInspect/inspection/device/form?id=${this.deviceId}`
      });
    },
    
    getDetail () {
      api.get(urls.deviceDetail(this.deviceId)).then(res => {
        this.deviceName = res.name;
        this.deviceImage = res.image_url;
        this.deviceRemark = res.remark;
        this.optionItems = res.items;
      }).catch(err => {
        console.error('获取设备详情失败:', err);
        uni.showToast({
          title: err.message || '获取设备详情失败',
          icon: 'none'
        });
      });
    },
    previewImage() {
      if (!this.deviceImage) {
        return;
      }
      try {
        uni.previewImage({
          urls: [this.deviceImage],
          fail: (err) => {
            console.error('图片预览失败:', err);
            uni.showToast({
              title: '图片预览失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('图片预览异常:', error);
        uni.showToast({
          title: '图片预览异常',
          icon: 'none'
        });
      }
    },
    
    deleteDevice() {
      uni.showModal({
        title: '提示',
        content: '确定要删除该设备吗？',
        success: (res) => {
          if (res.confirm) {
            api.delete(urls.deleteDevice(this.deviceId))
              .then(() => {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                
                setTimeout(() => {
                  uni.redirectTo({
                    url: 'index'
                  });
                }, 1000);

              })
              .catch(err => {
                console.error('删除设备失败:', err);
                uni.showToast({
                  title: err.message || '删除设备失败',
                  icon: 'none'
                });
              });
          }
        }
      });
    }
  }
};
</script>

<style>
:root {
  --primary-color: #4361ee;
  --danger-color: #f72585;
  --bg-color: #f5f7fa;
  --card-bg: #ffffff;
  --text-color: #2b2d42;
  --text-secondary: #6c757d;
  --border-radius: 12rpx;
  --box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-color);
  position: relative;
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为按钮组预留空间 */
}

.device-info {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 30rpx;
  flex: 1 1 auto;
  box-shadow: var(--box-shadow);
  overflow: visible;
}

.info-row {
  display: flex;
  padding: 24rpx 0;
}

.info-label {
  color: var(--text-color);
  font-size: 30rpx;
  font-weight: 500;
  margin-right: 20rpx;
  min-width: 140rpx;
}

.info-value {
  color: var(--text-secondary);
  font-size: 28rpx;
  flex: 1;
  text-align: left;
  word-break: break-all;
}

.device-image-section {
  margin: 30rpx 0;
}

.image-placeholder {
  width: 240rpx;
  height: 180rpx;
  background-color: #f0f3ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16rpx;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.image-placeholder:active {
  transform: scale(0.98);
}

.image-icon {
  color: #b8c2ff;
  font-size: 60rpx;
}

.device-image {
  width: 240rpx;
  height: 180rpx;
  margin-top: 16rpx;
  border-radius: var(--border-radius);
  transition: var(--transition);
  object-fit: cover;
}

.device-image:active {
  transform: scale(0.98);
}

.divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #e9ecef, transparent);
  margin: 30rpx 0;
}

.remarks-section {
  margin-bottom: 36rpx;
}

.remarks-label {
  color: var(--text-color);
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: block;
}

.remarks-content {
  color: var(--text-secondary);
  font-size: 28rpx;
  line-height: 1.6;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: var(--border-radius);
}

.options-section {
  margin-top: 36rpx;
}

.options-label {
  color: var(--text-color);
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
  display: block;
}

.options-table {
  border: 2rpx solid #e9ecef;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.table-row {
  display: flex;
  border-bottom: 2rpx solid #e9ecef;
  transition: var(--transition);
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:active {
  background-color: #f8f9fa;
}

.table-cell {
  padding: 20rpx;
  font-size: 28rpx;
}

.number-cell {
  width: 80rpx;
  border-right: 2rpx solid #e9ecef;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-secondary);
  background-color: #f8f9fa;
}

.content-cell {
  flex: 1;
  color: var(--text-color);
}

.button-group {
  display: flex;
  gap: 20rpx;
  position: fixed;
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
  z-index: 999;
  padding: 20rpx;
  border-radius: var(--border-radius);
  margin: 0;
}

.btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--border-radius);
  font-size: 32rpx;
  font-weight: 500;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.delete-btn {
  background-color: #d13030;
  color: white;
  border-radius: 20rpx;
}

.edit-btn {
  background-color: #2196f3;
  color: white;
  border-radius: 20rpx;
}
</style>