<template>
  <view class="add-device-container">
    <!-- Form content -->
    <view class="form-content">
      <!-- Device name input -->
      <view class="form-item">
        <view class="form-label">
          <text>设备名称</text>
          <text class="required">*</text>
        </view>
        <view class="form-input">
          <input type="text" v-model="deviceName" placeholder="请输入设备名称" />
        </view>
      </view>
      
      <!-- Device image upload -->
      <view class="form-item">
        <view class="form-label">设备图片</view>
        <view class="image-upload" @click="openFile">
          <view class="upload-placeholder">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传图片</text>
          </view>
          <image v-if="deviceImage" :src="deviceImage" mode="aspectFit" class="preview-image"></image>
        </view>
      </view>
      
      <!-- Notes input -->
      <view class="form-item">
        <view class="form-label">备注</view>
        <view class="form-input">
          <input type="text" v-model="remark" placeholder="请输入备注" />
        </view>
      </view>
    </view>
    
    <!-- Bottom button -->
    <view class="bottom-button">
      <button type="primary" @click="nextStep">{{isEditMode ? '保存' : '下一步'}}</button>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api";
import urls  from "@/lib/urls";

export default {
  data() {
    return {
      uploadForm: {},
      deviceId: null,
      deviceName: '',
      deviceImage: '',
      remark: '',
      isEditMode: false
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.deviceId = options.id;
      this.isEditMode = true;
      uni.setNavigationBarTitle({
        title: '编辑设备'
      });
      this.getDeviceDetail();
    }
  },
  methods: {
    getDeviceDetail() {
      api.get(urls.deviceDetail(this.deviceId))
        .then(res => {
          this.deviceName = res.name;
          this.deviceImage = res.image_url;
          this.remark = res.remark;
        })
        .catch(err => {
          console.error('获取设备详情失败:', err);
          uni.showToast({
            title: err.message || '获取设备详情失败',
            icon: 'none'
          });
        });
    },
    
    goBack() {
      uni.navigateBack();
    },

    submitForm() {
      if (!this.deviceName) {
        uni.showToast({
          title: '请输入设备名称',
          icon: 'none'
        });
        return;
      }

      const data = {
        name: this.deviceName,
        image_url: this.deviceImage,
        remark: this.remark
      };

      const request = this.isEditMode 
        ? api.put(urls.editDevice(this.deviceId), data)
        : api.post(urls.deviceList, data);

      request.then((res) => {
        uni.showToast({
          title: this.isEditMode ? '设备更新成功' : '设备创建成功',
          icon: 'success'
        });

        uni.navigateTo({
          url: '/packageInspect/inspection/device/item-form?device_id=' + res.id
        });
      }).catch(err => {
        uni.hideLoading();
        uni.showToast({
          title: err.message || '保存失败',
          icon: 'none'
        });
      });
    },

    async openFile() {
        let itemList = ["从微信聊天选择", "从手机相册选择", "拍摄"]
        uni.showActionSheet({
            itemList: itemList,
            success: res => {
                if (res.tapIndex == 0) {
                    wx.chooseMessageFile({
                        count: 1, // 只允许选择1个文件
                        type: 'all',
                        success: async res => {
                            await this.uploadAllFiles(res.tempFiles);
                        }
                    });
                } else if (res.tapIndex == 1) {
                    uni.chooseMedia({
                        count: 1,
                        sourceType: ['album'],
                        mediaType: ['image'],
                        success: async res => {
                            await this.uploadAllFiles(res.tempFiles);
                        }
                    })
                } else if (res.tapIndex == 2) {
                    uni.chooseMedia({
                        count: 1,
                        sourceType: ['camera'],
                        mediaType: ['image'],
                        success: async res => {
                            await this.uploadAllFiles(res.tempFiles);
                        }
                    })
                }
            }
        })
    },
    initData(){
        return new Promise((resolve, reject) => {
            api.get(urls.getUploadConfig).then(res => {
                this.uploadForm = res
                resolve()
            }).catch(err => {
                alert(err.message)
                reject()
            })
        })
    },

    judgeSize(size) {
        const maxSizeMB = 10 // 最大5MB
        size = size / 1024 / 1024 // 转换为MB
        if (size >= maxSizeMB) {
            return `图片大小不能超过${maxSizeMB}M`
        }
        return false
    },

    async uploadAllFiles(res) {
        await this.initData();
        const file = res[0];
        const filePath = file?.path || file?.tempFilePath;
        if (filePath) {
            this.uploadFile(filePath);
        }
    },

    async uploadFile(path) {
        uni.showLoading({
            title: '上传中'
        });
        
        try {
            const uploadFileRes = await uni.uploadFile({
                url: this.uploadForm.url,
                filePath: path,
                formData: this.uploadForm.form_params,
                name: this.uploadForm.name
            });

            let data = JSON.parse(uploadFileRes.data);
            if (data?.url) {
                this.deviceImage = data.url;
                uni.showToast({
                    title: '上传成功',
                    icon: 'success'
                });
            } else {
                uni.showToast({
                    title: '上传失败',
                    icon: 'none'
                });
            }
        } catch (error) {
            uni.showToast({
                title: '上传失败',
                icon: 'none'
            });
        } finally {
            uni.hideLoading();
        }
    },

    nextStep() {
      this.submitForm();
    }
  }
}
</script>

<style>
.add-device-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  height: 44px;
  align-items: center;
  padding: 0 15px;
  background-color: #ffffff;
}

.left {
  width: 30px;
}

.center {
  flex: 1;
  text-align: center;
}

.right {
  display: flex;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 500;
}

.back-icon {
  font-size: 20px;
}

.more-icon, .plus-icon {
  font-size: 20px;
  margin-left: 15px;
}

.form-content {
  flex: 1;
  padding: 20px 15px;
}

.form-item {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.form-label {
  font-size: 15px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.required {
  color: #ff0000;
  margin-left: 4px;
}

.form-input {
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.form-input input {
  width: 100%;
  font-size: 15px;
  color: #333;
}

.image-upload {
  width: 100px;
  height: 100px;
  background-color: #f9f9f9;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-icon {
  font-size: 24px;
  color: #999;
}

.upload-text {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.preview-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.bottom-button {
  padding: 15px;
  background-color: #ffffff;
}

button[type="primary"] {
  background-color: #007AFF;
  color: #ffffff;
  border-radius: 4px;
  font-size: 16px;
  height: 45px;
  line-height: 45px;
}
</style>