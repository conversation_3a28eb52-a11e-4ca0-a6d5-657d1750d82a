<template>
  <view class="container">
    <!-- Inspection form -->
    <view class="inspection-form">
      <!-- Header -->
      <view class="form-header">
        <text class="header-title">巡检项目：</text>
      </view>

      <!-- Inspection items 动态渲染 -->
      <view v-for="(item, idx) in deviceItems" :key="idx" class="inspection-item">
        <text class="item-label">{{ item.name }}</text>
        <view class="item-options">
          <text
            class="btn-status btn-normal"
            :class="{ selected: item.status === 1 }"
          >正常</text>
          <text
            class="btn-status btn-abnormal"
            :class="{ selected: item.status === 0 }"
          >异常</text>
        </view>
      </view>

      <!-- 现场照片 动态渲染 -->
      <view class="header-title">现场照片</view>
      <view class="image-container">
        <image
          v-for="(img, idx) in imageList"
          :key="idx"
          :src="img"
          mode="aspectFit"
          class="on-site-image"
          @click="previewImage(idx)"
        ></image>
      </view>

      <view v-if="status != 1">
        <!-- 问题描述 -->
        <view class="section-title">问题描述</view>
        <view class="problem-description">{{ question }}</view>

        <!-- 处理建议 -->
        <view class="section-title">处理建议</view>
        <view class="solution">{{ suggestion }}</view>

        <!-- 流程 -->
        <view class="section-title">流程</view>
        <view v-for="(item, idx) in handleProcess" :key="idx" class="flow-item">
          <view class="avatar">
            <image v-if="item.avatar && item.avatar.startsWith('http')" :src="item.avatar" class="avatar-icon" mode="aspectFill" style="width: 100%; height: 100%; border-radius: 50%;" />
            <text v-else class="avatar-icon">{{ item.nickname ? item.nickname[0] : '无' }}</text>
          </view>
          <view class="flow-content">
            <view class="flow-header">
              <view class="flow-user">{{ item.nickname }}</view>
              <view class="flow-time">{{ item.time }}</view>
            </view>
            <view class="flow-action">
              <text class="action-label">{{ item.text }}</text>
            </view>
          </view>
        </view>
        
      </view>
      
    </view>
    <!-- 审批按钮和弹窗 -->
    <view v-if="isCanApprove" style="position: fixed; left: 0; right: 0; bottom: 40rpx; z-index: 10; display: flex; justify-content: center;">
      <button class="btn-submit" style="width: 60%;" @click="showApprovalDialog = true">审批</button>
    </view>
    <view v-if="showApprovalDialog" style="position: fixed; left: 0; top: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.3); z-index: 100; display: flex; align-items: center; justify-content: center;">
      <view style="background: #fff; border-radius: 10rpx; width: 80vw; max-width: 500rpx; padding: 40rpx 30rpx; box-sizing: border-box;">
        <view style="font-size: 32rpx; font-weight: bold; margin-bottom: 30rpx;">审批</view>
        <view style="display: flex; align-items: center; margin-bottom: 30rpx;">
          <label style="display: flex; align-items: center; margin-right: 40rpx;">
            <radio :checked="approvalStatus === 1" @click="approvalStatus = 1" color="#007aff" />
            <text style="margin-left: 10rpx;">已处理</text>
          </label>
          <label style="display: flex; align-items: center;">
            <radio :checked="approvalStatus === 0" @click="approvalStatus = 0" color="#007aff" />
            <text style="margin-left: 10rpx;">暂不处理</text>
          </label>
        </view>
        <view style="display: flex; justify-content: space-between; margin-top: 30rpx;">
          <button style="width: 45%; background: #f5f5f5; color: #333;" @click="showApprovalDialog = false">取消</button>
          <button style="width: 45%; background: #007aff; color: #fff;" @click="submitApproval">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api.js";
import urls  from "@/lib/urls";
import { useUserStore } from "@/store/user";
import {mapState} from "pinia";

export default {
  data() {
    return {
        deviceRecordId: 0,
        isInspected: false, // 是否已巡检
        deviceItems: [], // 巡检项
        imageList: [],  // 现场照片（多图）
        status: 0,  // 设备记录状态
        question: '',   // 问题描述
        suggestion: '',  // 处理建议
        handleProcess: [], // 审批流程
        isCanApprove: false, // 是否显示审批按钮
        showApprovalDialog: false, // 审批弹窗显示
        approvalStatus: 1, // 1:已处理, 0:暂不处理
    }
  },

  computed: {
      ...mapState(useUserStore, ['user']),
  },
  onLoad(options) {
      this.deviceRecordId = options.id;
      this.loadData();
  },
  methods: {
      loadData() {
          api.get(urls.approvalDetail(this.deviceRecordId)).then(res => {
              this.isInspected = res.isInspected || false;

              this.deviceItems = (res.deviceItems || []).map(item => {
                  const newItem = {...item};
                  if (newItem.status !== undefined && newItem.status !== null && newItem.status !== '') {
                      newItem.status = Number(newItem.status);
                  } else {
                      newItem.status = ''; // 未选择状态
                  }
                  return newItem;
              });

              // 兼容 imageUrl 可能为 null 或字符串或数组
              if (Array.isArray(res.imageUrl)) {
                  this.imageList = res.imageUrl;
              } else if (typeof res.imageUrl === 'string' && res.imageUrl) {
                  this.imageList = [res.imageUrl];
              } else {
                  this.imageList = [];
              }
              this.question = res.question || '';
              this.suggestion = res.suggestion || '';
              this.handleProcess = res.handleProcess || [];
              this.isCanApprove = !!res.isCanApprove;
              this.status = res.status;
          });
      },

      goLogin(){
        if (!this.user) {
          setTimeout(() => {
            uni.showToast({
              title: '请先登录, 跳转中...',
              icon: 'none'
            });
          }, 500);

          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/index'
            });
          }, 1500);
        }
      },
      
      previewImage(idx) {
        uni.previewImage({
          current: this.imageList[idx],
          urls: this.imageList
        });
      },
      submitApproval() {
        this.goLogin();

        uni.showLoading({ title: '提交中...' });
        api.put(urls.approval(this.deviceRecordId), {
          approve_status: this.approvalStatus
        }).then(() => {
          uni.hideLoading();
          this.showApprovalDialog = false;
          uni.showToast({ title: '审批操作已提交', icon: 'success' });
          // 刷新数据
          this.loadData();
        }).catch(err => {
          uni.hideLoading();
          uni.showToast({ title: (err && err.message) || '提交失败', icon: 'none' });
        });
      }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}


.inspection-form {
  flex: 1;
  padding: 20rpx 50rpx;
}

.form-header {
  margin: 20rpx 0;
}

.header-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}


.inspection-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
}

.item-label {
  width: 160rpx;
  font-size: 30rpx;
  color: #333;
}

.item-options {
  display: flex;
  margin-left: auto; /* 这会将选项推到右侧，无论屏幕宽度如何 */
  justify-content: flex-end; /* 确保按钮从右侧开始排列 */
}

.btn-status {
  margin-right: 20rpx;
  height: 60rpx;
  min-width: 120rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
  line-height: 60rpx;
  border-radius: 6rpx;
  border: none;
  background-color: #f0f0f0;
  color: #666;
}

.btn-normal.selected {
  background-color: #007aff;
  color: white;
}

.btn-abnormal.selected {
  background-color: #ff9500;
  color: white;
}

.btn-submit {
  background-color: #007aff;
  color: white;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 8rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  margin: 30rpx 0 15rpx 0;
  color: #333;
}

.problem-description, .solution {
  font-size: 26rpx;
  color: #666;
  margin-left: 20rpx;
}

.image-container {
  margin: 10rpx 0 10rpx 20rpx;
}

.on-site-image {
  width: 200rpx;
  height: 200rpx;
  border: 1px solid #eee;
}

.flow-item {
  display: flex;
  margin: 20rpx 0;
  align-items: flex-start;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.avatar-icon {
  font-size: 24rpx;
  color: #333;
}

.flow-content {
  flex: 1;
}

.flow-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5rpx;
}

.flow-user {
  font-size: 26rpx;
  color: #333;
}

.flow-action {
  display: flex;
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.action-label {
  color: #666;
  margin-right: 10rpx;
}

.action-status {
  color: #4caf50;
}

.flow-time {
  font-size: 24rpx;
  color: #999;
}
</style>