<template>
	<view class="container">
		<view class="tabs">
			<view class="tab" :class="{ active: activeTab === 'pending' }" @click="switchTab('pending')">
				待审批
			</view>
			<view class="tab" :class="{ active: activeTab === 'approved' }" @click="switchTab('approved')">
				已审批
			</view>
		</view>

		<scroll-view scroll-y class="content">
			<view v-if="activeTab === 'pending'">
				<view class="device-card" v-for="(item, index) in waitApproval" :key="index" @click="navigateTo('approve/detail?id='+item.id)" >
					<view class="device-title">{{ item.device_name }}</view>
					<view class="device-info">
						<text class="info-label">异常数:</text>
						<text class="info-value">{{ item.abnormal_count }}</text>
					</view>
					<view class="device-info">
						<text class="info-label">所属任务:</text>
						<text class="info-value">{{ item.task_name }}</text>
					</view>
					<view class="device-info">
						<text class="info-label">提交时间:</text>
						<text class="info-value">{{ item.submit_at }}</text>
					</view>
					<view class="device-info">
						<text class="info-label">确认时间:</text>
						<text class="info-value">{{ item.approver_at || '-' }}</text>
					</view>
				</view>
                <view v-if="!waitApproval.length" style="text-align: center;">
                    <text>暂无待审批记录</text>
                </view>
			</view>

			<view v-else>
				<view class="device-card" v-for="(item, index) in approved" :key="index" @click="navigateTo('approve/detail?id='+item.id)">
					<view class="device-title">{{ item.device_name }}</view>
					<view class="device-info">
						<text class="info-label">异常数:</text>
						<text class="info-value">{{ item.abnormal_count }}</text>
					</view>
					<view class="device-info">
						<text class="info-label">所属任务:</text>
						<text class="info-value">{{ item.task_name }}</text>
					</view>
					<view class="device-info">
						<text class="info-label">提交时间:</text>
						<text class="info-value">{{ item.submit_at }}</text>
					</view>
					<view class="device-info">
						<text class="info-label">确认时间:</text>
						<text class="info-value">{{ item.approver_at || '-' }}</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
    <view v-if="!waitApproval.length && !approved.length" class="empty-tip">
        <text>暂无审批事项</text>
    </view>
</template>

<script>
import api from "@/lib/api.js";
import urls  from "@/lib/urls";

export default {
	data() {
		return {
            task_id: 0,
			activeTab: 'pending', // 当前活跃的标签: 'pending' 或 'approved'
            waitApproval: [],
            approved: [],
		}
	},
	methods: {
		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab;
		},

        navigateTo(page) {
            uni.navigateTo({
                url: '/packageInspect/inspection/' + page
            });
        },

		// 加载审批列表
        async fetchApprovalList() {
            try {
                uni.showLoading({ title: '加载中...' });
                let url = this.task_id >0 ? urls.taskDevicesRecordApproval(this.task_id) : urls.approvalList;
                const res = await api.get(url);

                this.waitApproval = res.waitApproval || [];
                this.approved = res.approved || [];
                uni.hideLoading();
            } catch (e) {
                uni.hideLoading();
                uni.showToast({ title: '审批列表加载失败', icon: 'none' });
            }
        }
	},
	mounted() {
		// 页面加载时获取数据
	},
    onShow() {
        this.fetchApprovalList();
    },
    onLoad(option) {
        console.log('option', option)
        this.task_id = option.task_id
    }
}
</script>

<style>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.tabs {
	display: flex;
	background-color: #fff;
	border-bottom: 1px solid #eee;
}

.tab {
	flex: 1;
	text-align: center;
	padding: 12px 0;
	font-size: 16px;
	color: #666;
	position: relative;
}

.tab.active {
	color: #3c7afe;
	font-weight: 500;
}

.tab.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 20%;
	width: 60%;
	height: 2px;
	background-color: #3c7afe;
}

.content {
	flex: 1;
	padding: 15px;
}

.device-card {
	background-color: #fff;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 15px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.device-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 10px;
}

.device-info {
	display: flex;
	margin-bottom: 8px;
	font-size: 14px;
	line-height: 1.5;
}

.info-label {
	color: #999;
	width: 70px;
}

.info-value {
	color: #666;
	flex: 1;
}

/* 引入字体图标（这里仅为示例，实际使用时需要引入正确的图标库） */
@font-face {
	font-family: 'iconfont';
	src: url('data:application/x-font-woff2;charset=utf-8;base64,...') format('woff2');
}

.empty-tip {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 30rpx;
    letter-spacing: 2rpx;
    text-align: center;
    background: #fff; /* 可选，防止内容被遮挡 */
    z-index: 10;
}
</style>