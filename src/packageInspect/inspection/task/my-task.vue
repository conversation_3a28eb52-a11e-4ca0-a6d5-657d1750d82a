<template>
  <view class="container">
    <!-- My Tasks section -->
    <view class="tasks-section">
      <!-- Task cards -->
      <view class="task-card" v-for="(task, index) in tasks" :key="index">
        <view class="task-header">
          <text class="task-title">{{task.name}}</text>
          <text class="task-record" @click="navigateTo('task-device/record?task_id=' + task.id + '&task_name=' + task.name)">巡检记录</text>
        </view>
        <view class="task-info">
          <view class="info-item" @click="navigateTo('task-device/index?task_id=' + task.id + '&task_name=' + task.name)">
            <text class="info-value">{{task.device_count}}</text>
            <text class="info-label">设备数</text>
          </view>
          <view class="info-item" @click="navigateTo('approve/index?task_id=' + task.id)">
            <text class="info-value">{{task.pending_approval_count}}</text>
            <text class="info-label">审批项</text>
          </view>
        </view>
      </view>
      <view v-if="tasks.length === 0" class="empty-tasks">
        <image class="empty-img" src="@/images/empty.png" mode="widthFix"></image>
        <text class="empty-text">任务空空的 ~</text>
        <button class="create-task-btn" @click="navigateTo('task/index')">创建任务</button>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api.js";
import urls  from "@/lib/urls";
import {getAppName} from "@/lib/context";

export default {
    data() {
        return {
            appName: getAppName(),
            tasks: [],
        }
    },
    onShareAppMessage() {
        return {
            title: this.appName,
            path: "/packageInspect/inspection/task/my-task",
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
        };
    },
    onLoad() {
        this.loadTasks();
    },
    onPullDownRefresh() {
        this.loadTasks();
    },
    methods: {
        loadTasks() {
            api.get(urls.taskList).then(res => {
                console.log(res)
                this.tasks = res;
            });
        },
        navigateTo(page) {
            // Navigate to different pages based on menu selection
            uni.navigateTo({
                url: '/packageInspect/inspection/' + page
            });
        },
        viewTaskRecord(taskId) {
            // Navigate to task record page
            uni.navigateTo({
                url: '/packageInspect/taskRecord/taskRecord?id=' + taskId
            });
        }
    }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
}

.tasks-section {
  padding: 30rpx;
}

.task-card {
  background: linear-gradient(135deg, #ffffff 0%, #f9f9f9 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.task-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
  margin-bottom: 16rpx;
}

.task-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5rpx;
}

.task-record {
  font-size: 26rpx;
  color: #3388ff;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  background-color: rgba(51, 136, 255, 0.1);
}

.task-info {
  display: flex;
  padding-top: 8rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50%;
  padding: 8rpx 0;
}

.info-value {
  font-size: 38rpx;
  font-weight: 700;
  color: #3388ff;
  margin-bottom: 6rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.empty-tasks {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300rpx;
  padding: 0;
}

.empty-img {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.7;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 18rpx;
}

.create-task-btn {
  background-color: #3388ff;
  color: #ffffff;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
}
</style>