<template>
  <view class="container">
    <view class="content">
      <!-- Task Name Field -->
      <view class="form-item">
        <view class="label">任务名称</view>
        <text class="required">*</text>
        <input
          type="text"
          v-model="taskName"
          placeholder="请填写巡检任务名称"
          class="input-field"
        />
      </view>

      <!-- Inspection Frequency Field -->
      <view class="form-item">
        <view class="label">巡检频率</view>
        <view class="frequency-selector">
          <input
            type="number"
            v-model="frequency"
            class="frequency-input"
          />
          <view class="frequency-unit">
            <text>日 1 次</text>
          </view>
        </view>
      </view>

      <!-- Empty space - can be used for additional fields -->
      <view class="empty-space"></view>

      <!-- Next Step Button -->
      <button class="next-button" @tap="nextStep">下一步</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      taskName: '',
      frequency: 1,
    }
  },
  methods: {

    nextStep() {
      // Validate input
      if (!this.taskName.trim()) {
        uni.showToast({
          title: '请填写巡检任务名称',
          icon: 'none'
        });
        return;
      }

      if (!this.frequency || this.frequency <= 0) {
        uni.showToast({
          title: '请填写有效的巡检频率',
          icon: 'none'
        });
        return;
      }

      // Prepare data to pass to the next page
      const taskData = {
        name: this.taskName,
        frequency: this.frequency,
      };

      // Navigate to the next page with data
      uni.navigateTo({
          url: '/packageInspect/inspection/task-device/add-device',
          success(res) {
            res.eventChannel.emit('taskData', taskData);
          }
      });
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
  padding: 0 16px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.label {
  width: 80px;
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.input-field {
  flex: 1;
  height: 20px;
  font-size: 14px;
  color: #333333;
  padding: 0 5px;
}

.input-field::placeholder {
  color: #c8c8c8;
}

.frequency-selector {
  flex: 1;
  display: flex;
  align-items: center;
}

.frequency-input {
  width: 40px;
  height: 20px;
  font-size: 14px;
  text-align: center;
  color: #007AFF;
}

.frequency-unit {
  display: flex;
  margin-left: 10px;
}

.frequency-unit text {
  padding: 0 10px;
  font-size: 14px;
  color: #333333;
}

.active-unit {
  color: #007AFF;
}

.empty-space {
  flex: 1;
  min-height: 100px;
}

.next-button {
  height: 44px;
  background-color: #007AFF;
  color: white;
  border-radius: 4px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.required {
  color: #ff0000;
}
</style>