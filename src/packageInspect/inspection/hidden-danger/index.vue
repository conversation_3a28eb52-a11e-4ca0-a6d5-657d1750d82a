<template>
  <view class="container">
    <custom-navbar :tabs="mainTabs" :currentTab="currentMainTab" :showBack="true" @change="changeMainTab"></custom-navbar>

    <view class="content" :style="{ paddingTop: navHeight + 'px' }">
      <!-- 我的 Tab -->
      <view v-if="currentMainTab === 0">
        <view class="sub-tabs">
          <view
            class="sub-tab-item"
            v-for="(item, index) in mySubTabs"
            :key="index"
            :class="{ active: currentMySubTab === index }"
            @click="changeMySubTab(index)"
          >
            {{ item }}
            <text class="count">({{index === 0 ? pendingList.length : confirmedList.length}})</text>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading" v-if="loading">
          <text>加载中...</text>
        </view>

        <!-- 待确认 -->
        <view v-else-if="currentMySubTab === 0">
          <view v-if="pendingList.length === 0" class="empty-state">
            <text>暂无待确认的隐患</text>
          </view>
          <view v-else class="card-list">
            <view class="card" v-for="(item, index) in pendingList" :key="item.id" @click="navigateTo('hidden-danger/detail', item.id)">
              <view class="card-content">
                <view class="image-placeholder" v-if="item.images && item.images.length > 0">
                  <image :src="item.images[0]" mode="aspectFill" style="width: 100%; height: 100%;"></image>
                </view>
                <view class="image-placeholder" v-else>
                  <text class="placeholder-icon">🖼️</text>
                </view>
                <view class="card-info">
                  <view class="description">
                    <text class="label">隐患描述:</text>
                    <text>{{ item.question }}</text>
                  </view>
                  <view class="suggestion">
                    <text class="label">处理建议:</text>
                    <text>{{ item.suggestion }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 已确认 -->
        <view v-else>
          <view v-if="confirmedList.length === 0" class="empty-state">
            <text>暂无已确认的隐患</text>
          </view>
          <view v-else class="card-list">
            <view class="card" v-for="(item, index) in confirmedList" :key="item.id" @click="navigateTo('hidden-danger/detail', item.id)">
              <view class="card-content">
                <view class="image-placeholder" v-if="item.images && item.images.length > 0">
                  <image :src="item.images[0]" mode="aspectFill" style="width: 100%; height: 100%;"></image>
                </view>
                <view class="image-placeholder" v-else>
                  <text class="placeholder-icon">🖼️</text>
                </view>
                <view class="card-info">
                  <view class="description">
                    <text class="label">隐患描述:</text>
                    <text>{{ item.question }}</text>
                  </view>
                  <view class="suggestion">
                    <text class="label">处理建议:</text>
                    <text>{{ item.suggestion }}</text>
                  </view>
                </view>
                <view class="status" :class="{ 'status-yes': item.approver_status === 1, 'status-no': item.approver_status === 0 }">
                  {{ item.approver_text || (item.approver_status === 1 ? '存在隐患' : '不存在隐患') }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 大厅 Tab -->
      <view v-else>
        <view class="sub-tabs hall-tabs">
          <view
            class="sub-tab-item"
            v-for="(item, index) in hallSubTabs"
            :key="index"
            :class="{ active: currentHallSubTab === index }"
            @click="changeHallSubTab(index)"
          >
            {{ item }}
          </view>
        </view>

        <view class="hall-list">
          <!-- 加载状态 -->
          <view class="loading" v-if="hallLoading">
            <text>加载中...</text>
          </view>

          <!-- 空状态 -->
          <view v-else-if="hallList.length === 0" class="empty-state">
            <text>暂无数据</text>
          </view>

          <!-- 列表内容 -->
          <template v-else>
            <view class="hall-item" v-for="(item, index) in currentHallList" :key="item.id" @click="navigateTo('hidden-danger/detail', item.id)">
              <view class="rank-number" v-if="shouldShowRank(index)">
                <text :class="getRankClass(index)">{{ index + 1 }}</text>
              </view>
              <view class="hall-content">
                <view class="image-placeholder" v-if="item.images && item.images.length > 0">
                  <image :src="item.images[0]" mode="aspectFill" style="width: 100%; height: 100%;"></image>
                </view>
                <view class="image-placeholder" v-else>
                  <text class="placeholder-icon">🖼️</text>
                </view>
                <view class="card-info">
                  <view class="description">
                    <text class="label">隐患描述:</text>
                    <text>{{ item.question }}</text>
                  </view>
                  <view class="suggestion">
                    <text class="label">处理建议:</text>
                    <text>{{ item.suggestion }}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 加载更多状态 -->
            <view class="loading-more" v-if="hallLoadingMore">
              <text>加载更多...</text>
            </view>
          </template>
        </view>
      </view>
    </view>

    <view class="camera-btn" @click="navigateTo('hidden-danger/create')">
	  	<uni-icons type="camera" size="60" color="#000000"></uni-icons>
    </view>
  </view>
</template>

<script>
import CustomNavbar from '../components/navbar.vue';
import api from '@/lib/api.js';
import { useUserStore } from "@/store/user";
import {mapState} from "pinia";
import urls from '@/lib/urls.js';

export default {
  components: { CustomNavbar },
  data() {
    return {
      loading: false,
      navHeight: 64, // status bar + navbar height
      mainTabs: ['我的', '大厅'],
      currentMainTab: 0,

      // 我的 subtabs
      mySubTabs: ['待确认', '已确认'],
      currentMySubTab: 0,

      // 大厅 subtabs
      hallSubTabs: ['太震惊', '这没什么', '我发布的'],
      currentHallSubTab: 0, // 默认选中"太震惊"tab

      // 隐患数据
      pendingList: [],
      confirmedList: [],

      // 大厅数据
      hallList: [],
      hallLoading: false,
      hallLoadingMore: false,
      nextCursor: null,
      // 移除不需要的静态数据
      amazingList: [],
      normalList: [],
      myPublishedList: []
    }
  },
  computed: {
    ...mapState(useUserStore, ['user']),
    currentHallList() {
      return this.hallList;
    }
  },
  created() {
    const systemInfo = uni.getSystemInfoSync();
    this.navHeight = systemInfo.statusBarHeight + 44; // 44 is the height of navbar content
  },
  methods: {
    goLogin(){
      if (!this.user) {
        setTimeout(() => {
          uni.showToast({
            title: '请先登录, 跳转中...',
            icon: 'none'
          });
        }, 500);

        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/login/index'
          });
        }, 1500);
      }
    },

    async fetchHiddenDangerList() {
      this.loading = true;
      try {
        const response = await api.get(urls.hiddenDanger);
        console.log('隐患列表接口返回数据:', response); // 调试日志
        this.pendingList = response.waitConfirm || [];
        this.confirmedList = response.confirmed || [];
      } catch (error) {
        console.error('获取隐患列表失败:', error);
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    async fetchHallList(loadMore = false) {
      if (!loadMore) {
        this.hallLoading = true;
        this.hallList = [];
        this.nextCursor = null;
      } else {
        this.hallLoadingMore = true;
      }

      try {
        const type = this.currentHallSubTab === 0 ? 'top' : this.currentHallSubTab === 1 ? 'down' : 'mine';
        const params = { type };
        if (this.nextCursor) {
          params.cursor = this.nextCursor;
        }

        const response = await api.get(urls.hiddenDangerHall,  params);
        console.log('大厅列表接口返回数据:', response);

        if (loadMore) {
          this.hallList = [...this.hallList, ...response.data];
        } else {
          this.hallList = response.data;
        }
        this.nextCursor = response.next_cursor;
      } catch (error) {
        console.error('获取大厅列表失败:', error);
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      } finally {
        if (loadMore) {
          this.hallLoadingMore = false;
        } else {
          this.hallLoading = false;
        }
      }
    },
    navigateTo(page, id) {
      let url = '/packageInspect/inspection/' + page;
      
      // 如果有ID，添加ID参数
      if (id) {
        url += '?id=' + id;
        // 根据当前tab添加type参数
        if (this.currentMainTab === 0) {
          url += '&type=mine';
        } else if (this.currentMainTab === 1) {
          url += '&type=hall';
        }
      }
      
      uni.navigateTo({
        url: url
      });
    },
    changeMainTab(index) {
      this.currentMainTab = index;
      if (index === 1) {
        // 切换到大厅tab时加载数据
        this.fetchHallList();
      }
    },
    changeMySubTab(index) {
      this.currentMySubTab = index;
    },
    changeHallSubTab(index) {
      this.currentHallSubTab = index;
      this.fetchHallList();
    },
    shouldShowRank(index) {
      // 只在 top 和 down 类型的前10条数据显示序号
      return (this.currentHallSubTab === 0 || this.currentHallSubTab === 1) && index < 10;
    },
    getRankClass(index) {
      if (index === 0) {
        return 'rank-1';
      } else if (index === 1) {
        return 'rank-2';
      } else if (index === 2) {
        return 'rank-3';
      }
      return '';
    },
    onReachBottom() {
      if (this.currentMainTab === 1 && this.nextCursor && !this.hallLoadingMore) {
        this.fetchHallList(true);
      }
    }
  },
  onShow() {
    this.goLogin();
    
    // 检查是否从create页面返回
    const pages = getCurrentPages();
    console.log('当前页面栈:', pages);
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];
    console.log('当前页面栈11111111:', prevPage);

      if (prevPage.route === 'packageInspect/inspection/hidden-danger/create') {
        this.currentMainTab = 1; // 切换到大厅tab
      }
    }

    if (this.currentMainTab === 0) {
      this.fetchHiddenDangerList();
    } else {
      this.fetchHallList();
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
}

.content {
  flex: 1;
}

.sub-tabs {
  display: flex;
  background-color: #fff;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.sub-tab-item {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #666;
  position: relative;
}

.sub-tab-item.active {
  color: #007AFF;
}

.sub-tab-item.active:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #007AFF;
}

.hall-tabs .sub-tab-item {
  flex: auto;
  padding: 0 10px;
}

.card-list {
  padding: 10px;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card-content {
  padding: 15px;
  position: relative;
}

.image-placeholder {
  width: 100%;
  height: 150px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.placeholder-icon {
  font-size: 40px;
  color: #ccc;
}

.card-info {
  margin-top: 10px;
}

.description, .suggestion {
  margin-bottom: 5px;
  font-size: 14px;
}

.label {
  font-weight: bold;
  margin-right: 5px;
}

.status {
  position: absolute;
  right: 15px;
  bottom: 15px;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
}

.status-yes {
  color: #FF6B00;
}

.status-no {
  color: #4CD964;
}

.hall-list {
  padding: 10px;
}

.hall-item {
  background-color: #fff;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  display: flex;
}

.rank-number {
  width: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
}

.rank-number text {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.rank-1 {
  background-color: #FF0000;
  color: #fff;
  border-radius: 3px;
}

.rank-2 {
  background-color: #FF6B00;
  color: #fff;
  border-color: 3px;
}

.rank-3 {
  background-color: #FFCC00;
  color: #fff;
  border-radius: 3px;
}

.hall-content {
  flex: 1;
  padding: 15px 10px;
}

.camera-btn {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.camera-icon {
  font-size: 30px;
  color: #fff;
}

.loading {
  padding: 20px;
  text-align: center;
  color: #666;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
  color: #999;
}

.count {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}

.loading-more {
  padding: 10px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>