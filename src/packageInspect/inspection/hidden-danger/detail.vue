<template>
	<view class="container">
		<!-- Image swiper -->
		<swiper class="image-swiper" :indicator-dots="true" indicator-color="rgba(255, 255, 255, 0.6)" indicator-active-color="#000">
			<swiper-item v-for="(image, index) in hazardData.images" :key="index">
				<view class="image-placeholder">
					<image class="image" :src="image" mode="aspectFit" @click="previewImage(image)"></image>
				</view>
			</swiper-item>
		</swiper>

		<!-- Hazard details -->
		<view class="detail-section">
			<view class="detail-item">
				<text class="detail-label">隐患编号</text>
				<text class="detail-value">{{hazardData.number}}</text>
			</view>

			<view class="detail-item">
				<text class="detail-label">隐患描述</text>
				<text class="detail-value red">{{hazardData.question}}</text>
			</view>

			<view class="detail-item">
				<text class="detail-label">处理建议</text>
				<text class="detail-value green">{{hazardData.suggestion}}</text>
			</view>
		</view>

        <!-- 从大厅点进来，有这些元素 -->
        <view v-if="type === 'hall'">
            <!-- 提交日期 -->
            <view class="created-at" v-if="hazardData.created_at">
                {{ hazardData.created_at }}
            </view>

            <!-- 投票按钮区域 -->
            <view class="vote-section">
                <button
                    class="vote-btn amazed"
                    :class="{ active: votedType==='amazed' }"
                    @click="handleVote('amazed')"
                    :style="{ flex: amazedCount > 0 ? amazedCount : 1, minWidth: '80px' }"
                >
                    <text>太震惊了</text>
                    <text class="vote-count">{{amazedCount}}</text>
                </button>
                <button
                    class="vote-btn no-amazed"
                    :class="{ active: votedType==='no_amazed' }"
                    @click="handleVote('no_amazed')"
                    :style="{ flex: noAmazedCount > 0 ? noAmazedCount : 1, minWidth: '80px' }"
                >
                    <text>这没什么</text>
                    <text class="vote-count">{{noAmazedCount}}</text>
                </button>
            </view>

            <!-- Bottom icons -->
            <view class="bottom-icons" v-if="hazardData.is_owner">
                <view class="action-icon" @click="handleDelete" v-if="hazardData.is_owner">
                    <image src="/static/images/icon/trash-2-line.png" mode="aspectFit"></image>
                </view>
                <view class="action-icon" @click="handleLock" v-if="hazardData.is_owner">
                    <image :src="hazardData.is_public ? '/static/images/icon/lock-unlock-line.png' : '/static/images/icon/lock-line.png'" mode="aspectFit"></image>
                </view>
            </view>
        </view>

		<!-- 从我的点进来，有流程元素 -->
        <view v-if="type === 'mine'">
            <view class="process-section">
                <view class="process-item" v-for="(process, index) in hazardData.handle_process" :key="index">
                    <view class="submitter-info">
                        <image class="avatar" :src="process.avatar" mode="aspectFill"></image>
                        <view class="user-info">
                            <text class="username">{{process.nickname}}</text>
                            <text class="action">{{process.text}}</text>
                        </view>
                    </view>
                    <text class="timestamp">{{process.time}}</text>
                </view>
            </view>

            <!-- 流程确认 -->
            <view class="button-section" v-if="!hazardData.is_owner && hazardData.handle_process.length === 1">
                <button class="confirm-btn" @click="confirmHazard">确认</button>
            </view>
        </view>

	</view>

	<!-- 审批弹窗 -->
	<view v-if="showApprovalDialog" class="dialog-mask">
		<view class="dialog-box custom-dialog">
			<view class="dialog-close" @click="handleApprovalCancel">×</view>
			<view class="dialog-title custom-title">请确认是否存在隐患</view>
			<view class="dialog-content custom-content">
				<radio-group :value="approvalResult" @change="onApprovalRadioChange" class="custom-radio-group">
					<label class="custom-radio-label"><radio value="yes" :checked="approvalResult==='yes'" color="#1989fa" /> 存在隐患</label>
					<label class="custom-radio-label"><radio value="no" :checked="approvalResult==='no'" color="#d1d1d1" /> 不存在隐患</label>
				</radio-group>
			</view>
			<view class="dialog-actions custom-actions">
				<button class="custom-btn" @click="handleApprovalCancel">取消</button>
				<button class="custom-btn primary" @click="handleApprovalConfirm">确定</button>
			</view>
		</view>
	</view>

	<!-- 兴趣弹窗 -->
	<view v-if="showInterestDialog" class="dialog-mask">
		<view class="dialog-box custom-dialog">
			<view class="dialog-close" @click="handleInterestCancel">×</view>
			<view class="dialog-title custom-title">提示</view>
			<view class="dialog-content custom-content">
				想要在组织内推广？试试随手拍企业版
			</view>
			<view class="dialog-actions custom-actions">
				<button class="custom-btn" @click="handleInterestCancel">下次再说</button>
				<button class="custom-btn primary" @click="handleInterestConfirm">很感兴趣</button>
			</view>
			<view class="custom-checkbox-row">
				<checkbox-group @change="handleNoMoreTipChange">
					<label class="custom-checkbox-label"><checkbox :checked="noInterestTip" color="#1989fa" /> 不再提醒</label>
				</checkbox-group>
			</view>
		</view>
	</view>

</template>

<script>
import api from '@/lib/api.js';
import urls from '@/lib/urls.js'

export default {
	data() {
		return {
			id: '',
			type: '',
			hazardData: {
				number: '',
				question: '',
				suggestion: '',
				images: [],
				amazed: 0,
				no_amazed: 0,
				created_at: '',
				is_owner: false,
				is_public: false,
				type: '',
				handle_process: []
			},
			// 新增本地投票计数和类型
			amazedCount: 0,
			noAmazedCount: 0,
			votedType: '',
			showApprovalDialog: false,
			approvalResult: 'yes', // 'yes' or 'no'
			showInterestDialog: false,
			noInterestTip: uni.getStorageSync('noInterestTip') || false,
		};
	},
	watch: {
		// 监听接口数据变化，重置本地投票数
		'hazardData.amazed'(val) {
			this.amazedCount = val || 0;
		},
		'hazardData.no_amazed'(val) {
			this.noAmazedCount = val || 0;
		},
	},
	mounted() {
		// 移除这里的初始化，因为此时数据还没有加载
	},
	onLoad(options) {
		// Get hazard ID and type from navigation parameters
		if (options.id && options.type) {
			this.id = options.id;
			this.type = options.type;
			this.loadHazardDetails();
		}
	},
    onShareAppMessage() {
        // 当是从待确认进入时，可以再次拉起分享
        if (this.id && this.type === 'mine' && this.hazardData.handle_process.length === 1) {
            return {
                title: '隐患详情',
                path: `/packageInspect/inspection/hidden-danger/detail?id=${this.id}&type=mine`,
                imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
            }
        }
        return {
            title: '隐患详情',
            path: `/packageInspect/inspection/hidden-danger/detail?id=${this.id}&type=${this.type}`,
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
        }
    },
	methods: {
		loadHazardDetails() {
			uni.showLoading({
				title: '加载中...'
			});
			api.get(urls.hiddenDangerDetail(this.id), {type: this.type}).then((res) => {
                uni.hideLoading();
                this.hazardData = res;
                // 数据加载完成后初始化投票计数
                this.amazedCount = res.amazed || 0;
                this.noAmazedCount = res.no_amazed || 0;
                console.log("接口数据", res)
            }).catch((err) => {
                uni.hideLoading();
                console.error("加载数据失败", err);
                uni.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
            });
		},
		confirmHazard() {
			this.showApprovalDialog = true;
		},
		handleApprovalConfirm() {
			uni.showLoading({ title: '处理中...' });
            let status = this.approvalResult === 'yes' ? 1 : 0;
			api.put(urls.hiddenDangerApproval(this.id), { status: status })
				.then(() => {
					uni.hideLoading();
					this.showApprovalDialog = false;
					if (!this.noInterestTip) {
						this.showInterestDialog = true;
					} else {
						uni.showToast({ title: '审批成功', icon: 'success' });
						setTimeout(() => uni.navigateBack(), 1500);
					}
				})
				.catch((res) => {
					uni.hideLoading();
					uni.showToast({ title: res.message || '操作失败', icon: 'none' });
				});
		},
		handleApprovalCancel() {
			this.showApprovalDialog = false;
		},
        // 很感兴趣
		handleInterestConfirm() {
			this.showInterestDialog = false;
			uni.navigateTo({ url: '/packageInspect/inspection/hidden-danger/clue?id=' + this.id });
		},
        // 下次再说
		handleInterestCancel() {
			this.showInterestDialog = false;
			setTimeout(() => uni.navigateBack(), 1500);
		},
        // 不再提醒
		handleNoMoreTipChange(e) {
			this.noInterestTip = e.detail.value.length > 0;
			uni.setStorageSync('noInterestTip', this.noInterestTip);
		},
		handleVote(type) {
            console.log("投票", type)
            console.log("投票类型", this.votedType)
            console.log("当前投票计数", { amazed: this.amazedCount, noAmazed: this.noAmazedCount })

            // 已投票不可再投
			if  (this.votedType !== '') {
                uni.showToast({
                    title: '已投票',
                    icon: 'none'
                });
                return;
            }

            // 显示加载提示
            uni.showLoading({
                title: '投票中...'
            });

            console.log("发送投票请求", { id: this.id, type: type })
			// 调用接口
			api.put(urls.handleHiddenDanger(this.id), { type: type })
                .then((res) => {
                    console.log("投票成功", res)
                    uni.hideLoading();
                    // 立即前端累加
                    if(type==='amazed') {
                        this.amazedCount++;
                        this.votedType = 'amazed';
                    } else {
                        this.noAmazedCount++;
                        this.votedType = 'no_amazed';
                    }

                    console.log("更新后投票计数", { amazed: this.amazedCount, noAmazed: this.noAmazedCount, votedType: this.votedType })

                    // 显示投票成功提示
                    uni.showToast({
                        title: '投票成功',
                        icon: 'success'
                    });
                })
                .catch((res) => {
                    uni.hideLoading();
                    console.log("投票失败", res)
                    this.votedType = '';
                    uni.showToast({
                        title: res.message || '投票失败',
                        icon: 'none'
                    });
                });
		},

		handleDelete() {
			uni.showModal({
				title: '提示',
				content: '确定要删除这条隐患记录吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '删除中...'
						});

						api.put(urls.handleHiddenDanger(this.id), {
							type: 'delete'
						}).then(() => {
							uni.hideLoading();
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}).catch(() => {
							uni.hideLoading();
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							});
						});
					}
				}
			});
		},

		handleLock() {
			const action = this.hazardData.is_public ? '是否设为仅自己可见' : '是否公开为所有人可见';
			uni.showModal({
				title: '提示',
				content: `${action}`,
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...'
						});

						api.put(urls.handleHiddenDanger(this.id), {
							type: 'lock'
						}).then(() => {
							uni.hideLoading();
							this.loadHazardDetails(); // 重新加载数据以更新锁定状态
							uni.showToast({
								title: '操作成功',
								icon: 'success'
							});
						}).catch(() => {
							uni.hideLoading();
							uni.showToast({
								title: '操作失败',
								icon: 'none'
							});
						});
					}
				}
			});
		},
		onApprovalRadioChange(e) {
			this.approvalResult = e.detail.value;
		},
		previewImage(current) {
			uni.previewImage({
				current,
				urls: this.hazardData.images
			});
		},
	}
};
</script>

<style>
.container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background-color: #ffffff;
}

.header {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 10px 15px;
	background-color: #f8f8f8;
}

.back-btn {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.title {
	flex: 1;
	text-align: center;
	font-size: 18px;
	font-weight: bold;
	color: #333;
}

.action-buttons {
	display: flex;
	align-items: center;
}

.ml-10 {
	margin-left: 10px;
}

.image-swiper {
	width: 100%;
	height: 300px;
	background-color: #f5f5f5;
}

.image-placeholder {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #f0f0f0;
}

.image {
	width: 100%;
	height: 100%;
}

.detail-section {
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15px;
}

.detail-label {
	font-size: 16px;
	color: #333;
	font-weight: 500;
}

.detail-value {
	font-size: 16px;
	text-align: right;
	flex: 1;
	margin-left: 15px;
}

.red {
	color: #e74c3c;
}

.green {
	color: #2ecc71;
}

.process-section {
	padding: 15px;
}

.process-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 0;
	border-bottom: 1px solid #f0f0f0;
}

.process-item:last-child {
	border-bottom: none;
}

.submitter-info {
	display: flex;
	align-items: center;
}

.avatar {
	width: 40px;
	height: 40px;
	border-radius: 20px;
	margin-right: 10px;
}

.user-info {
	display: flex;
	flex-direction: column;
}

.username {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.action {
	font-size: 14px;
	color: #666;
	margin-top: 4px;
}

.timestamp {
	font-size: 14px;
	color: #999;
}

.button-section {
	padding: 15px;
	margin-bottom: 30px;
}

.confirm-btn {
	width: 100%;
	height: 45px;
	background-color: #007aff;
	color: #ffffff;
	font-size: 16px;
	border-radius: 5px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.created-at {
	padding: 0 15px 6px 15px;
	color: #666;
	font-size: 14px;
	margin-top: 0;
	margin-bottom: 0;
}

.vote-section {
	display: flex;
	width: 100%;
	margin-bottom: 10px;
	padding-bottom: 60px; /* 为底部图标留出空间 */
}

.vote-btn {
	flex: 1;
	height: 44px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: none;
	font-size: 16px;
	padding: 0;
	color: #fff;
	border-radius: 0;
}

.vote-btn.amazed {
	background-color: #D13030;
}

.vote-btn.no-amazed {
	background-color: #1989FA;
}

.vote-btn.active {
	opacity: 0.7;
	position: relative;
}

.vote-btn.active::after {
	content: '✓';
	position: absolute;
	right: 10px;
	font-size: 18px;
	font-weight: bold;
}

.vote-btn .vote-count {
	margin-left: 5px;
}

.bottom-icons {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 50px;
	background-color: #fff;
	border-top: 1px solid #eee;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding-right: 15px;
	padding-bottom: env(safe-area-inset-bottom);
	gap: 20px;
}

.action-icon {
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-icon image {
	width: 100%;
	height: 100%;
}

.dialog-mask {
	position: fixed; left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.3); z-index: 9999;
	display: flex; align-items: center; justify-content: center;
}
.dialog-box.custom-dialog {
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 8px 32px rgba(0,0,0,0.18);
	padding: 0 32px 24px 32px;
	width: 80vw;
	max-width: 320px;
	min-width: 0;
	position: relative;
	animation: dialog-pop .2s;
}
@keyframes dialog-pop {
	0% { transform: scale(0.9); opacity: 0; }
	100% { transform: scale(1); opacity: 1; }
}
.dialog-close {
	position: absolute;
	right: 16px;
	top: 16px;
	font-size: 22px;
	color: #333;
	cursor: pointer;
	z-index: 2;
	font-weight: bold;
	user-select: none;
}
.dialog-title.custom-title {
	font-size: 20px;
	font-weight: bold;
	text-align: center;
	margin-top: 24px;
	margin-bottom: 18px;
}
.dialog-content.custom-content {
	text-align: center;
	font-size: 16px;
	margin-bottom: 18px;
}
.custom-radio-group {
	display: flex;
	flex-direction: row;
	justify-content: center;
	gap: 32px;
	margin-bottom: 10px;
}
.custom-radio-label {
	display: flex;
	align-items: center;
	font-size: 16px;
	margin-right: 16px;
}
.dialog-actions.custom-actions {
	display: flex;
	flex-direction: row;
	justify-content: center;
	gap: 24px;
	margin-top: 10px;
}
.custom-btn {
	min-width: 96px;
	height: 38px;
	border-radius: 6px;
	border: 1px solid #d1d1d1;
	background: #fff;
	color: #333;
	font-size: 16px;
	font-weight: 500;
	margin: 0;
	display: flex;
	align-items: center;
	justify-content: center;
}
.custom-btn.primary {
	background: #1989fa;
	color: #fff;
	border: none;
}
.custom-checkbox-row {
	display: flex;
	align-items: center;
	margin-top: 10px;
	margin-left: 4px;
}
.custom-checkbox-label {
	display: flex;
	align-items: center;
	font-size: 15px;
	color: #666;
}
</style>