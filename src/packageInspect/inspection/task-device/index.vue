<template>
  <view class="container">
    <!-- Tab navigation -->
    <view class="tab-container">
      <view class="tab-nav">
        <view class="tab-item" :class="{ active: activeTab === '待巡检' }" @click="switchTab('待巡检')">
          <text>待巡检</text>
          <view class="active-indicator" v-if="activeTab === '待巡检'"></view>
        </view>
        <view class="tab-item" :class="{ active: activeTab === '已巡检' }" @click="switchTab('已巡检')">
          <text>已巡检</text>
          <view class="active-indicator" v-if="activeTab === '已巡检'"></view>
        </view>
      </view>
      <view class="tab-action" @click="addNewEquipment">
        <text class="add-icon">+</text>
      </view>
    </view>

    <!-- Equipment list for pending inspection -->
    <view class="equipment-list" v-if="activeTab === '待巡检'">
      <view class="equipment-item"
            v-for="item in pendingList"
            :key="item.id"
            @click="goToDetail(item.id, item.device_name)"
            hover-class="equipment-item-hover">
        <view class="equipment-image">
          <image v-if="item.device_img" :src="item.device_img" mode="aspectFit"></image>
          <image v-else src="@/images/empty.png" mode="aspectFit" style="max-width:100%;max-height:100%"></image>
        </view>
        <view class="equipment-info">
          <view class="equipment-title">{{item.device_name}}</view>
          <view class="equipment-detail">巡检项：{{item.device_item_count}}</view>
          <view class="equipment-remark">备注：{{item.device_remark}}</view>
        </view>
      </view>
      <view v-if="!pendingList.length" style="text-align: center;">
          <text>今日巡检任务已完成</text>
        </view>
    </view>

    <!-- Equipment list for completed inspection -->
    <view class="equipment-list" v-if="activeTab === '已巡检'">
      <view class="equipment-item"
            v-for="item in completedList"
            :key="item.id"
            @click="goToDetail(item.id, item.device_name)"
            hover-class="equipment-item-hover">
        <view class="equipment-image">
          <image v-if="item.device_img" :src="item.device_img" mode="aspectFit"></image>
          <image v-else src="@/images/empty.png" mode="aspectFit" style="max-width:100%;max-height:100%"></image>
        </view>
        <view class="equipment-info">
          <view class="equipment-title">{{item.device_name}}</view>
          <view class="equipment-detail">巡检项：{{item.device_item_count}}</view>
          <view class="equipment-remark">备注：{{item.device_remark}}</view>
        </view>
      </view>
    </view>
  </view>

  <view v-if="!completedList.length && !pendingList.length" class="empty-tip">
    <text>{{ message || '未到巡检日期' }}</text>
  </view>
</template>

<script>
import api from '@/lib/api.js';
import urls from '@/lib/urls.js';

export default {
  data() {
    return {
      activeTab: '待巡检',
      pendingList: [],
      completedList: [],
      task_id: 0,
      task_name: '',
      frequency: null,
      message: '',
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab;
    },
    addNewEquipment() {
      uni.navigateTo({
        url: '/packageInspect/inspection/task-device/add-device?from=task-device&task_id=' + this.task_id
      });
    },
    goToDetail(id, name) {
      // Navigate to equipment detail
      uni.navigateTo({
        url: `/packageInspect/inspection/task-device/inspect?id=${id}&deviceName=${name}`
      });
    },
    async fetchDeviceList() {
      try {
        uni.showLoading({ title: '加载中...' });
        const res = await api.get(urls.taskDevicesRecordTable(this.task_id));
        // 假设接口返回的数据结构为 { pending: [...], completed: [...] }
        this.pendingList = res.waitInspect || [];
        this.completedList = res.inspected || [];
        this.message = res.message || '';
        uni.hideLoading();
      } catch (e) {
        uni.hideLoading();
        uni.showToast({ title: '设备列表加载失败', icon: 'none' });
      }
    }
  },
  onLoad(option) {
    console.log('option', option)
    this.task_id = option.task_id;
    this.task_name = option.task_name;
    uni.setNavigationBarTitle({
      title: option.task_name
    })
  },
  onShow() {
    this.fetchDeviceList();
  }
}
</script>

<style>
.container {
  background-color: #fff;
  min-height: 100vh;
}

/* Header styles */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 20rpx;
  height: 90rpx;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  width: 60rpx;
}

.back-arrow {
  font-size: 40rpx;
  color: #333;
}

.header-center {
  flex: 1;
  text-align: center;
}

.header-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  width: 120rpx;
  justify-content: flex-end;
}

.more-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  color: #333;
}

.circle-icon {
  font-size: 42rpx;
  color: #333;
}

/* Tab navigation styles */
.tab-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  height: 90rpx;
  border-bottom: 1px solid #f0f0f0;
}

.tab-nav {
  display: flex;
  height: 100%;
  width: 80%; /* 增加宽度，让标签有更多空间 */
  justify-content: space-between; /* 使标签分散对齐 */
}

.tab-item {
  position: relative;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  color: #666;
  height: 100%;
}

.tab-item:first-child {
  margin-right: auto; /* 第一个标签靠左 */
}

.tab-item:last-child {
  margin-left: auto; /* 最后一个标签靠右 */
}

.tab-item.active {
  color: #2979ff;
  font-weight: 500;
}

.active-indicator {
  position: absolute;
  bottom: 0;
  left: 20rpx;
  right: 20rpx;
  height: 4rpx;
  background-color: #2979ff;
}

.tab-action {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #2979ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}

/* Equipment list styles */
.equipment-list {
  padding: 20rpx;
}

.equipment-item {
  display: flex;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.equipment-image {
  width: 120rpx;
  height: 120rpx;
  background-color: #f5f5f5;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
}

.equipment-image image {
  max-width: 100%;
  max-height: 100%;
}

.equipment-info {
  flex: 1;
}

.equipment-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.equipment-detail, .equipment-remark {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 6rpx;
}

/* 设备项悬停效果 */
.equipment-item-hover {
  background-color: #f9f9f9;
  transform: scale(0.99);
  transition: all 0.2s;
}

.empty-tip {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 30rpx;
  letter-spacing: 2rpx;
  text-align: center;
  background: #fff; /* 可选，防止内容被遮挡 */
  z-index: 10;
}
</style>