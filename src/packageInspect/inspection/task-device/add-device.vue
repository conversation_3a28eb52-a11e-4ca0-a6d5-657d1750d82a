<template>
  <view class="container">
    <!-- Equipment selection section -->
    <view class="selection-section">
      <view class="section-header">
        <text class="header-text">请添加巡检设备</text>
      </view>

      <!-- Equipment selector dropdown -->
      <view class="dropdown-container">
        <view class="dropdown-header" @click="toggleDropdown">
          <text class="dropdown-text">{{ selectedEquipment ? selectedEquipment.name : '请选择设备' }}</text>
          <view class="dropdown-actions">
            <text class="dropdown-arrow" :class="{ 'arrow-up': showDropdown }">^</text>
          </view>
        </view>

        <!-- Dropdown list -->
        <view class="dropdown-list" :class="{show: showDropdown}" v-show="showDropdown">
          <!-- Search input -->
          <view class="search-container">
            <input
              class="search-input"
              v-model="searchText"
              @input="filterEquipment"
              placeholder="搜索设备"
              focus
            />
            <text class="search-icon">🔍</text>
          </view>

          <!-- Equipment list -->
          <view
            v-for="(item, index) in filteredList"
            :key="item.id"
            class="dropdown-item"
            :class="{ 'selected': selectedEquipments.some(e => e.id === item.id) }"
            @click="selectEquipment(item)"
          >
            <text class="item-text">{{ item.name }}</text>
            <text class="check-icon" v-if="selectedEquipments.some(e => e.id === item.id)">✓</text>
          </view>
        </view>
      </view>
    </view>
    <!-- Selected equipment list -->
    <view class="selected-list">
      <view
        v-for="(item, index) in selectedEquipments"
        :key="item.id"
        class="selected-item"
      >
        <text class="item-text">{{ item.name }}</text>
      </view>
    </view>

    <!-- Save button -->
    <view class="button-container">
      <button class="btn-save" @click="saveTask">保存</button>
    </view>
  </view>
</template>

<script>
import api from '@/lib/api.js';
import urls from '@/lib/urls.js';

export default {
  props: {
    // 移除默认写死的设备数据
    // equipmentData: {
    //   type: Array,
    //   default: () => ['设备1', '设备2', '设备3', '设备4', '设备5', '设备6', '设备7', '设备8'],
    //   validator: value => Array.isArray(value) && value.every(item => typeof item === 'string')
    // },
    // initialSelected: {
    //   type: Array,
    //   default: () => ['设备1', '设备2'],
    //   validator: value => Array.isArray(value) && value.every(item => typeof item === 'string')
    // }
  },
  data() {
    return {
      showDropdown: false,
      equipmentList: [], // 设备对象数组
      filteredList: [],
      selectedEquipment: null, // 设备对象
      selectedEquipments: [], // 设备对象数组
      searchText: '',
      debounceTimer: null,
      taskData: {}, // 用于存储eventChannel传入的数据
      from: '', // 标识来自哪个页面
      task_id: 0,
    }
  },
  watch: {
    // equipmentData(newVal) {
    //   this.equipmentList = [...newVal];
    //   this.filteredList = [...newVal];
    // },
    // initialSelected(newVal) {
    //   this.selectedEquipments = [...newVal];
    //   this.selectedEquipment = newVal[0] || '';
    // }
  },
  onLoad: function(option) {
    this.from = option.from;
    this.task_id = option.task_id;
    const eventChannel = this.getOpenerEventChannel();
    // 监听taskData事件，获取上一页面通过eventChannel传送到当前页面的数据
    eventChannel.on('taskData', (data) => {
      console.log(data)
      this.taskData = data;
    })
    // 页面加载时获取设备列表
    this.getDeviceList();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    selectEquipment(item) {
      // 用id判断是否已选
      const index = this.selectedEquipments.findIndex(e => e.id === item.id);
      if (index === -1) {
        this.selectedEquipments.push(item);
      } else {
        this.selectedEquipments.splice(index, 1);
      }
      this.selectedEquipment = this.selectedEquipments.length > 0
        ? this.selectedEquipments[0]
        : null;
      this.showDropdown = false;
    },
    async saveTask() {
      if (this.selectedEquipments.length === 0) {
        uni.showToast({
          title: '请至少选择一个设备',
          icon: 'none'
        });
        return;
      }
      try {
        uni.showLoading({
          title: '保存中...',
          mask: true
        });
        // 从任务设备列表进入，点击保存是进行新增关联设备
        if (this.from == 'task-device') {
            // 组装参数，合并taskData和设备id数组
            const params = {
                devices: this.selectedEquipments.map(e => e.id)
            };
            await api.post(urls.assocDevice(this.task_id), params);
            uni.hideLoading();
            uni.showModal({
                title: '保存成功',
                content: '设备已成功添加到巡检任务',
                showCancel: false,
                success: () => {
                    // 返回创建入口页
                    uni.navigateBack();
                }
            });
        } else {
            // 组装参数，合并taskData和设备id数组
            const params = {
                ...this.taskData,
                devices: this.selectedEquipments.map(e => e.id)
            };
            await api.post(urls.createTask, params);
            uni.hideLoading();
            uni.showModal({
                title: '保存成功',
                content: '设备已成功添加到巡检任务',
                showCancel: false,
                success: () => {
                    // 返回创建入口页
                    uni.navigateBack({
                        delta: 2
                    });
                }
            });
        }

      } catch (error) {
        uni.hideLoading();
        uni.showModal({
          title: '保存失败',
          content: '设备保存失败，请重试',
          showCancel: false
        });
        console.error('保存设备失败:', error);
      }
    },

    filterEquipment() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.debounceTimer = setTimeout(async () => {
        const keyword = this.searchText.trim();
        if (!keyword) {
          // 关键词为空时重新获取全部设备
          await this.getDeviceList();
          return;
        }
        try {
          uni.showLoading({ title: '搜索中...' });
          // 假设接口支持 ?keyword=xxx 查询
          const params = {
            ...(this.from == 'task-device' 
              ? { operate: 'add_device', task_id: this.task_id } 
              : { operate: 'create_task' }
            ),
            keyword: keyword
          };

          const res = await api.get(urls.deviceList, params);
          let list = Array.isArray(res) ? res : (res.data || []);
          list = list.filter(item => item && typeof item.id !== 'undefined' && typeof item.name === 'string');
          this.equipmentList = [...list];
          this.filteredList = [...list];
          uni.hideLoading();
          if (list.length === 0) {
            uni.showModal({
              title: '提示',
              content: '未搜索到相关设备',
              showCancel: false
            });
          }
        } catch (e) {
          uni.hideLoading();
          uni.showToast({ title: '搜索失败', icon: 'none' });
        }
        // 保持已选设备在搜索状态下的高亮显示
        this.$nextTick(() => {
          this.selectedEquipments.forEach(sel => {
            if (!this.filteredList.some(f => f.id === sel.id)) {
              this.filteredList.push(sel);
            }
          });
        });
      }, 300);
    },
    async getDeviceList() {
      const params = this.from == 'task-device' ? {operate: 'add_device', task_id: this.task_id} : {operate: 'create_task'};
      try {
        uni.showLoading({ title: '加载设备列表...' });
        const res = await api.get(urls.deviceList, params);
        let list = Array.isArray(res) ? res : (res.data || []);
        list = list.filter(item => item && typeof item.id !== 'undefined' && typeof item.name === 'string');
        this.equipmentList = [...list];
        this.filteredList = [...list];
        this.selectedEquipments = [];
        this.selectedEquipment = null;
        uni.hideLoading();
      } catch (e) {
        uni.hideLoading();
        uni.showToast({ title: '设备列表获取失败', icon: 'none' });
        this.equipmentList = [];
        this.filteredList = [];
        this.selectedEquipments = [];
        this.selectedEquipment = null;
      }
    },
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  padding: 20rpx;
}

.selection-section {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.header-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.dropdown-container {
  position: relative;
  z-index: 10;
}

.dropdown-header {
  height: 88rpx;
  background-color: #fff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  border: 1rpx solid #e1e5eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.dropdown-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 400;
}

.dropdown-actions {
  display: flex;
  align-items: center;
}

.add-icon {
  font-size: 24rpx;
  color: #fff;
  margin-right: 20rpx;
  background-color: #007aff;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.dropdown-arrow {
  font-size: 28rpx;
  color: #999;
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.arrow-up {
  transform: rotate(0deg);
}

.dropdown-list {
  position: absolute;
  top: 120rpx;
  left: 10rpx;
  right: 10rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  z-index: 11;
  max-height: 700rpx;
  overflow-y: auto;
  overflow-x: hidden;
  transform-origin: top center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scaleY(0.95);
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

.dropdown-list.show {
  transform: scaleY(1);
  opacity: 1;
  pointer-events: auto;
  visibility: visible;
}

.dropdown-list::-webkit-scrollbar {
  width: 6rpx;
  background-color: transparent;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3rpx;
}

.dropdown-item {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  border-bottom: 1rpx solid #f0f2f5;
  transition: background-color 0.2s;
  overflow: hidden;
}

.item-text {
  font-size: 30rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80%;
}

.dropdown-item:active {
  background-color: #f5f7fa;
}

.dropdown-item.selected {
  background-color: #f0f7ff;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.item-text {
  font-size: 30rpx;
  color: #333;
}

.check-icon {
  color: #007aff;
  font-size: 36rpx;
  font-weight: bold;
}

.selected-list {
  padding: 0 20rpx;
  flex: 1;
}

.selected-item {
  height: 88rpx;
  background-color: #fff;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #e1e5eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  background-color: #f0f7ff;
  border-left: 6rpx solid #007aff;
}

.selected-item .item-text {
  font-weight: 500;
  color: #007aff;
}

.button-container {
  padding: 30rpx;
}

.btn-save {
  height: 88rpx;
  line-height: 88rpx;
  background-color: #007aff;
  color: #fff;
  font-size: 34rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.search-container {
  padding: 20rpx;
  position: relative;
  border-bottom: 1rpx solid #f0f2f5;
}

.search-input {
  width: calc(100% - 40rpx);
  height: 70rpx;
  padding: 0 50rpx 0 20rpx;
  margin: 0 20rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.search-icon {
  position: absolute;
  right: 40rpx;
  top: 30rpx;
  font-size: 30rpx;
  color: #999;
}
</style>