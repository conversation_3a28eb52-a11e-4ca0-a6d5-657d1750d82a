{
    "easycom": {
        "autoscan": true,
        "custom": {
            // uni-ui 规则如下配置
            "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
        }
    },
    "mp-weixin": {
        "embeddedAppIdList": [
            "wx8abaf00ee8c3202e"
        ]
        //需要半屏跳转的小程序appid
    },
    "pages": [
        //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/index/index",
            "style": {
                "navigationBarTitleText": "安管合规",
                "navigationBarBackgroundColor": "#F3F3FF",
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/index/webview",
            "style": {
                "navigationBarTitleText": "",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/login/index",
            "style": {
                "navigationBarTitleText": "",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/login/bind",
            "style": {
                "navigationBarTitleText": "",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/login/phone",
            "style": {
                "navigationBarTitleText": "",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/login/authorize",
            "style": {
                "navigationBarTitleText": "授权登录",
                "navigationBarBackgroundColor": "#FFFFFF"
            }
        },
        {
            "path": "pages/document/index",
            "style": {
                "navigationBarTitleText": "资料",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/document/list",
            "style": {
                "navigationBarTitleText": "",
                "navigationBarBackgroundColor": "#FFFFFF",
                "onReachBottomDistance": 120
            }
        },
        {
            "path": "pages/document/detail",
            "style": {
                "navigationBarTitleText": "资料详情",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/document/category",
            "style": {
                "navigationBarTitleText": "资料分类",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/document/topic-list",
            "style": {
                "navigationBarTitleText": "专题列表",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/document/topic-detail",
            "style": {
                "navigationBarTitleText": "专题详情",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/index",
            "style": {
                "navigationBarTitleText": "培训科目",
                "onReachBottomDistance": 120,
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/index",
            "style": {
                "navigationBarTitleText": "选择习题",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/category",
            "style": {
                "navigationBarTitleText": "选择习题",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/collect",
            "style": {
                "navigationBarTitleText": "题目收藏",
                "navigationBarBackgroundColor": "#fff"
            }
        },
        {
            "path": "pages/training/exam/practice",
            "style": {
                "navigationBarTitleText": "顺序练习",
                "navigationBarBackgroundColor": "#fff"
            }
        },
        {
            "path": "pages/training/exam/search",
            "style": {
                "navigationBarTitleText": "题目搜索",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/special",
            "style": {
                "navigationBarTitleText": "专项练习",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/test",
            "style": {
                "navigationBarTitleText": "模拟考试",
                "navigationBarBackgroundColor": "#fff"
            }
        },
        {
            "path": "pages/training/exam/start-test",
            "style": {
                "navigationBarTitleText": "模拟考试",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/end-test",
            "style": {
                "navigationBarTitleText": "考试报告",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/wrong",
            "style": {
                "navigationBarTitleText": "错题本",
                "navigationBarBackgroundColor": "#fff"
            }
        },
        {
            "path": "pages/training/exam/chapter",
            "style": {
                "navigationBarTitleText": "章节练习",
                "navigationBarBackgroundColor": "#fff"
            }
        },
        {
            "path": "pages/training/exam/buy",
            "style": {
                "navigationBarTitleText": "会员购买",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/video/list",
            "style": {
                "navigationBarTitleText": "注安课程",
                "navigationBarBackgroundColor": "#F3F3FF",
                "onReachBottomDistance": 120
            }
        },
        {
            "path": "pages/training/video/course-package",
            "style": {
                "navigationBarTitleText": "课程包",
                "navigationBarBackgroundColor": "#F3F3FF",
                "onReachBottomDistance": 120
            }
        },
        {
            "path": "pages/training/video/detail",
            "style": {
                "navigationBarTitleText": "课程详情",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/search/doc",
            "style": {
                "navigationBarTitleText": "资料搜索",
                "navigationBarBackgroundColor": "#FFFFFF"
            }
        },
        {
            "path": "pages/search/qa",
            "style": {
                "navigationBarTitleText": "动态搜索",
                "navigationBarBackgroundColor": "#F3F3FF",
                "onReachBottomDistance": 120
            }
        },
        {
            "path": "pages/search/class",
            "style": {
                "navigationBarTitleText": "课程搜索",
                "navigationBarBackgroundColor": "#F3F3FF",
                "onReachBottomDistance": 120
            }
        },
        {
            "path": "pages/me/index",
            "style": {
                "navigationBarTitleText": "我的",
                "navigationBarBackgroundColor": "#F3F3FF",
                "enablePullDownRefresh": true,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/me/course",
            "style": {
                "navigationBarTitleText": "已购课程",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/me/course-package",
            "style": {
                "navigationBarTitleText": "已购课程包",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/me/download",
            "style": {
                "navigationBarTitleText": "已购资料",
                "navigationBarBackgroundColor": "#fff"
            }
        },
        {
            "path": "pages/me/favorite",
            "style": {
                "navigationBarTitleText": "收藏夹",
                "navigationBarBackgroundColor": "#fff"
            }
        },
        {
            "path": "pages/me/integral-detail",
            "style": {
                "navigationBarTitleText": "积分明细",
                "navigationBarBackgroundColor": "#F3F3FF",
                "onReachBottomDistance": 120,
                "enablePullDownRefresh": true,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/me/balance-detail",
            "style": {
                "navigationBarTitleText": "余额明细",
                "navigationBarBackgroundColor": "#F3F3FF",
                "onReachBottomDistance": 120,
                "enablePullDownRefresh": true,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/me/invite",
            "style": {
                "navigationBarTitleText": "推荐好友",
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/me/order",
            "style": {
                "navigationBarTitleText": "我的订单",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/me/qa",
            "style": {
                "navigationBarTitleText": "我的动态",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/me/recharge",
            "style": {
                "navigationBarTitleText": "充值积分",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/me/setting",
            "style": {
                "navigationBarTitleText": "设置",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/me/change-name",
            "style": {
                "navigationBarTitleText": "设置",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/article/list",
            "style": {
                "navigationBarTitleText": "文章",
                "navigationBarBackgroundColor": "#F3F3FF",
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/article/detail",
            "style": {
                "navigationBarTitleText": "文章详情",
                "navigationBarBackgroundColor": "#FFFFFF"
            }
        },
        {
            "path": "pages/article/video",
            "style": {
                "navigationBarTextStyle": "white",
                "navigationBarTitleText": "视频详情",
                "navigationBarBackgroundColor": "#000"
            }
        },
        {
            "path": "pages/ask/index",
            "style": {
                "navigationBarTitleText": "安全问答",
                "navigationBarBackgroundColor": "#F3F3FF",
                "enablePullDownRefresh": true,
                "onReachBottomDistance": 120,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/ask/send",
            "style": {
                "navigationBarTitleText": "提问",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/ask/detail",
            "style": {
                "navigationBarTitleText": "问答详情",
                "navigationBarBackgroundColor": "#FFFFFF",
                "enablePullDownRefresh": true,
                "onReachBottomDistance": 120,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/ask/answer",
            "style": {
                "navigationBarTitleText": "回答问题",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/ask/home",
            "style": {
                "navigationBarTitleText": "安全圈",
                "navigationBarBackgroundColor": "#F3F3FF",
                "enablePullDownRefresh": true,
                "onReachBottomDistance": 120,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/ask/post",
            "style": {
                "navigationBarTitleText": "发动态",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/ask/topic-list",
            "style": {
                "navigationBarTitleText": "话题",
                "navigationBarBackgroundColor": "#F3F3FF",
                "onReachBottomDistance": 120,
                "enablePullDownRefresh": true,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/ask/n-detail",
            "style": {
                "navigationBarTitleText": "",
                "navigationBarBackgroundColor": "#FFFFFF"
            }
        },
        {
            "path": "pages/ask/topic-detail",
            "style": {
                "navigationBarTitleText": "话题详情",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/services/index",
            "style": {
                "navigationBarTitleText": "服务大厅",
                "navigationBarBackgroundColor": "#F3F3FF",
                "enablePullDownRefresh": true,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/services/intro",
            "style": {
                "navigationBarTitleText": "服务介绍",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/services/list",
            "style": {
                "navigationBarTitleText": "服务订单列表",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/services/detail",
            "style": {
                "navigationBarTitleText": "服务订单详情",
                "navigationBarBackgroundColor": "#F3F3FF",
                "enablePullDownRefresh": true,
                "app-plus": {
                    "pullToRefresh": {
                        "color": "#065cfe",
                        "style": "circle"
                    }
                }
            }
        },
        {
            "path": "pages/services/operate/submit-step",
            "style": {
                "navigationBarTitleText": "提交需求",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/services/operate/submit",
            "style": {
                "navigationBarTitleText": "提交需求",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/services/operate/pay",
            "style": {
                "navigationBarTitleText": "付款",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/services/operate/download",
            "style": {
                "navigationBarTitleText": "下载方案",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/services/operate/company-pay",
            "style": {
                "navigationBarTitleText": "对公转账",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/me/service",
            "style": {
                "navigationBarTitleText": "联系客服",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/about/index",
            "style": {
                "navigationBarTitleText": "关于我们",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/about/agreement",
            "style": {
                "navigationBarTitleText": "用户协议",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/about/privacy",
            "style": {
                "navigationBarTitleText": "隐私政策",
                "navigationBarBackgroundColor": "#F3F3FF"
            }
        },
        {
            "path": "pages/training/exam/swiper-test",
            "style": {
                "navigationBarTitleText": "滑动测试"
            }
        },
        {
            "path": "pages/services/form/form",
            "style": {
                "navigationBarTitleText": "提交需求"
            }
        },
        {
            "path": "pages/services/operate/advance-pay",
            "style": {
                "navigationBarTitleText": "付款"
            }
        },
        {
            "path": "pages/services/operate/solution_preview",
            "style": {
                "navigationBarTitleText": "方案确认",
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/selftest/index",
            "style": {
                "navigationBarTitleText": "行政处罚自测",
                "navigationBarBackgroundColor": "#fcf3f1"
            }
        },
        {
            "path": "pages/selftest/industry",
            "style": {
                "navigationBarTitleText": "机构行业"
            }
        },
        {
            "path": "pages/selftest/subject",
            "style": {
                "navigationBarTitleText": "题目"
            }
        },
        {
            "path": "pages/selftest/result",
            "style": {
                "navigationBarTitleText": "我的自测"
            }
        },
        {
            "path": "pages/selftest/result-detail",
            "style": {
                "navigationBarTitleText": "自测结果"
            }
        },
        {
            "path": "pages/expert/list",
            "style": {
                "navigationBarTitleText": "专家库",
                "onReachBottomDistance": 120
                //                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/expert/apply",
            "style": {
                "navigationBarTitleText": "申请认证"
            }
        },
        {
            "path": "pages/expert/detail",
            "style": {
                "navigationBarTitleText": "专家介绍"
            }
        }

    ],
    "subPackages": [
        {
            "root": "packageA",
            "pages": [
                {
                    "path": "ai/index",
                    "style": {
                        "backgroundTextStyle": "light",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "navigationBarTitleText": "安全助手",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "ai/record",
                    "style": {
                        "backgroundTextStyle": "light",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "navigationBarTitleText": "历史记录",
                        "navigationBarTextStyle": "black",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "packageOrg",
            "pages": [
                {
                    "path": "organization/me",
                    "style": {
                        "navigationBarTitleText": "我的",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/me/exam",
                    "style": {
                        "navigationBarTitleText": "考试记录",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/me/learn",
                    "style": {
                        "navigationBarTitleText": "学习记录",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/me/info",
                    "style": {
                        "navigationBarTitleText": "个人信息",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/photos",
                    "style": {
                        "navigationBarTitleText": "身份验证",
                        "navigationBarBackgroundColor": "#FFFFFF",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "organization/topics",
                    "style": {
                        "navigationBarTitleText": "",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "organization/course",
                    "style": {
                        "navigationBarTitleText": "课程列表",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "organization/start-test",
                    "style": {
                        "navigationBarTitleText": "考试",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/login/index",
                    "style": {
                        "navigationBarTitleText": "学员登录",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/login/id",
                    "style": {
                        "navigationBarTitleText": "身份证登录",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/login/phone",
                    "style": {
                        "navigationBarTitleText": "手机验证码登录",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/login/bind",
                    "style": {
                        "navigationBarTitleText": "绑定手机号",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "organization/home",
                    "style": {
                        "navigationBarTitleText": "培训机构",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true,
                        "app-plus": {
                            "pullToRefresh": {
                                "color": "#065cfe",
                                "style": "circle"
                            }
                        }
                    }
                },
                {
                    "path": "organization/package",
                    "style": {
                        "navigationBarTitleText": "",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "organization/enroll/index",
                    "style": {
                        "navigationBarTitleText": "培训报名",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "organization/enroll/apply-refund",
                    "style": {
                        "navigationBarTitleText": "申请退款",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "organization/enroll/refund-detail",
                    "style": {
                        "navigationBarTitleText": "退款详情",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true
                    }
                }
            ]
        },
        {
            "root": "packageInspect",
            "pages": [
                {
                    "path": "inspection/index",
                    "style": {
                        "navigationBarTitleText": "首页",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true,
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "inspection/task-device/index",
                    "style": {
                        "navigationBarTitleText": "巡检任务1",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/task-device/inspect",
                    "style": {
                        "navigationBarTitleText": "设备检查",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/task-device/record",
                    "style": {
                        "navigationBarTitleText": "巡检记录",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "inspection/task-device/add-device",
                    "style": {
                        "navigationBarTitleText": "添加巡检设备",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/approve/index",
                    "style": {
                        "navigationBarTitleText": "审批列表",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/approve/detail",
                    "style": {
                        "navigationBarTitleText": "巡检详情",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/task/index",
                    "style": {
                        "navigationBarTitleText": "创建巡检任务",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/task/my-task",
                    "style": {
                        "navigationBarTitleText": "我的任务",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/device/index",
                    "style": {
                        "navigationBarTitleText": "设备列表",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "inspection/device/detail",
                    "style": {
                        "navigationBarTitleText": "设备详情",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/device/form",
                    "style": {
                        "navigationBarTitleText": "新增设备",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/device/item-form",
                    "style": {
                        "navigationBarTitleText": "新增巡检项",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/device/ai",
                    "style": {
                        "navigationBarTitleText": "AI辅助创建",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/device/ai-create",
                    "style": {
                        "navigationBarTitleText": "AI辅助创建",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/hidden-danger/index",
                    "style": {
                        "navigationBarTitleText": "隐患列表",
                        "navigationBarBackgroundColor": "#F3F3FF",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "inspection/hidden-danger/clue",
                    "style": {
                        "navigationBarTitleText": "信息填写",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/hidden-danger/create",
                    "style": {
                        "navigationBarTitleText": "随手拍",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                },
                {
                    "path": "inspection/hidden-danger/detail",
                    "style": {
                        "navigationBarTitleText": "隐患详情",
                        "navigationBarBackgroundColor": "#F3F3FF"
                    }
                }
            ]
        }
    ],
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "uni-app",
        "navigationBarBackgroundColor": "#F3F3FF",
        "backgroundColor": "#F3F3FF"
    },
    "tabBar": {
        "color": "#999999",
        "selectedColor": "#390ABC",
        "backgroundColor": "#fff",
        "list": [
            {
                "selectedIconPath": "static/tabbar/home-6-fill.png",
                "iconPath": "static/tabbar/home-6-line.png",
                "pagePath": "pages/index/index",
                "text": "首页"
            },
            {
                "selectedIconPath": "static/tabbar/bill-fill.png",
                "iconPath": "static/tabbar/bill-line.png",
                "pagePath": "pages/document/index",
                "text": "资料"
            },
            {
                "selectedIconPath": "static/tabbar/graduation-cap-fill.png",
                "iconPath": "static/tabbar/graduation-cap-line.png",
                "pagePath": "pages/training/index",
                "text": "培训"
            },
            {
                "selectedIconPath": "static/tabbar/user-4-fill.png",
                "iconPath": "static/tabbar/user-4-line.png",
                "pagePath": "pages/me/index",
                "text": "我的"
            }
        ]
    }
}