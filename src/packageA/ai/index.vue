<template>
	<view class="content">
		<navbar-custom />

		<view class="ai-cnt">
			<scroll-view class="scroll-Y" :scroll-y="true" :scroll-top="scrollTop">
				<view class="scroll-content">
					<view class="welcome-box">
						<view class="wb-tit">Hi,{{ nickname }}</view>
						<view class="wb-subtit">我是你的专属AI助手，帮你答疑解惑</view>
						<view class="wb-question-tit">
							<view class="wbqt-tit">你可以试着问我：</view>
							<view class="wbqt-change-btn" @click="requestRecommendList()">
								<image src="@/static/images/icon/restart-line.png"></image>
								<text>换一换</text>
							</view>
						</view>
						<view class="wb-question-list">
							<view class="wbql-item" v-for="(item, index) in chatRecommend" :key="index">
								<view class="wbql-i-tit" @click="quickSend(item)">{{ item }}</view>
								<view class="wbqlit-icon" @click="quickSend(item)">
									<image src="@/static/images/icon/arrow-right-s-line.png"></image>
								</view>
							</view>
						</view>
					</view>

					<view v-for="(item, index) in chatMessage" :key="index">
						<view class="ask-text-box">
							<view class="atb-main">{{ item.prompt }}</view>
						</view>
						<view class="answer-text-box">
							<view v-if="chatMessageIndex === index" class="atb-rich-box">
								<markdown-view v-if="typerText.length > 0" :markdown="typerText.join('')"></markdown-view>

								<chat-loading v-if="!item.finish && !typerPause"  />
							</view>
							<view v-else class="atb-rich-box">
								<markdown-view :markdown="item.completion"></markdown-view>
							</view>
							<view v-show="item.finish" class="atb-option">
								<view class="atbo-r">
									<view v-if="item.like !== 1" class="atbor-btn" @click="requestLike(index, 1)">
										<image src="@/static/images/icon/thumbs-up.png"></image>
									</view>
									<view v-if="item.like === 1"  class="atbor-btn on" @click="requestLike(index, 0)">
										<image src="@/static/images/icon/thumbs-up-white.png"></image>
									</view>
									<view v-if="item.like !== 2"  class="atbor-btn" @click="requestLike(index, 2)">
										<image src="@/static/images/icon/thumbs-down.png"></image>
									</view>
									<view v-if="item.like === 2"  class="atbor-btn on" @click="requestLike(index, 0)">
										<image src="@/static/images/icon/thumbs-down-white.png"></image>
									</view>
									<view class="atbor-btn atborb-more" @click="openAction(index)">
										<image src="@/static/images/icon/more.png"></image>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 停止回答按钮 -->
					<view class="foot-empty-block">
						<view v-show="chatLoading">
							<view v-if="!typerPause" class="feb-stop-btn" @click="stopChat()">
								<image src="@/static/images/icon/stop-circle-line.png"></image>
								<text>停止回答</text>
							</view>
							<view v-if="typerPause && !streamLoading" class="feb-stop-btn" @click="reloadChat()">
								<image src="@/static/images/icon/refresh-fill.png"></image>
								<text>重新回答</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<view class="ai-foot">
			<view class="af-box">
				<view class="af-input-box">
					<input class="uni-input"
					   v-model="keyword"
					   :always-embed="true"
					   :adjust-position="true"
					   :cursor-spacing="cursorSpacing"
					   @confirm="send()"
					   placeholder="你可以问安全生产相关的任何问题哦"/>
					<view class="afib-btn" @click="send()">
						<image src="@/static/images/icon/send-plane-fill.png"></image>
					</view>
				</view>
			</view>
		</view>

		<!-- 更多操作 -->
		<view class="ai-modelbox" v-if="showAction" @click="closeAction()">
			<view class="aimb-part">
				<view class="aimbp-item" @click.stop="copy()">
					<image src="@/static/images/icon/file-copy-fill.png"></image>
					<text>复制内容</text>
				</view>
				<view class="aimbp-item" @click.stop="retry()">
					<image src="@/static/images/icon/refresh-fill.png"></image>
					<text>重新回答</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import api, { getUrl, getToken } from "@/lib/api";
import { useUserStore } from "@/store/user";
import { mapState } from "pinia";
import MarkdownView from './components/markdown.vue';
import NavbarCustom from './components/navbar.vue';
import ChatLoading from './components/loading.vue';

export default {
	components: { MarkdownView, NavbarCustom, ChatLoading },
	data() {
		return {
			cursorSpacing: 30,
			scrollTop: 0,
			scrollHeight: 0,
			type: 'append',
			showAction: false,
			nickname: '-',
			requestLoading: false,
			chatLoading: false,
			chatActionIndex: 0,
			chatMessageIndex: undefined,
			chatMessage: [],
			chatRecommend: [],
			prompt: '',
			completion: '',
			keyword: '',
			streamKey: '',
			streamTimer: null,
			streamLoading: false,
			sessionId: 0,
			currentId: 0,
			typerPause: false,
			typerLoading: false,
			typerText: [],
			typerIndex: 0,
			typerSpeed: 50,
			typerTimer: null
		}
	},
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    watch: {
        loaded(newValue) {
            if (newValue && this.user) {
                this.init();
            }
        }
    },
	onLoad(e) {
		if (e.id) {
			this.sessionId = parseInt(e.id);
		}

        if (this.user) {
            this.init();
        } else {
            uni.switchTab({
                url:'/pages/index/index'
            })
        }
	},
	methods: {
        init() {
			switch (uni.getSystemInfoSync().platform) {
				case 'ios':
					this.cursorSpacing = 70;
					break;
				default:
					this.cursorSpacing = 30;
			}

            this.nickname = this.user.nickname;

            this.requestRecommendList();

            this.$nextTick(() => {
                this.initScrollHeight()

                if (this.sessionId) {
                    this.requestMessageList();
                } else {
                    this.chatMessageIndex = 0;
                }
            });
		},

		initScrollHeight() {
			uni.createSelectorQuery()
				.in(this)
				.select('.scroll-Y')
				.boundingClientRect(data => {
					if (data) {
						this.scrollHeight = data.height
					}
				})
				.exec();
		},
		// 获取内容高度
		initContentHeight() {
			setTimeout(() => {
				uni.createSelectorQuery()
					.in(this)
					.select('.scroll-content')
					.boundingClientRect(data => {
						if (data) {
							let top = data.height - this.scrollHeight;
							if (top > 0) {
								this.scrollTop = top;
							}
						}
					})
					.exec();
			}, 150)
		},
		stopChat() {
			this.type = 'pause';
			this.typerPause = true;
            this.chatLoading = false;

			if (this.streamTimer !== null) {
				clearTimeout(this.streamTimer);
				this.streamTimer = null;
			}

			if (this.typerTimer !== null) {
				clearTimeout(this.typerTimer);
				this.typerTimer = null;
			}
		},
		reloadChat() {
			this.chatLoading = false;
			this.typerPause = false;
			this.keyword = this.chatMessage[this.chatMessage.length - 1].prompt;
			this.send();
		},
		openAction(index) {
			if (index !== this.chatMessage.length - 1) {
				return;
			}

			if (this.chatLoading) {
				return;
			}

			this.chatActionIndex = index;
			this.showAction = true;
		},
		closeAction() {
			this.showAction = false;
		},
		copy() {
			uni.setClipboardData({
				data: this.chatMessage[this.chatActionIndex].completion,
				success: () => {
					uni.showToast({
						icon: 'none',
						title: '复制成功',
						duration: 2000
					});
					this.closeAction();
				},
				fail: () => {
					uni.showToast({
						icon: 'none',
						title: '复制失败',
						duration: 2000
					});
				}
			});
		},
		retry() {
			if (this.chatActionIndex === this.chatMessage.length - 1) {
				this.type = 'retry';
				this.keyword = this.chatMessage[this.chatActionIndex].prompt;
				this.currentId = this.chatMessage[this.chatActionIndex].id;
				this.chatMessage[this.chatActionIndex].finish = false;
				this.send();
				this.closeAction();
			}
		},
		typer(step) {
			this.typerText.push(this.completion.substring(this.typerIndex, this.typerIndex + step))
			if (this.typerIndex < this.completion.length) {
				if (!this.typerPause) {
					this.typerIndex += step;
					this.typerTimer = setTimeout(() => {
						let count = this.completion.length - this.typerIndex;
						let stepTmp = Math.ceil(count / 10);

						this.typer(stepTmp);
					}, this.typerSpeed)

					this.$nextTick(() => {
						this.initContentHeight()
					})
				}
			} else {
				this.typerLoading = false;
			}
		},
		handleChatContent(content) {
			let length = content.length;
			let contents = content.split('');

			for (let i = 0; i < length; i++) {
				this.completion += contents[i]
			}

			if (!this.typerLoading) {
				this.typerLoading = true;
				this.typer(1);
			}
		},
		handleChatSuccess() {
			if (!this.completion) {
				return;
			}

			let isCreate = true;

			if (this.currentId) {
				if (this.type === 'pause') {
					isCreate = false;
				}

				if (this.type === 'retry') {
					isCreate = false;
				}
			}

			if (isCreate) {
				api.post('chat/messages', {
					prompt: this.prompt,
					completion: this.completion,
					session_id: this.sessionId
				}).then(res => {
					if (this.chatMessageIndex === 0) {
						this.sessionId = res.session_id;
					}

					this.chatMessage[this.chatMessageIndex] = {...res, finish: true};
					this.type = 'append';
					this.prompt = '';
				}).catch(e => {
					uni.showModal({
						title: '温馨提示',
						content: e.message,
						showCancel: false,
						confirmText: '我知道了'
					});
				}).finally(() => {
					this.chatLoading = false;

					this.$nextTick(() => {
						this.initContentHeight();
					})
				});
			} else {
				api.put('chat/messages/' + this.currentId, {
					completion: this.completion,
					like: 0
				}).then(res => {
					this.chatMessage[this.chatActionIndex] = {...res, finish: true};
					this.type = 'append';
					this.prompt = '';
				}).catch(e => {
					uni.showModal({
						title: '温馨提示',
						content: e.message,
						showCancel: false,
						confirmText: '我知道了'
					});
				}).finally(() => {
					this.chatLoading = false;

					this.$nextTick(() => {
						this.initContentHeight();
					})
				});
			}
		},
		quickSend(keyword) {
			if (this.chatLoading) {
				return;
			}

			this.keyword = keyword;
			this.send();
		},
		send() {
			this.$nextTick(function() {
				this.scrollTop = 0
			});

			if (this.chatLoading) {
				return;
			}

			if (!this.keyword) {
				uni.showModal({
					title: '温馨提示',
					content: "请输入提示词",
					showCancel: false,
					confirmText: '我知道了'
				});

				return;
			}

			this.prompt = this.keyword;
			this.keyword = '';
			this.chatLoading = true;
			this.completion = '';
			this.typerPause = false;
			this.typerLoading = false;
			this.typerIndex = 0;
			this.typerText = [];

			switch (this.type) {
				case 'pause':
				case 'retry':
					this.$nextTick(() => {
						this.initContentHeight();
					})
					break;
				default:
					this.chatMessageIndex = this.chatMessage.length;

					this.chatMessage[this.chatMessageIndex] = {
						prompt: this.prompt,
						completion: '',
						like: 0,
						finish: false
					}

					this.$nextTick(() => {
						this.initContentHeight();
					})
			}

			let messages = [];

			if (this.chatMessage.length > 0) {
				this.chatMessage.forEach(item => {
					if (item.completion) {
						messages.push({
							role: 'user',
							content: item.prompt,
						});
						messages.push({
							role: 'assistant',
							content: item.completion,
						})
					}
				});

				messages = messages.splice(-8);
			}

            // #ifdef MP-WEIXIN
            this.getChatBuffer(messages);
            // #endif

            // #ifndef MP-WEIXIN
            api.post('chat/stream', {
                prompt: this.prompt,
                messages
            }).then(res => {
                this.streamKey = res.data;

                this.streamTimer = setTimeout(() => {
                    this.getChat();
                }, 1500)
            }).catch(e => {
                this.stopChat();

                uni.showModal({
                    title: '温馨提示',
                    content: e.message,
                    showCancel: false,
                    confirmText: '我知道了'
                });
            });
            // #endif
		},
        getChatBuffer(messages) {
            this.streamLoading = true;

            const requestTask = uni.request({
                url: getUrl('chat/chat'),
                method: 'POST',
                data: {
                    prompt: this.prompt,
                    messages,
                    stream: 1,
                    mode: 'applet'
                },
                responseType: "arraybuffer",
                enableChunked: true,
                header: {
                    'content-type': 'application/json',
                    'Authorization': 'Bearer ' + getToken()
                },
                success: () => {
                    this.streamLoading = false;

                    console.log('stream finished');
                },
                fail: (err) => {
                    console.log("request fail", err);
                },
            });

            requestTask.onChunkReceived((response) => {
                const arrayBuffer = new Uint8Array(response.data);

                let resultText = '';

                for (let i in arrayBuffer) {
                    resultText += String.fromCharCode(arrayBuffer[i]);
                }

                if (resultText) {
                    resultText = resultText.trim().split("\n\n");

                    resultText.forEach(item => {
                        const list = JSON.parse(item);

                        for (let i in list) {
                            this.handleChatContent(list[i].result);

                            this.$nextTick(() => {
                                if (!this.typerPause && list[i].is_end) {
                                    this.handleChatSuccess();
                                }
                            })
                        }
                    })
                }
            });
        },
		getChat() {
			if (!this.streamKey) {
				return;
			}

			this.streamLoading = true;

			api.post('chat/streamCallback', {
				hash: this.streamKey
			}).then(res => {
				const list = res.data;

				for (let i in list) {
					this.handleChatContent(list[i].result);

					if (list[i].is_end) {
						console.log('stream finish', list[i].is_end);

						this.streamKey = '';
						this.handleChatSuccess();
					}
				}

				if (!this.typerPause) {
					this.streamTimer = setTimeout(() => {
						this.getChat();
					}, 1000);
				}
			}).catch(e => {
				uni.showModal({
					title: '温馨提示',
					content: e.message,
					showCancel: false,
					confirmText: '我知道了'
				});
			}).finally(() => {
				this.streamLoading = false;
			});
		},
		requestLike(index, like) {
			if (this.requestLoading) {
				return;
			}

			this.requestLoading = true;

			api.post('chat/messages/like', {
				id: this.chatMessage[index].id,
				like
			}).then(() => {
				this.chatMessage[index].like = like;

				this.$forceUpdate();
			}).finally(() => {
				this.requestLoading = false;
			});
		},
		requestRecommendList() {
			if (this.requestLoading) {
				return;
			}

			this.requestLoading = true;

			api.post('chat/sessions/recommend').then(res => {
				this.chatRecommend = res;

				this.$nextTick(() => {
					this.initContentHeight();
				})
			}).finally(() => {
				this.requestLoading = false;
			});
		},
		requestMessageList() {
			api.get('chat/messages', {
				session_id: this.sessionId
			}).then(res => {
				res.data.forEach(item => {
					this.chatMessage.push({
						...item,
						finish: true
					})
				})
				this.chatMessageIndex = this.chatMessage.length - 1;
				this.typerText = this.chatMessage[this.chatMessageIndex].completion.split('');

				this.$nextTick(() => {
					this.initContentHeight();
				})
			}).catch(() => {});
		},
	}
}
</script>

<style>
	.scroll-Y {
		height: 100%;
	}
	page,
	.content {
		width: 100%;
		height: 100%;
        background-color: #f3f3ff;
	}
	.content {
		display: flex;
		flex-direction: column;
	}
	.ai-cnt {
		flex: 1;
		overflow-y: scroll;
	}
	.ai-foot {
		padding-bottom: env(safe-area-inset-bottom);
		background-color: #fff;
	}
	.af-box {
		padding: 30rpx;
	}
	.af-input-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 100rpx;
		border-radius: 100rpx;
		border: 1px solid #e7e7e7;
		padding: 10rpx;
		box-sizing: border-box;
	}
	.af-input-box .uni-input {
		flex: 1;
		box-sizing: border-box;
		padding-left: 20rpx;
	}
	.afib-btn {
		width: 80rpx;
		height: 80rpx;
		border-radius: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #f3f3ff;
		margin-left: 20rpx;
	}
	.afib-btn image {
		width: 36rpx;
		height: 36rpx;
	}


	.welcome-box {
		margin: 20rpx;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
	}
	.wb-tit {
		height: 36rpx;
		font-weight: bold;
	}
	.wb-subtit {
		font-size: 28rpx;
		padding-top: 20rpx;
		color: #999;
		padding-bottom: 30rpx;
	}
	.wb-question-tit {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	.wbqt-tit {
		font-weight: bold;
	}
	.wbqt-change-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx;
	}
	.wbqt-change-btn image {
		width: 32rpx;
		height: 32rpx;
	}
	.wbqt-change-btn text {
		font-size: 28rpx;
		padding-left: 10rpx;
	}

	.wbql-item {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 0;
	}
	.wbql-i-tit {
		flex: 1;
		line-height: 1.6;
	}
	.wbqlit-icon {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 50rpx;
		height: 50rpx;
	}
	.wbqlit-icon image {
		width: 36rpx;
		height: 36rpx;
	}

	.ask-text-box {
		padding: 20rpx;
		display: flex;
		justify-content: flex-end;
	}
	.atb-main {
		background-color: #390abc;
		border-radius: 20rpx 0 20rpx 20rpx;
		padding: 30rpx;
		color: #fff;
		line-height: 1.6;
	}

	.answer-text-box {
		padding: 0 30rpx;
		background-color: #fff;
		border-radius:0 20rpx 20rpx 20rpx;
		margin: 20rpx;
	}
	.atb-rich-box {
		background-color: #fff;
		color: #333;
		line-height: 1.6;
		white-space: pre-wrap;
	}
	.atbor-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		width: 80rpx;
		border: 1px solid #E7E7E7;
		border-radius: 100%;
		margin: 0 10rpx;
	}
	.atbor-btn image {
		width: 36rpx;
		height: 36rpx;
	}
	.atbor-btn.on {
		background-color: #F54141;
		border: 1px solid #F54141;
	}
	.atbo-r {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-bottom: 20rpx;
	}

	.foot-empty-block {
		height: 150rpx;
	}
	.feb-stop-btn {
		position: fixed;
		bottom: 200rpx;
		left: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		width: 240rpx;
		margin-left: -120rpx;
		margin-bottom: env(safe-area-inset-bottom);
		border-radius: 80rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, .05);
	}
	.feb-stop-btn image {
		width: 36rpx;
		height: 36rpx;
	}
	.feb-stop-btn text {
		font-size: 28rpx;
		padding-left: 10rpx;
		padding-bottom: 4rpx;
	}

	.ai-modelbox {
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 20;
		background-color: rgba(0, 0, 0, .5);
	}
	.aimb-part {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-around;
		background-color: #fff;
		padding-left: 30rpx;
		padding-right: 30rpx;
		padding-top: 20rpx;
		padding-bottom: env(safe-area-inset-bottom);
		border-radius: 30rpx 30rpx 0 0;
	}
	.aimbp-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0 20rpx;
	}
	.aimbp-item image {
		width: 64rpx;
		height: 64rpx;
	}
	.aimbp-item text {
		font-size: 28rpx;
		padding-top: 20rpx;
	}

	.atb-loading {
		padding: 20rpx 0 10rpx;
		text-align: center;
	}
	.atb-loading image {
		width: 48rpx;
		height: 48rpx;
		animation: rotate 500ms infinite linear;
	}
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
