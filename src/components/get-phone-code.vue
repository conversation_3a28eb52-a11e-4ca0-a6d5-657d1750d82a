<!--发送手机验证码组件-->
<template>
    <view class="get-code-box">
        <input class="uni-input" placeholder="四位验证码" :value="modelValue" :maxlength="codeLength" @input="$emit('update:modelValue', $event.detail.value)" />
        <button class="gcb-btn" :loading="sending" @click="sendPhoneCode" :disabled="dis">{{sendText}}</button>
    </view>
</template>

<script>
import api from '@/lib/api.js';

export default {
    props: {
        //手机号
        phone: String,
        // 验证码类型
        type: String,
        modelValue: String,
        codeLength: {
            type: Number,
            default: 4
        }
    },
    data() {
        return {
            sending: false,
            counting: 0,
            timerId: null
        }
    },
    computed: {
        dis() {
            return this.sending || this.counting > 0;
        },
        sendText() {
            if (this.sending) {
                return "发送中...";
            } else if (this.counting > 0) {
                return "重新发送(" + this.counting + "s)";
            } else {
                return "发送验证码";
            }
        }
    },
    unmounted() {
        if (this.timerId !== null) {
            this.timerId = null;
        }
    },
    emits: ['update:modelValue'],
    methods: {
        sendPhoneCode() {
            if (!this.phone) {
                uni.showModal({
                    title: "温馨提示",
                    content: "请输入手机号码",
                    showCancel: false
                });
                return;
            }

            this.sending = true;

            api.post("phone-code", {phone: this.phone, type: this.type }).then(res => {
                //倒计时
                this.counting = 60;
                this.timerId = setInterval(() => {
                    this.counting--;
                    if (this.counting <= 0) {
                        clearInterval(this.timerId);
                        this.timerId = null;
                        this.counting = 0;
                    }
                }, 1000);

                uni.showToast({
                    title: "验证码已发送",
                    icon: "none"
                });
            }).catch(e => {
                uni.showModal({
                    title: "发送失败",
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                this.sending = false;
            });
        }
    }
}
</script>
<style>
    .get-code-box {
        position: relative;
        margin-top: 30rpx;
    }

    .uni-input {
        height: 100rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
    }

    .gcb-btn {
        position: absolute;
        right: 0;
        top: 0;
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        color: forestgreen;
        z-index: 2;
        border: none;
        background-color: transparent;
        border: 0 12rpx 12rpx 0;
    }
    .gcb-btn::after {
        display: none;
    }
</style>