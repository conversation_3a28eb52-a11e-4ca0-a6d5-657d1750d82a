<template>
    <uni-popup ref="popup" type="bottom" :safe-area="false">
        <view class="choose-pay-box">
            <view class="cpb-tit">请选择支付方式</view>
            <view class="cpb-content">
                <view class="cpbc-item" :class="{'cur': buyType == 'balance'}" v-if="allowTypes.indexOf('balance') > -1 && user?.balance_show" @click="buyType = 'balance'">
                    <view class="cpbci-l">
                        <image src="@/images/icon/wallet-2-fill.png"></image>
                        <view class="cpbcil-cnt">
                            <view class="cpbcilc-tit">钱包余额支付</view>
                            <view class="cpbcilc-tips">￥{{ order?.total_amount }}</view>
                        </view>
                    </view>
                    <view class="cpbci-r" v-if="parseFloat(user?.balance) < parseFloat(order?.total_amount)">
                        余额不足
                    </view>
                    <view class="cpbci-check">
                        <image src="@/images/icon/check-line.png"></image>
                    </view>
                </view>
                <view class="cpbc-item" :class="{'cur': buyType == 'credit'}" v-if="allowTypes.indexOf('credit') > -1" @click="buyType = 'credit'">
                    <view class="cpbci-l">
                        <image src="@/images/icon/copper-coin-fill-2.png"></image>
                        <view class="cpbcil-cnt">
                            <view class="cpbcilc-tit">使用积分支付</view>
                            <view class="cpbcilc-tips">{{ credit }}积分</view>
                        </view>
                    </view>
                    <view class="cpbci-r" v-if="user?.credit < credit">
                        积分不足，<navigator url="/pages/me/recharge">去充值</navigator>
                    </view>
                    <view class="cpbci-check">
                        <image src="@/images/icon/check-line.png"></image>
                    </view>
                </view>
                <view class="cpbc-item" :class="{'cur': buyType == 'amount'}" v-if="allowTypes.indexOf('amount') > -1" @click="buyType = 'amount'">
                    <view class="cpbci-l">
                        <image src="@/images/icon/wechat-pay-fill.png"></image>
                        <view class="cpbcil-cnt">
                            <view class="cpbcilc-tit">使用微信支付</view>
                            <view class="cpbcilc-tips">￥{{ order?.total_amount }}</view>
                        </view>
                    </view>
                    <view class="cpbci-check">
                        <image src="@/images/icon/check-line.png"></image>
                    </view>
                </view>
            </view>

            <view class="cpb-foot">
                <view class="cpbf-btn cancle" @click="this.$refs.popup.close()">取消</view>
                <view class="cpbf-btn submit" v-if="buyType == 'credit'" @click="buyFromCredit">确认支付</view>
                <view class="cpbf-btn submit" v-else-if="buyType == 'amount'" @click="buyFromAmount">确认支付</view>
                <view class="cpbf-btn submit" v-else @click="buyFromBalance">确认支付</view>
            </view>
        </view>
    </uni-popup>
</template>
<script>
import {alert, showDelayLoading} from "@/lib/utils";
import api from "@/lib/api";
import {makePayment} from "@/lib/pay";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store/user";

export default {
    props: {
        allowTypes: {
            type: Array,
            require: true
        },
        order: {
            type: Object,
            default: {}
        },
        credit: {
            type: Number,
            default: 0
        },
        title: {
            type: String,
            default: '购买资料'
        }
    },
    data() {
        return {
            buyType: ''
        }
    },
    computed: {
        ...mapState(useUserStore, ["user", "reload"])
    },
    watch: {
        order(newValue, oldValue) {
            if (newValue) {
                this.$forceUpdate()
            }
        }
    },
    methods: {
        open() {
            this.$refs.popup.open('bottom')
        },
        close() {
            this.$refs.popup.close()
        },
        buyFromCredit() {
            if (this.user.credit >= this.credit) {
                uni.showModal({
                    title: this.title,
                    content: `您将花费${this.credit}积分购买该内容，确认要购买吗？`,
                    confirmText: "确认购买",
                    success: (res) => {
                        if (res.confirm) {
                            this.$emit('creditPay')
                        }
                    }
                })
            } else {
                this.creditNotEnough(this.user.credit)
            }
        },
        creditNotEnough(credit) {
            uni.showModal({
                title: '积分不足',
                content: "当前积分余额：" + credit + "，无法完成购买。请充值或者通过完成任务来获得更多积分",
                confirmText: "去充值",
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: "/pages/me/recharge"
                        })
                    }
                }
            })
        },
        buyFromAmount() {
            this.$emit('createdOrder', 'amount')
        },
        payFromAmount() {
            uni.showLoading({
                title: "发起支付中",
                mask: true
            });

            makePayment(this.order.order_no, "wechat", "mp").then(() => {
                uni.showToast({
                    title: "购买成功",
                    icon: "success",
                    duration: 1500,
                    success: () => {
                        this.$emit('paymentCallback')
                        this.$refs.popup.close()
                        this.reload()
                    }
                });
            }).catch(e => {
                uni.showModal({
                    title: "支付失败",
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                uni.hideLoading();
            })
        },
        buyFromBalance() {
            uni.showModal({
                content: "您将花费" + this.order.total_amount + "余额购买该内容，确认要购买吗？",
                confirmText: "确认购买",
                success: (res) => {
                    if (res.confirm) {
                        this.$emit('createdOrder', 'balance')
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        payFromBalance() {
            // 扣除对应积分
            const hideLoading = showDelayLoading("支付中", 200)
            api.post('balances/payment', {order_no: this.order.order_no}).then(res => {
                uni.showToast({
                    title: "购买成功",
                    icon: "success",
                    duration: 1500,
                    success: () => {
                        this.$emit('paymentCallback')
                        this.$refs.popup.close()
                        this.reload()
                    }
                });
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        }
    }
}
</script>

<style>
/* 收费弹窗 */
.choose-pay-box {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding-bottom: env(safe-area-inset-bottom);
}
.cpb-tit {
    height: 120rpx;
    line-height: 120rpx;
    text-align: center;
    font-size: 28rpx;
    color: #999;
}
.cpb-content {
    padding: 0 30rpx;
}
.cpbc-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #E7E7E7;
    border-radius: 12rpx;
    height: 160rpx;
    padding: 0 30rpx;
    margin-bottom: 30rpx;
}
.cpbci-l {
    display: flex;
    align-items: center;
}
.cpbci-l image {
    width: 80rpx;
    height: 80rpx;
}
.cpbcil-cnt {
    padding-left: 20rpx;
}
.cpbcilc-tit {
    font-weight: bold;
    font-size: 28rpx;
}
.cpbcilc-tips {
    font-size: 28rpx;
    color: #999;
    padding-top: 10rpx;
}
.cpbci-r {
    font-size: 28rpx;
}
.cpbci-r navigator {
    display: inline-block;
    color: #390abc;
    text-decoration: underline;
}
.cpbci-check {
    position: absolute;
    top: -1px;
    right: -1px;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    background-color: #390ABC;
    border-radius: 0 12rpx 0 12rpx;
}
.cpbci-check image {
    width: 32rpx;
    height: 32rpx;
}
.cpbc-item.cur {
    background-color: #f3f3ff;
    border: 1px solid #390ABC;
}
.cpbc-item.cur .cpbci-check {
    display: flex
}

.cpb-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx 30rpx;
}
.cpbf-btn {
    height: 100rpx;
    background-color: #f3f3f3;
    line-height: 100rpx;
    border-radius: 12rpx;
    width: 200rpx;
    text-align: center;
    font-weight: bold;
}
.cpbf-btn.submit {
    background-color: #390ABC;
    color: #fff;
    flex: 1;
    margin-left: 30rpx;
}
</style>