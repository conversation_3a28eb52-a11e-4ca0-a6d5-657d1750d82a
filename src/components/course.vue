<template>
    <view class="video-list-box" v-if="list.length > 0">
        <view class="vlb-pane">
            <view class="video-item" v-for="item in list" :key="item.sid" @click="goCourseDetail(item)">
                <image :src="item.cover_src"></image>
                <view class="vi-info">
                    <view class="vii-tit">{{ item.title }}</view>
                    <view class="vii-teacher">讲师：{{ item.resource.teacher_name }}</view>
                    <view class="vii-foot">
                        <view class="viif-l">{{ item.resource.learning_count }}人在学</view>
                        <view class="viif-r" v-if="item.view_limit == limitType.amount">￥{{ item.charge_amount }}</view>
                        <view class="viif-r" v-if="item.view_limit == limitType.credit">{{ item.charge_credit }}积分</view>
                        <view class="viif-r" v-if="item.view_limit == limitType.credit_amount">{{ item.charge_credit }}积分/￥{{ item.charge_amount }}</view>
                        <view class="viif-r" v-if="item.view_limit == limitType.free">免费</view>
                    </view>
                </view>
            </view>
        </view>
        <uni-load-more v-if="list.length" :status="listStatus"></uni-load-more>
    </view>
    <!-- 暂无数据 -->
    <view class="no-data-nomal-box" v-if="listLoaded && !listLoading && list.length === 0">
        <view class="ndnb-icon">
            <image src="@/images/empty.png" mode="widthFix"></image>
        </view>
        <text class="ndnb-tip">抱歉，未检索到相关课程</text>
    </view>
</template>

<script>
    import {alert, getDetailUrl} from "@/lib/utils";
    import { ContentViewLimit as cvl } from "@/lib/enums";

    export default {
        props: {
            list: {
                type: Array,
                default: []
            },
            listStatus: {
                type: String,
                default: 'more'
            },
            listLoaded: {
                type: Boolean,
                default: false
            },
            listLoading: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                limitType: cvl
            }
        },
        methods: {
            goCourseDetail(item) {
                let url = getDetailUrl('course', item?.sid)
                if (url === '') {
                    alert("未获取到详情地址")
                    return
                }
                uni.navigateTo({
                    url: url
                })
            }
        }
    }
</script>

<style>
    .vlb-pane {
        padding: 30rpx;
    }

    .vlbt-item {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 40rpx;
        color: #999;
        font-size: 32rpx;
    }

    .vlbt-item.cur {
        position: relative;
        font-weight: bold;
        color: #333;
    }

    .vlbt-item.cur::after {
        content: '';
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 20rpx;
        width: 20rpx;
        height: 8rpx;
        border-radius: 8rpx;
        background-color: #390abc;
    }

    .video-item {
        display: flex;
        align-items: center;
        background: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }

    .video-item image {
        width: 200rpx;
        height: 200rpx;
        border-radius: 12rpx;
    }

    .vi-info {
        flex: 1;
        padding-left: 20rpx;
    }

    .vii-tit {
        height: 80rpx;
        line-height: 40rpx;
        font-weight: bold;
        font-size: 28rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }

    .vii-teacher {
        padding: 10rpx 0;
        font-size: 28rpx;
    }

    .vii-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24rpx;
        padding-top: 8rpx;
    }

    .viif-l {
        color: #999;
    }

    .viif-r {
        color: red;
        font-weight: bold;
    }
</style>