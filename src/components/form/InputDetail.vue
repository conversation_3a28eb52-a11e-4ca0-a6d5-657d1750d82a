<template>
    <template >
        <template v-if="type == 'group'">
            <view :class="{'ss-group-box': keyIndex > 0}" >
                <view class="ssgb-tit">{{ title }}</view>
            </view>
        </template>
        <template v-else-if="type == 'text'">
            <view class="single-txt" >
                <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                <view class="st-input">
                    <input
                        :value="value"
                        @input="inputValue"
                        :type="inputType()"
                        :maxlength="inputMaxLen()"
                        :placeholder="inputDesc()"
                    />
                </view>
                <view class="uf-review-tip not-pass" v-if="status == 3">审核不通过：{{reject_reason}}</view>
                <view class="uf-review-tip pass"  v-if="status == 2">审核通过</view>

            </view>
        </template>
        <template v-else-if="type == 'select'">
            <view class="single-txt" >
                <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>

                <picker @change="bindPickerChange" class="dropdown-part" :value="selectIndex" :range="options.options">
                    <text>{{selectText}}</text>
                    <image src="../../images/icon/arrow-down-s-line.png"></image>
                </picker>

                <view class="uf-review-tip not-pass" v-if="status == 3">审核不通过：{{reject_reason}}</view>
                <view class="uf-review-tip pass"  v-if="status == 2">审核通过</view>

            </view>
        </template>
        <template v-else-if="type == 'checkbox'">
            <view class="single-cb" >
                <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                <view class="st-checkbox">
                    <checkbox-group @change="groupChange">
                        <checkbox v-for="(item, index) in options.options" color="#390ABC" :key="index" :value="item" :checked="checkboxChecked(item)" >{{item}}</checkbox>
                    </checkbox-group>
                </view>
                <view class="uf-review-tip not-pass" v-if="status == 3">审核不通过：{{reject_reason}}</view>
                <view class="uf-review-tip pass"  v-if="status == 2">审核通过</view>
            </view>
        </template>
        <template v-else-if="type == 'textarea'">
            <view class="mtextarea">
                <view class="mt-tit">{{title}}<text v-if="isRequired">*</text></view>
                <view class="mt-cnt">
                    <textarea :value="value"
                              @input="inputValue"
                              :maxlength="inputMaxLen()"
                              :placeholder="inputDesc()" />
                </view>
                <view class="uf-review-tip not-pass" v-if="status == 3">审核不通过：{{reject_reason}}</view>
                <view class="uf-review-tip pass"  v-if="status == 2">审核通过</view>

            </view>
        </template>
        <template v-else-if="type == 'file' || type == 'image'  || type == 'pic' ">
            <view class="upload-file" >
                <view class="uf-tit">{{title}}<text>*</text></view>
                <view class="uf-tips">{{descText}}</view>

                <view class="adidci-doc-block" v-for="(item, index) in value" :key="index">
                    <view class="adidcidb-l">
                        <image src="@/images/icon/file-excel-fill.png" v-if="getFormatType(item.filename) == 'excel'"></image>
                        <image src="@/images/icon/file-word-fill.png" v-else-if="getFormatType(item.filename) == 'word'"></image>
                        <image src="@/images/icon/file-pdf-2-fill.png" v-else-if="getFormatType(item.filename) == 'pdf'"></image>
                        <image src="@/images/icon/file-ppt-fill.png" v-else-if="getFormatType(item.filename) == 'ppt'" mode=""></image>
                        <image src="@/images/icon/file-zip-fill.png" v-else-if="getFormatType(item.filename) == 'zip'" mode=""></image>
                        <image src="@/images/icon/file-video-fill-2.png" v-else-if="getFormatType(item.filename) == 'video'" mode=""></image>
                        <image src="@/images/icon/file-image-fill.png" v-else-if="getFormatType(item.filename) == 'image'" mode=""></image>
                        <view class="adidcidbl-txt">
                            <view class="adidcidblt-tit">{{item.filename}}</view>
                            <view class="adidcidblt-size">{{ getFormatType(item.filename) }} - {{ getSize(item) }} Mb</view>
                        </view>
                    </view>
                    <view class="adidcidb-r" @click="delFile(index)">
                        <image src="@/images/icon/close-line.png"></image>
                    </view>
                </view>
                <!-- 审核信息 -->
                <!-- <view class="uf-review-tip not-pass">审核不通过：文档不全</view> -->
<!--                <view class="uf-review-tip pass">审核通过</view>-->
                <view class="uf-review-tip not-pass" v-if="status == 3">审核不通过：{{reject_reason}}</view>
                <view class="uf-review-tip pass"  v-if="status == 2">审核通过</view>
                <view class="uf-upbtn" v-if="fileMax" @click="openFile">
                    <image src="@/images/icon/upload-2-line.png"></image>
                    <text>上传文件</text>
                </view>
            </view>
        </template>
    </template>
</template>

<script>
import {alert, getFormatType} from "@/lib/utils";
import api from "@/lib/api";
import File_img from "@/pages/services/form/file_img.vue";

export default {
    components: {File_img},
    props: {
        keyIndex: Number,
        status: Number,
        id: Number,
        title: String,
        reject_reason: String,
        desc: String,
        type: String,
        value: [String, Array],
        isRequired: Boolean,
        options: Object
    },
    data() {
        return {
            uploadForm: {},
            binds: {},
        }
    },
    computed: {
        fileMax() {
            if (!this.value) {
                return true
            }
            if (this.value?.length < this.options?.limit) {
                return true
            }
            return false;
        },
        selectIndex() {
            let index = this.options.options.findIndex(res => res == this.value)
            if (index >= 0) {
                return index
            }
            return ""
        },
        selectText() {
            let index = this.options.options.findIndex(res => res == this.value)
            if (index >= 0) {
                return this.options.options[index]
            }
            return "请选择"
        },
        descText() {
            if (this.desc) {
                return this.desc
            }
            let desc = ''
            let mb = ''
            if (this.type == 'file' || this.type == 'image') {
                if (this.options) {
                    mb = this.getSize(this.options)
                }

                if (this.type == 'image') {
                    desc += "支持图片,"
                } else {
                    if(this.options?.ext.find(res => res == '*')) {
                        desc += "文件类型不限,"
                    } else {
                        desc += "文件类型支持"
                        if (this.options?.ext.find(res => res == 'doc')) {
                            desc += "文档、"
                        }
                        if (this.options?.ext.find(res => res == 'archive')) {
                            desc += "压缩包、"
                        }
                        if (this.options?.ext.find(res => res == 'image')) {
                            desc += "图片、"
                        }
                        if (this.options?.ext.find(res => res == 'video')) {
                            desc += "视频、"
                        }
                    }
                }
                desc = desc.slice(0, -1)

                if (mb) {
                    desc +="，文件大小"+ mb +"M以内"
                }
            }
            return desc
        }
    },
    mounted() {
        if (this.value) {
            this.$emit('inputUpdate', {value: this.value, key: this.keyIndex})
        }
    },
    emits: ['update:value', 'inputUpdate', 'inputUpdateDelFiles',],
    methods: {
        inputValue(e) {
            this.$emit('inputUpdate', {value: e.detail.value, key: this.keyIndex})
        },
        inputType() {
            if (this.options?.mask) {
                return this.options?.mask
            }
            return "text"
        },
        inputMaxLen() {
            if (this.options?.maxlen) {
                return this.options.maxlen;
            }
            return -1;
        },
        inputDesc() {
            if (this.desc) {
                return this.desc
            }
            return "请输入"+this.title;
        },
        async openFile() {

            let itemList = ["从微信聊天选择", "从手机相册选择", "拍摄"]

            uni.showActionSheet({
                itemList: itemList,
                success: res => {
                    if (res.tapIndex == 0) {
                        wx.chooseMessageFile({
                            count: this.options?.limit, //默认100
                            type: 'all',
                            success: async res => {
                                await this.uploadAllFiles(res.tempFiles);
                            }
                        });
                    } else if (res.tapIndex == 1) {
                        let mediaType = ['image', 'video'];

                        uni.chooseMedia({
                            count: this.options?.limit,
                            sourceType: ['album'],
                            mediaType: mediaType,
                            success: async res => {
                                console.log(res)
                                await this.uploadAllFiles(res.tempFiles);
                            }
                        })
                    } else if (res.tapIndex == 2) {
                        let mediaType = ['image', 'video'];

                        uni.chooseMedia({
                            count: this.options?.limit,
                            sourceType: ['camera'],
                            mediaType: mediaType,
                            success: async res => {
                                console.log(res)
                                await this.uploadAllFiles(res.tempFiles);
                            }
                        })
                    }
                }
            })
        },
        getSize(data) {
            let kb = 0
            if (data?.filesize) {
                kb = data.filesize
            }
            if (data?.size) {
                kb = data?.size;
            }
            let mb = Math.floor(kb / 1024)
            console.log(mb)
            return mb
        },
        initData(){
            return new Promise((resolve, reject) => {
                let data = {
                    input_id: this.id
                }
                api.get('ers/orders/form/upload-config', data).then(res => {
                    this.uploadForm = res
                    resolve()
                }).catch(err => {
                    alert(err.message)
                    reject()
                })
            })
        },
        async uploadAllFiles(res) {
            for (const item of res) {
                await  this.initData().then(() => {
                    if (item?.path) {
                        this.uploadFile(item.path)
                    } else if (item?.tempFilePath) {
                        this.uploadFile(item.tempFilePath)
                    }
                }) // 等待每个文件上传完成
            }
        },
        groupChange(e) {
            this.$emit('inputUpdate', {value: e.detail.value, key: this.keyIndex})
        },
        checkboxChecked(data) {
            if (!this.value) {
                return false
            }
            let index = this.value.findIndex(res => res == data)
            console.log(index)
            if (index >= 0) {
                return true
            }
            return false
        },
        async uploadFile(path) {
            uni.showLoading({
                title: '上传中'
            });
            try {
                const uploadFileRes = await uni.uploadFile({
                    url: this.uploadForm.url,
                    filePath: path,
                    formData: this.uploadForm.form_params,
                    name: this.uploadForm.name
                });

                // 注意：这里返回的uploadFileRes.data 为JSON 需要自己去转换
                let data = JSON.parse(uploadFileRes.data);
                if (data?.key) {
                    let value = this.value ? [...this.value] : [];
                    value.push(data);
                    this.$emit('inputUpdate', { value: value, key: this.keyIndex });
                } else {
                    alert('上传失败');
                }
            } catch (error) {
                console.log(error);
                uni.showToast({
                    title: error.message || '上传失败',
                    duration: 2000
                });
            } finally {
                uni.hideLoading();
            }
        },
        bindPickerChange(e) {
            let value = e.detail.value
            this.$emit('inputUpdate', {value: this.options.options[value], key: this.keyIndex})
        },
        getFormatType(type) {
            return getFormatType(type);
        },
        delFile(index) {
            if (this.type == 'file' || this.type == 'image') {
                console.log(this.value)
                if (this.value[index].key) {
                    let value = [...this.value]
                    value.splice(index, 1)
                    this.$emit('inputUpdate', {value: value, key: this.keyIndex})
                } else {
                    this.$emit('inputUpdateDelFiles', {value: this.value[0].path, index: this.keyIndex})
                }
            } else {
                this.$emit('inputUpdate', {value: [], key: this.keyIndex})
            }
        }
    },
}
</script>

<style>
page {
    background-color: #f3f3ff;
}

.ssgb-tit {
    height: 80rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #999;
    padding: 0 30rpx;
}
.ss-group-box {
    margin-top: 30rpx;
}

/* 单选、多选 */
.dropdown-part {
    flex: 1;
    display: flex;
    align-items: center;
    height: 100rpx;
    justify-content: flex-end;
}
.dropdown-part text {
    padding-right: 20rpx;
    color: #999;
    font-size: 28rpx;
}
.dropdown-part image {
    width: 32rpx;
    height: 32rpx;
}
.single-cb {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 0 0 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}
.st-checkbox {
    flex: 1;
    padding-top: 20rpx;
}
.st-checkbox  checkbox {
    padding-right: 40rpx;
    padding-bottom: 20rpx;
}

/* 单行文本 */
.single-txt {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    background-color: #fff;
    padding: 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}
.st-input {
    flex: 1;
}
.st-input input {
    height: 100rpx;
    line-height: 100rpx;
}
.st-unit {
    font-size: 24rpx;
    color: #999;
}
.st-name {
    width: 200rpx;
}
.st-name text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}
.ssgb-cnt .single-txt {
    margin-bottom: 1px;
}
.ssgb-cnt .upload-file {
    margin-bottom: 1px;
}

/* 多行文本 */
.mtextarea {
    position: relative;
    margin-bottom: 30rpx;
}
.mt-tit {
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 30rpx;
    color: #999;
    font-size: 28rpx;
}
.mt-tit text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}
.mt-cnt {
    display: flex;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}
.mt-cnt textarea {
    flex: 1;
    padding: 30rpx;
    font-size: 28rpx;
}

/* 上传框 */
.upload-file {
    position: relative;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    padding-bottom: 1px;
    margin-bottom: 1rpx;
}
.uf-tit {
    font-size: 28rpx;
    padding: 30rpx 30rpx 0;
}
.uf-tit text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}
.uf-tips {
    font-size: 24rpx;
    color: #999;
    padding: 10rpx 30rpx 20rpx;
}
.uf-upbtn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    color: #390ABC;
}
.uf-upbtn image {
    width: 32rpx;
    height: 32rpx;
}
.uf-upbtn text {
    font-size: 28rpx;
    padding-left: 10rpx;
    font-weight: bold;
}

/* 上传后文件样式 */
.adidci-doc-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e7e7e7;
    border-radius: 12rpx;
    padding: 20rpx;
}
.adidcidbl-txt {
    padding-left: 20rpx;
}
.adidcidb-l {
    display: flex;
    align-items: center;
}
.adidcidb-l image {
    width: 64rpx;
    height: 64rpx;
}
.adidcidblt-tit {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300rpx;
    font-size: 28rpx;
}
.adidcidblt-size {
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}
.adidcidb-r image {
    width: 32rpx;
    height: 32rpx;
}

.upload-file .adidci-doc-block {
    margin: 0 30rpx 30rpx;
    background-color: #F3F3FF;
    border: none;
}

.uf-review-tip {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 24rpx;
    background-color: red;
    color: #fff;
    padding: 4rpx 8rpx;
}
.uf-review-tip.pass {
    background-color: #07c160;
}


/* 底部按钮 */
.sic-foot {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 15rpx 15rpx env(safe-area-inset-bottom);
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}
.sicf-btn {
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #390abc;
    color: #fff;
    border-radius: 12rpx;
}
.sicf-btn image {
    width: 36rpx;
    height: 36rpx;
}
.sicf-btn text {
    font-weight: bold;
}
.sic-foot-eb {
    height: 100rpx;
    padding-bottom: env(safe-area-inset-bottom);
}
</style>