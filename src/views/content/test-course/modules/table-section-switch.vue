<script setup lang="tsx">
import { ref } from 'vue';
import { operateCourseSub } from "@/service/api";

interface Props {
  rowData: Api.Cms.ChapterSection;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'reload'): void;
}

const emit = defineEmits<Emits>();

const active = defineModel<boolean>('active', {
  default: false
});

const loading = ref<boolean>(false);

async function handleUpdateValue(value: boolean) {
  loading.value = true

  const { error } = await operateCourseSub({
    course_id: props.rowData.course_id,
    type: props.rowData.type,
    resource_id: props.rowData.id,
    operate: value ? 'open' : 'close'
  });

  loading.value = false;

  if (error) {
    return;
  }

  emit('reload');
}
</script>

<template>
  <NSwitch
    :rubber-band="false"
    :value="active"
    :loading="loading"
    @update:value="handleUpdateValue"
  />
</template>

<style scoped></style>
