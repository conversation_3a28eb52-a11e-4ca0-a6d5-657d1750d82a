<template>
    <view class="content">

        <view class="photo-verification" v-if="!isSuccess && orgSid" >
            <view class="pv-tips">拍摄您本人人脸，请确保正对手机，光线充足</view>
            <view class="pv-cnt" v-if="imageSrc">
                <image :src="imageSrc" mode="widthFix"></image>
            </view>
            <view class="pv-cnt" v-else>
                <image :src="homeUrl+'/static/images/phone_ver.png'" mode="widthFix"></image>
            </view>
            <view class="pv-foot" v-if="!imageSrc" @click="chooseImage">
                <button>采集本人人脸</button>
            </view>
            <view class="pv-foot" v-else @click="submitPhotos">
                <button>上传图片</button>
            </view>
            <view class="pv-foot lom" v-if="imageSrc" @click="chooseImage">
                <button>重新采集人脸</button>
            </view>
        </view>

        <!-- 采集成功 -->
        <view class="photo-verification" v-if="isPc != 0 && isSuccess">
            <view class="pv-sucess">
                <image src="/src/images/icon/sucess-200.png"></image>
            </view>
            <view class="pv-yes">采集成功</view>
        </view>


        <!--
        <button @click="chooseImage">选择图片</button>
        <button @click="submitPhotos">上传图片</button>
        <view v-if="imageSrc">
            <image :src="imageSrc" mode="aspectFit"></image>
        </view>
        -->
    </view>

</template>

<script>
import api from "@/lib/api";
import {alert, showDelayLoading} from "@/lib/utils";
import { homeUrl } from '@/config';

export default {
    data() {
        return {
            isSuccess: false,
            scene: '',
            orgSid: '',
            imageSrc: '',
            isPc: true,
            loading: false,
            homeUrl
        };
    },
    onLoad(e) {
        console.log(e)
        if (e.scene) {
            this.scene = e.scene;
        }

        if (e.is_pc) {
            this.isPc = e.is_pc;
        }
        this.getOrg()
    },
    methods: {
        chooseImage() {
            uni.chooseMedia({
                count: 1, // 默认9
                mediaType: 'image',
                sizeType: ['original'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['camera'], // 可以指定来源是相册还是相机，默认二者都有
                camera: 'front',
                success: (res) => {
                    console.log(res);
                    this.imageSrc = res.tempFiles[0].tempFilePath;
                },
				fail: err => {
					console.log(err);
				}
            });
        },
        getOrg() {
            api.get(`learn-captures/${this.scene}/org`).then(res => {
                this.orgSid = res.sid
            }).catch(err => {
                alert(err.message);
            })
        },
        submitPhotos() {
            if (!this.imageSrc) {
                uni.showToast({
                    title: '请先上传图片',
                    icon: 'none'
                });
                return;
            }

            if (this.loading) {
                return;
            }
            this.loading = true


            api.get(`orgs/${this.orgSid}/upload-config`, {type: 'image'}).then(res => {
                uni.uploadFile({
                    url: res.url,
                    filePath: this.imageSrc,
                    name: 'file',
                    formData: res.form_params,
                    success: (uploadRes) => {

                        let data = JSON.parse(uploadRes.data);
                        const hideLoading = showDelayLoading("上传中", 200)
                        api.post("learn-captures/" + this.scene,{photo: data.key}).then(res => {
                            uni.$emit('photos-pic-update')

                            if (this.isPc == 0) {
                                uni.navigateBack({
                                    delta: 1
                                })
                            } else {
                                this.isSuccess = true
                                uni.showToast({
                                    title: '上传成功',
                                    icon: 'success'
                                })
                            }

                        }).catch(err => {
                            alert(err.message);
                        }).finally(() => {
                            hideLoading()
                            this.loading = false
                        })
                    },
                    fail: (err) => {
                        console.error(err);

                        uni.showModal({
                            title: '上传失败',
                            content: err.error
                        });
                    }
                });
            }).catch(err => {

            }).finally(() => {
                this.loading = false
            })

        }
    }

}
</script>

<style scoped>
    .photo-verification {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 80rpx;
    }
    .pv-tips {
        font-size: 28rpx;
    }
    .pv-cnt {
        padding: 100rpx 0;
    }
    .pv-cnt image {
        width: 400rpx;
    }

    .pv-foot button {
        width: 500rpx;
        height: 100rpx;
        text-align: center;
        line-height: 100rpx;
        font-size: 32rpx;
        font-weight: bold;
        border-radius: 12rpx;
        margin-bottom: 40rpx;
        background-color: #390ABC;
        color: #FFF;
    }
    .pv-sucess image {
        width: 160rpx;
        height: 160rpx;
    }
    .pv-yes {
        font-weight: bold;
        padding-top: 40rpx;
        color: #12B45F;
    }
    .pv-foot.lom button {
        background-color: #fff;
        border: 1px solid #e7e7e7;
        color: #333333;
    }
</style>
