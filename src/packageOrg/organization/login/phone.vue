<template>
    <view class="content">
        <view class="login-center-box">
            <view class="lcb-logo" v-if="orgData">
                <image v-if="orgData.logo_url" :src="orgData.logo_url"></image>
                <view class="logo-txt">{{orgData.alias}}</view>
            </view>

            <view class="phone-login-box">
                <view class="plb-tips">未注册手机号将自动为您创建账号</view>
                <view class="login-input-part">
                    <input class="uni-input" placeholder="请输入手机号" v-model="phone" />
                    <GetPhoneCode v-model="code" :phone="phone" type="login" />
                    <view class="lip-btn" @click="postLogin">立即登录</view>
                </view>
            </view>

            <Agreement ref="agreement" @confirm-agree="postLogin" />
        </view>
    </view>
</template>

<script>
import api from '@/lib/api.js';
import { useUserStore } from '@/store/user.js';
import {mapActions, mapState} from 'pinia';
import Agreement from '@/components/agreement.vue';
import GetPhoneCode from '@/components/get-phone-code.vue';
import { loginReturn } from '@/lib/login.js';
import {getAppName, getReferral} from '@/lib/context.js';
import { getDeviceInfo } from '@/lib/utils'

export default {
    components: {
        Agreement,
        GetPhoneCode
    },
    data() {
        return {
            id_number: "",
            phone: "",
            password: "",
            code: "",
            orgId: "",
            agreed: false,
            appName: getAppName(),
            type: 'phone'
        }
    },
    onLoad(e) {
        if (uni.getStorageSync("org_id")) {
            this.orgId = uni.getStorageSync("org_id")
        }
        if (e.type) {
            this.type = e.type
            console.log(this.type)
        }
    },
    computed: {
        ...mapState(useUserStore, ['orgData']),
    },
    methods: {
        ...mapActions(useUserStore, ["login"]),
        postLogin() {
            if (!this.phone || !this.code) {
                uni.showModal({
                    title: "登录失败",
                    content: "请输入手机号和验证码",
                    showCancel: false
                });
                return;
            }
            if (!this.$refs.agreement.check()) {
                return;
            }

            uni.showLoading({
                title: "登录中",
                mask: true
            });

            const deviceInfo = getDeviceInfo()

            api.post("login/phone-code", {
                phone: this.phone,
                code: this.code,
                referral: getReferral(),
                platform: deviceInfo.platform,
                system: deviceInfo.system,
                org_sid: this.orgId,
            }).then(res => {
                this.login(res);
                uni.showToast({
                    title: "登录成功",
                    showCancel: false,
                    success: () => setTimeout(() => {
                        uni.reLaunch({
                            url: '/packageOrg/organization/home'
                        })
                    }, 1000)
                });
            }, e => {
                uni.hideLoading();
                uni.showModal({
                    title: "登录失败",
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                uni.hideLoading();
            });
        },
        agree(e) {
            this.agreed = e.detail.value.includes("agree");
        },
    }
}
</script>

<style>
    page {
        width: 100%;
        height: 100%;
        background-color: #F3F3FF;
    }
    .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }
    .phone-login-box {
        width: 540rpx;
    }
    .login-center-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .lcb-logo {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-bottom: 80rpx;
    }
    .lcb-logo image {
        width: 120rpx;
        height: 120rpx;
        vertical-align: top;
    }
    .logo-txt {
        padding-top: 20rpx;
        font-size: 36rpx;
        text-align: center;
        font-weight: bold;
        color: #333;
    }
    .lcb-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .lcb-btn.wechat {
        background-color: #07C160;
    }
    .lcb-btn.wechat text {
        color: #fff;
    }
    .lcb-btn image {
        width: 42rpx;
        height: 42rpx;
    }
    .lcb-btn text {
        padding-left: 20rpx;
    }
    .lcbf-txt navigator {
        display: inline-block;
        color: #390ABC;
        text-decoration: underline;
        padding: 0 8rpx;
    }
    .plb-tips {
        font-size: 24rpx;
        color: #999;
        padding: 0 0 30rpx;
        text-align: center;
    }

    .login-input-part .uni-input {
        height: 100rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        margin-bottom: 20rpx;
    }
    .lip-btn {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        background-color: #390ABC;
        color: #fff;
        margin-top: 60rpx;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
</style>