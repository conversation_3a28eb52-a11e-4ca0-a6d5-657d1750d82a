<template>
    <view class="content">
        <view class="login-center-box">
            <view class="lcb-logo" v-if="orgData">
                <image v-if="orgData.logo_url" :src="orgData.logo_url"></image>
                <view class="logo-txt">{{orgData.alias}}</view>
            </view>

            <navigator url="/packageOrg/organization/login/phone?type=phone" class="lcb-btn">
                <image src="../../../images/icon/smartphone-line.png"></image>
                <text>手机快捷登录</text>
            </navigator>

            <navigator url="/packageOrg/organization/login/id?type=card" class="lcb-btn">
                <image src="../../../images/icon/id-card-line.png"></image>
                <text>身份证号登录</text>
            </navigator>

            <view class="lcb-btn wechat" @click="wechatLogin">
                <image src="../../../images/icon/wechat-2-fill.png"></image>
                <text>微信一键登录</text>
            </view>

            <Agreement ref="agreement" @confirm-agree="wechatLogin" />
        </view>
    </view>
</template>

<script>
import Agreement from '@/components/agreement.vue';
import api from '@/lib/api.js';
import {getAppName, getReferral} from '@/lib/context.js';
import { loginReturn } from '@/lib/login.js';
import {alert, getDeviceInfo} from '@/lib/utils.js';
import { useUserStore } from '@/store/user.js';
import {mapActions, mapState} from 'pinia';

export default {
    data() {
        return {
            appName: getAppName(),
            orgId: ''
        }
    },
    components: {
        Agreement,
    },
    computed: {
        ...mapState(useUserStore, ['orgData']),
    },
    onLoad(e) {
        if (e.org_sid) {
            this.orgId = e.org_sid
            uni.setStorageSync("org_id", e.org_sid)
        }
        if (uni.getStorageSync("org_id")) {
            this.orgId = uni.getStorageSync("org_id")
        }
    },
    methods: {
        ...mapActions(useUserStore, ["login"]),
        wechatLogin() {
            if (!this.$refs.agreement.check()) {
                return;
            }

            uni.showLoading({
                title: "登录中",
                mask: true
            });

            uni.getProvider({
                service: "oauth",
                success: p => {
                    if (p.provider.includes("weixin")) {
                        //微信小程序授权登录
                        uni.login({
                            provider: "weixin",
                            success: res => {
                                const deviceInfo = getDeviceInfo()
                                let data = {
                                    code: res.code,
                                    referral: getReferral(),
                                    platform: deviceInfo.platform,
                                    system: deviceInfo.system,
                                    org_sid: this.orgId,
                                }
                                api.request("login/wechat-mini-app", {method: "POST", data}).then(res => {
                                    this.login(res.data);
                                    uni.hideLoading();
                                    uni.showToast({
                                        title: "登录成功",
                                        showCancel: false,
                                        success: () => setTimeout(() => {
                                            // 确保传递orgId参数
                                            const url = `/packageOrg/organization/home?org_sid=${this.orgId}`;
                                            uni.reLaunch({
                                                url: url
                                            })
                                        }, 1000)
                                    });
                                }, e => {
                                    uni.hideLoading();

                                    if (e.code == 404) {
                                        const bindCode = e.header("X-Bind-Code");
                                        uni.showToast({
                                            title: e.message,
                                            showCancel: false,
                                            success: () => {
                                                uni.navigateTo({
                                                    url: "/pages/login/bind?code=" + bindCode
                                                });
                                            }
                                        });
                                    } else {
                                        uni.showModal({
                                            title: "登录失败",
                                            content: e.message,
                                            showCancel: false
                                        });
                                    }
                                });
                            },
                            fail: e => {
                                uni.hideLoading();
                                alert(e.errMsg);
                            }
                        });
                    } else {
                        //没有可用的快捷登录
                        uni.hideLoading();
                        alert("不支持的登录方式");
                    }
                },
                fail: e => {
                    uni.hideLoading();
                    alert(e.errMsg);
                }
            });
        }
    }
};
</script>

<style>
    page {
        width: 100%;
        height: 100%;
        background-color: #F3F3FF;
    }
    .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }
    .login-center-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .lcb-logo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-bottom: 120rpx;
    }
    .lcb-logo image {
        width: 120rpx;
        height: 120rpx;
        vertical-align: top;
    }
    .logo-txt {
        padding-top: 20rpx;
        font-size: 36rpx;
        text-align: center;
        font-weight: bold;
        color: #333;
    }
    .lcb-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        width: 540rpx;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .lcb-btn.wechat {
        background-color: #07C160;
    }
    .lcb-btn.wechat text {
        color: #fff;
    }
    .lcb-btn image {
        width: 42rpx;
        height: 42rpx;
    }
    .lcb-btn text {
        padding-left: 20rpx;
    }

</style>