<template>
    <view class="content">
        <view class="video-list-box">
            <template v-for="(item, index) in list" :key="index">
                <view class="vlb-pane" v-if="item.course"  @click="">
                    <view  class="video-item">
                        <image :src="item.course.content.cover_src" mode="widthFix"></image>
                        <view class="vi-info">
                            <view class="vii-tit">{{item.course.course_name}}</view>
                            <view class="vii-data">
                                {{ item.course.study_progress.study_hour }}<text> / {{ item.course.hour }}学时</text>
                            </view>

                            <view class="vii-report" @click="goWeb(item.course)">
                                <text>学习记录</text>
                                <image src="../../../images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="vlb-pane" v-if="item.course_pack" >
                    <view  class="video-item">
                        <image :src="item.course_pack.content.cover_src" mode="widthFix"></image>
                        <view class="vi-info">
                            <view class="vii-tit">{{item.course_pack.content.title}}</view>
                            <view class="vii-data">
                                {{ item.course_pack.study_progress.study_hour }}<text> / {{ item.course_pack.hour }}学时</text>
                            </view>

                            <view class="vii-report" @click.stop="goWeb(item.course_pack)">
                                <text>学习记录</text>
                                <image src="../../../images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </view>
                    </view>
                </view>
            </template>
        </view>

        <view class="no-data-nomal-box" v-if="!loading && !list.length">
            <view class="ndnb-icon">
                <image src="../../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无数据</text>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {showDelayLoading, alert, getDetailUrl} from "@/lib/utils";

export default {
    data() {
        return {
            list: [],
            loading: true,
            orgId: '',
        }
    },
    onLoad(query) {
        if (uni.getStorageSync("org_id")) {
            this.orgId = uni.getStorageSync("org_id")
        }
        this.initData()
    },
    methods: {
        initData(){
            let loading = showDelayLoading("加载中", 200)
            api.get(`orgs/${this.orgId}/my-study`).then(res => {
                this.list = res.trains
                loading()
            }).catch(err => {
                loading()
                alert(err.message)
            }).finally(() =>{
                this.loading = false
            })
        },
        clickCourseData(data){
            let url = `/pages/training/video/detail?sid=${data.sid}`
            uni.navigateTo({
                url
            })
        },
        goWeb(data) {
            console.log(data)
            let url = encodeURIComponent(data.study_record_url)
            uni.navigateTo({
                url: `/pages/index/webview?url=${url}&title=学时报告`
            })
        },
        goPackage(data){
            uni.navigateTo({
                url: `/pages/training/video/course-package?sid=${data.course_pack.content.sid}&org_sid=${this.orgId}&enroll_sid=${data.enroll_sid}`
            })
        }
    },
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .vlb-tab {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        display: flex;
        align-items: center;
        background-color: #F3F3FF;
        padding: 0 30rpx;
    }
    .vlbt-eb {
        height: 100rpx;
    }

    .vlbt-item {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 40rpx;
        color: #999;
        font-size: 32rpx;
    }
    .vlbt-item.cur {
        position: relative;
        font-weight: bold;
        color: #333;
    }
    .vlbt-item.cur::after {
        content: '';
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 20rpx;
        width: 20rpx;
        height: 8rpx;
        border-radius: 8rpx;
        background-color: #390abc;
    }
    .video-item {
        display: flex;
        align-items: flex-start;
        background: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .video-item image {
        width: 200rpx;
        height: 200rpx;
        border-radius: 12rpx;
    }
    .vi-info {
        flex: 1;
        padding-left: 20rpx;
    }
    .vii-tit {
        line-height: 40rpx;
        font-weight: bold;
        font-size: 28rpx;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    .vii-teacher {
        padding: 10rpx 0;
        font-size: 28rpx;
        color: #999;
    }
    .vii-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24rpx;
        padding-top: 8rpx;
    }
    .viif-l {
        color: #999;
    }
    .viif-r {
        color: red;
        font-weight: bold;
    }
    .vii-data {
        font-size: 48rpx;
        font-weight: bold;
        padding: 10rpx 0 16rpx;
    }
    .vii-data text {
        font-size: 24rpx;
        font-weight: normal;
    }
    .vii-report {
        display: flex;
        align-items: center;
        height: 70rpx;
        width: 200rpx;
        border: 1px solid #e7e7e7;
        border-radius: 12rpx;
        justify-content: center;
    }
    .vii-report image {
        width: 32rpx;
        height: 32rpx;
    }
    .vii-report text {
        padding-right: 10rpx;
        font-size: 24rpx;
    }


    /* 已过期样式 */
    .video-item.timeout {
        position: relative;
    }
    .timeout-tips {
        display: none;
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        background-color: #999999;
        color: #FFFFFF;
        padding: 10rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
    }
    .video-item.timeout .timeout-tips {
        display: inline-block;
    }
    .video-item.timeout .vii-tit {
        color: #999;
    }
</style>