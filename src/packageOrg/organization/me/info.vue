<template>
    <view class="content">
        <view class="apply-form-box">
            <view class="single-txt">
                <view class="st-name">姓名
                </view>
                <view class="st-input">
                    <input class="uni-input" disabled :value="form.name" placeholder="请输入姓名"/>
                </view>
            </view>
        </view>
        <view class="apply-form-box">
            <view class="single-txt">
                <view class="st-name">手机号码
                </view>
                <view class="st-input">
                    <input class="uni-input" disabled :value="hideSensitiveInfo(form.phone)" placeholder="请输入手机号码"/>
                </view>
            </view>
        </view>
        <view class="apply-form-box">
            <view class="single-txt">
                <view class="st-name">身份证号码
                </view>
                <view class="st-input">
                    <input class="uni-input" disabled :value="hideSensitiveInfo(form.id_card_number, 'idCard')" placeholder="请输入身份证号码"/>
<!--                    <input class="uni-input" disabled v-model="form.id_card_number" placeholder=""/>-->

                </view>
            </view>
        </view>
        <view v-for="(input, index) in form.extra" :key="index">
            <InputDetail
                :keyIndex="index"
                :orgSid="orgSid"
                :title="input.name"
                :placeholder="input.placeholder"
                :desc="input.desc"
                :type="input.type"
                :status="1"
                :id="input.id"
                :reject_reason="input.reject_reason"
                v-model:value="input.value"
                :isRequired="input.required"
                :options="input.config"
                @inputUpdate="inputUpdate"
                @inputUpdateDelFiles="inputUpdateDelFiles"
            ></InputDetail>
        </view>

        <view class="sic-foot-eb"></view>
        <view class="sic-foot">
            <button class="sicf-btn"  @click="saveData">保存数据</button>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert, alertOrg, getFormatType, showDelayLoading} from "@/lib/utils";
import InputDetail from "@/components/form/InputDetailOrg.vue";
import dayjs from "@/uni_modules/uv-ui-tools/libs/util/dayjs";

export default {
    components: {InputDetail},
    data() {
        return {
            orgSid: '',
            form: {
                "type": "",
                "name": "",
                "phone": "",
                "photo": "",
                "id_card_front_url": "",
                "id_card_back_url": "",
                "photo_url": "",
                "id_card_number": "",
                "extra": [],
            },
            showFrom: false,
        }
    },
    onLoad() {
        this.orgSid = uni.getStorageSync("org_id")
        this.getEnrollments()
    },
    methods: {
        isTypeInput(name) {
            console.log(name)
            let arr =  ['select', 'text', 'region' , 'date', 'checkbox', 'radio']
            return  arr.find(res => res == name);
        },
        getEnrollments() {
            api.get(`orgs/${this.orgSid}/my-enrollment`).then(res => {
                res.extra.forEach(item => {
                    let data = item
                    if (data.type == 'photo') {
                        data.value = {
                            key: res.photo,
                            value: res.photo_url,
                        }
                    }
                    if (data?.type == 'region') {
                        let displayText = data.display_text.split(" ")
                        let area = {
                            province: displayText[0],
                            city: displayText[1],
                            district: null,
                            district_code: data.value,
                        }
                        if (displayText[2]) {
                            area.district = displayText[2]
                        }
                        data.value = area
                    }

                    if (data && (item.type == 'pic' || item.type == 'file')) {
                        console.log(data.value)
                        if (data.value instanceof Array) {
                            data.value.forEach(item => {
                                item.id = item.name
                                item.url = item.path_url
                            })
                        }
                    }
                })
                console.log(res.extra)
                this.form = res
            }).catch(err => {
                alert(err.message)
            })
        },
        getFormatType(type) {
            return getFormatType(type);
        },
        hideSensitiveInfo(info, type = 'phone') {
            if (type === 'phone') {
                // 隐藏手机号码中间4位
                if (info.length === 11) {
                    return info.slice(0, 3) + '****' + info.slice(7);
                }
            } else if (type === 'idCard') {
                // 隐藏身份证号码中间8位（保留前6位和后4位）
                if (info.length === 18) {
                    return info.slice(0, 6) + '********' + info.slice(14);
                }
            }
            // 如果不满足条件，返回原信息或提示错误
            return info;
        },
        inputUpdate(e) {
            console.log(e)
            this.form.extra[e.key].value = e.value
            this.form.extra[e.key].isEdit = true
        },
        inputUpdateDelFiles(e) {
            console.log(e)
            if (this.form.extra[e.index].delFile) {
                this.form.extra[e.index].delFile.push(e.value+",delete")
            } else {
                this.form.extra[e.index].delFile = [e.value+",delete"]
            }
            let delIndex  = this.form.extra[e.index].value.findIndex(res => res.path == e.value)
            this.form.extra[e.index].value.splice(delIndex, 1)
            this.form.extra[e.index].isEdit = true
            console.log(this.form.extra[e.index].delFile )
        },
        saveData() {
            const extras = this.form.extra.reduce((acc, field) => {
                if (field.type === "date" && field.value) {
                    acc[field.id] = field.value;
                } else if (field.type === "pic" || field.type === "file") {
                    let value = []
                    if (field.delFile) {
                        value.push(...field.delFile)
                    }
                    if (field.value) {
                        field.value.forEach(item => {
                            if (item.key) {
                                value.push(item.key)
                            }
                        })
                    }
                    if (value.length) {
                        acc[field.id] = value
                    }
                } else if (field.type === "photo") {
                    acc[field.id] = field.value.key;
                } else if (field.type === "sign") {
                    console.log(field)
                    try {
                        const fs = uni.getFileSystemManager();
                        const fileData = fs.readFileSync(field.value, 'base64');
                        acc[field.id] = `data:image/png;base64,${fileData}`;
                    } catch (error) {
                        console.error('读取文件失败:', error);
                    }
                } else if (field.type === "region") {
                    let area_text = [field.value.province]
                    if (field.value.city) {
                        area_text.push(field.value.city)
                    }
                    if (field.value.district) {
                        area_text.push(field.value.district)
                    }
                    acc[field.id] = { area_text: area_text, area_code: field.value.district_code};
                } else {
                    if (field.type === "region") {
                        if (field.value.indexOf("qiniu-priv") > -1) {
                            acc[field.id] = field.value
                        }
                    } else {
                        acc[field.id] = field.value;
                    }
                }

                return acc;
            }, {});
            console.log('extras', JSON.stringify(extras))
            const hideLoading = showDelayLoading("保存中", 200)
            api.put(`orgs/${this.orgSid}/enrollments`, {extras: extras}).then(res => {
                hideLoading()
                uni.showToast({
                    title: '保存成功',
                    icon: 'none'
                })
                this.getEnrollments()
                
                // 保存成功后返回原页面
                const pages = getCurrentPages()
                if (pages.length > 1) {
                    const prevPage = pages[pages.length - 2]
                    if (prevPage.route.includes('training/video/detail')) {
                        prevPage.$vm.hasPhoto = true
                        uni.navigateBack()
                    }
                }
            }).catch(err => {
                hideLoading()
                alertOrg(err.message)
            }).finally(() => {

            });
        },
        previewImage(data) {
            if (getFormatType(data.filename) == 'image') {
                uni.previewImage({
                    urls: [data.path_url],
                    current: data.path_url,
                    success: () => {
                    },
                    fail: (e) => {
                        console.log(e);
                    }
                })
            }
        },
        filePhoto() {

        }
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    page {
        background-color: #f3f3ff;
    }

    .content {
        padding: 30rpx 0;
    }

    .ssgb-tit {
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        color: #999;
        padding: 0 30rpx;
    }

    .ss-group-box {
        margin-top: 30rpx;
    }

    /* 单选、多选 */
    .dropdown-part {
        flex: 1;
        display: flex;
        align-items: center;
        height: 100rpx;
        justify-content: flex-end;
    }

    .dropdown-part text {
        padding-right: 20rpx;
        color: #999;
        font-size: 28rpx;
    }

    .dropdown-part image {
        width: 32rpx;
        height: 32rpx;
    }

    .single-cb {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        padding: 0 0 0 30rpx;
        font-size: 28rpx;
        margin-bottom: 1rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }

    .st-checkbox {
        flex: 1;
        padding-top: 20rpx;
    }

    .st-checkbox checkbox {
        padding-right: 40rpx;
        padding-bottom: 20rpx;
    }

    /* 单行文本 */
    .single-txt {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        background-color: #fff;
        padding: 0 30rpx;
        font-size: 28rpx;
        margin-bottom: 1rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }

    .st-input {
        flex: 1;
    }

    .st-input input {
        height: 100rpx;
        line-height: 100rpx;
        text-align: right;
    }
    .st-input image {
        height: 100rpx;
        width: 100rpx;
        line-height: 100rpx;
        text-align: right;
    }

    .st-unit {
        font-size: 24rpx;
        color: #999;
    }

    .st-name {
        width: 200rpx;
    }

    .st-name text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }

    .ssgb-cnt .single-txt {
        margin-bottom: 1px;
    }

    .ssgb-cnt .upload-file {
        margin-bottom: 1px;
    }

    /* 开放单选 */
    .radio-open {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        background-color: #fff;
        padding: 0 30rpx;
        font-size: 28rpx;
        margin-bottom: 1rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }

    .ro-part {
        flex: 1;
    }

    .ro-part radio-group {
        display: flex;
        align-items: center;
    }

    .ro-part label {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .ro-part label text {
        padding-left: 8rpx;
    }

    .ro-name {
        width: 200rpx;
    }

    .ro-name text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }

    /* 折叠多选 */
    .checkbox-fold {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        background-color: #fff;
        padding: 0 30rpx;
        font-size: 28rpx;
        margin-bottom: 1rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }

    .cf-box {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .cf-box image {
        width: 32rpx;
        height: 32rpx;
    }

    .cf-box text {
        padding-right: 10rpx;
        max-width: 400rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #999;
    }

    .cf-name {
        width: 200rpx;
    }

    .cf-name text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }

    /* 折叠多选弹窗 */
    .checkbox-popup-box {
        background-color: #F3F3FF;
        border-radius: 12rpx 12rpx 0 0;
        padding-bottom: 80rpx;
    }

    .cpb-tit {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        font-weight: bold;
        border-bottom: 1px solid #F3F3FF;
    }

    .cpbl-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        padding: 0 20rpx;
        border-bottom: 1px solid #F3F3FF;
        background-color: #fff;
    }

    .cpbli-txt {
        font-size: 28rpx;
    }

    .cpb-foot {
        padding: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .cpbf-btn {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        font-size: 28rpx;
    }

    .cpbf-submit {
        flex: 1;
        background-color: #390ABC;
        color: #fff;
        border-radius: 12rpx;
        font-weight: bold;
    }

    .cpbf-cancle {
        width: 200rpx;
        margin-right: 20rpx;
        background-color: #fff;
        border-radius: 12rpx;
    }

    /* 多行文本 */
    .mtextarea {
        position: relative;
        margin-bottom: 30rpx;
    }

    .mt-tit {
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 30rpx;
        color: #999;
        font-size: 28rpx;
    }

    .mt-tit.has-description {
        height: auto;
        line-height: 1;
        padding: 40rpx 30rpx 10rpx;
    }

    .mt-description {
        font-size: 24rpx;
        color: #999;
        padding: 10rpx 30rpx 20rpx;
    }

    .mt-tit text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }

    .mt-cnt {
        display: flex;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }

    .mt-cnt textarea {
        flex: 1;
        padding: 30rpx;
        font-size: 28rpx;
    }

    /* 上传框 */
    .upload-file {
        position: relative;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding-bottom: 1px;
        margin-bottom: 1rpx;
    }

    .uf-tit {
        font-size: 28rpx;
        padding: 30rpx 30rpx 0;
    }

    .uf-tit text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }

    .uf-tips {
        font-size: 24rpx;
        color: #999;
        padding: 10rpx 30rpx 20rpx;
    }

    .uf-upbtn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        color: #390ABC;
    }

    .uf-upbtn image {
        width: 32rpx;
        height: 32rpx;
    }

    .uf-upbtn text {
        font-size: 28rpx;
        padding-left: 10rpx;
        font-weight: bold;
    }

    /* 上传后文件样式 */
    .adidci-doc-block {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #e7e7e7;
        border-radius: 12rpx;
        padding: 20rpx;
    }

    .adidcidbl-txt {
        padding-left: 20rpx;
    }

    .adidcidb-l {
        display: flex;
        align-items: center;
    }

    .adidcidb-l image {
        width: 64rpx;
        height: 64rpx;
    }

    .adidcidblt-tit {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300rpx;
        font-size: 28rpx;
    }

    .adidcidblt-size {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }

    .adidcidb-r image {
        width: 32rpx;
        height: 32rpx;
    }

    .upload-file .adidci-doc-block {
        margin: 0 30rpx 30rpx;
        background-color: #F3F3FF;
        border: none;
    }

    .uf-review-tip {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 24rpx;
        background-color: red;
        color: #fff;
        padding: 4rpx 8rpx;
    }

    .uf-review-tip.pass {
        background-color: #07c160;
    }

    /* 单选展示出来 */


    /* 底部按钮 */
    .sic-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 15rpx 15rpx env(safe-area-inset-bottom);
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        z-index: 10;
    }

    .sicf-btn {
        height: 90rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390abc;
        color: #fff;
        border-radius: 12rpx;
    }

    .sicf-btn image {
        width: 36rpx;
        height: 36rpx;
    }

    .sicf-btn text {
        font-weight: bold;
    }

    .sic-foot-eb {
        height: 100rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .cpb-list {
        max-height: 750rpx;
        overflow-y: scroll;
    }



</style>