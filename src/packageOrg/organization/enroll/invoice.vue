<template>
    <view class="content">
        <!-- 页面标题 -->
        <view class="page-header">
            <view class="header-title">申请开发票</view>
            <view class="header-subtitle">{{ enrollTitle }}</view>
        </view>

        <!-- 订单信息 -->
        <view class="order-info">
            <view class="section-title">订单信息</view>
            <view class="info-card">
                <view class="info-item">
                    <text class="info-label">订单金额：</text>
                    <text class="info-value">¥{{ enrollAmount }}</text>
                </view>
                <view class="info-item">
                    <text class="info-label">报名项目：</text>
                    <text class="info-value">{{ enrollTitle }}</text>
                </view>
            </view>
        </view>

        <!-- 发票信息表单 -->
        <view class="invoice-form">
            <view class="section-title">发票信息</view>
            <view class="form-card">
                <!-- 发票类型 -->
                <view class="form-item">
                    <view class="form-label">发票类型 <text class="required">*</text></view>
                    <view class="radio-group">
                        <label class="radio-item" v-for="type in invoiceTypes" :key="type.value">
                            <radio :value="type.value" :checked="invoiceForm.type === type.value" @change="onInvoiceTypeChange" />
                            <text>{{ type.label }}</text>
                        </label>
                    </view>
                </view>

                <!-- 发票抬头 -->
                <view class="form-item">
                    <view class="form-label">发票抬头 <text class="required">*</text></view>
                    <input 
                        class="form-input" 
                        v-model="invoiceForm.title" 
                        placeholder="请输入发票抬头"
                        maxlength="100"
                    />
                </view>

                <!-- 纳税人识别号（企业发票时显示） -->
                <view class="form-item" v-if="invoiceForm.type === 'company'">
                    <view class="form-label">纳税人识别号 <text class="required">*</text></view>
                    <input 
                        class="form-input" 
                        v-model="invoiceForm.taxNumber" 
                        placeholder="请输入纳税人识别号"
                        maxlength="20"
                    />
                </view>

                <!-- 联系邮箱 -->
                <view class="form-item">
                    <view class="form-label">联系邮箱 <text class="required">*</text></view>
                    <input 
                        class="form-input" 
                        v-model="invoiceForm.email" 
                        placeholder="请输入接收发票的邮箱"
                        type="email"
                        maxlength="50"
                    />
                </view>

                <!-- 联系电话 -->
                <view class="form-item">
                    <view class="form-label">联系电话</view>
                    <input 
                        class="form-input" 
                        v-model="invoiceForm.phone" 
                        placeholder="请输入联系电话"
                        type="number"
                        maxlength="11"
                    />
                </view>

                <!-- 备注 -->
                <view class="form-item">
                    <view class="form-label">备注</view>
                    <textarea 
                        class="form-textarea" 
                        v-model="invoiceForm.remark" 
                        placeholder="请输入备注信息（选填）"
                        maxlength="200"
                    ></textarea>
                </view>
            </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
            <button class="submit-btn" @click="submitInvoice" :disabled="submitting">
                {{ submitting ? '提交中...' : '提交申请' }}
            </button>
        </view>
    </view>
</template>

<script>
import api from '@/lib/api';
import urls from '@/lib/urls';
import { alert } from '@/lib/utils';

export default {
    data() {
        return {
            enrollId: '',
            enrollTitle: '',
            enrollAmount: '',
            submitting: false,
            // 发票类型选项
            invoiceTypes: [
                { value: 'personal', label: '个人发票' },
                { value: 'company', label: '企业发票' }
            ],
            // 发票表单数据
            invoiceForm: {
                type: 'personal',
                title: '',
                taxNumber: '',
                email: '',
                phone: '',
                remark: ''
            }
        };
    },
    onLoad(options) {
        console.log('开发票页面参数:', options);
        
        this.enrollId = options.enrollId || '';
        this.enrollTitle = decodeURIComponent(options.title || '');
        this.enrollAmount = options.amount || '';
        
        if (!this.enrollId) {
            uni.showToast({
                title: '参数错误',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        }
    },
    methods: {
        // 发票类型改变
        onInvoiceTypeChange(e) {
            this.invoiceForm.type = e.detail.value;
            // 切换到个人发票时清空纳税人识别号
            if (this.invoiceForm.type === 'personal') {
                this.invoiceForm.taxNumber = '';
            }
        },

        // 表单验证
        validateForm() {
            if (!this.invoiceForm.title.trim()) {
                uni.showToast({
                    title: '请输入发票抬头',
                    icon: 'none'
                });
                return false;
            }

            if (this.invoiceForm.type === 'company' && !this.invoiceForm.taxNumber.trim()) {
                uni.showToast({
                    title: '请输入纳税人识别号',
                    icon: 'none'
                });
                return false;
            }

            if (!this.invoiceForm.email.trim()) {
                uni.showToast({
                    title: '请输入联系邮箱',
                    icon: 'none'
                });
                return false;
            }

            // 简单的邮箱格式验证
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(this.invoiceForm.email)) {
                uni.showToast({
                    title: '请输入正确的邮箱格式',
                    icon: 'none'
                });
                return false;
            }

            return true;
        },

        // 提交发票申请
        submitInvoice() {
            if (!this.validateForm()) {
                return;
            }

            this.submitting = true;

            const requestData = {
                enroll_id: this.enrollId,
                type: this.invoiceForm.type,
                title: this.invoiceForm.title.trim(),
                tax_number: this.invoiceForm.taxNumber.trim(),
                email: this.invoiceForm.email.trim(),
                phone: this.invoiceForm.phone.trim(),
                remark: this.invoiceForm.remark.trim()
            };

            console.log('提交发票申请数据:', requestData);

            // 这里需要添加发票申请的API接口
            api.post(urls.enrollInvoice(this.enrollId), requestData).then(res => {
                console.log('发票申请成功:', res);
                uni.showToast({
                    title: '发票申请提交成功',
                    icon: 'success',
                    success: () => {
                        setTimeout(() => {
                            uni.navigateBack();
                        }, 1500);
                    }
                });
            }).catch(err => {
                console.error('发票申请失败:', err);
                uni.showToast({
                    title: err.message || '申请失败，请重试',
                    icon: 'none'
                });
            }).finally(() => {
                this.submitting = false;
            });
        }
    }
}
</script>

<style lang="scss">
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx;
}

/* 页面标题 */
.page-header {
    margin-bottom: 30rpx;
}

.header-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 10rpx;
}

.header-subtitle {
    font-size: 28rpx;
    color: #666;
}

/* 章节标题 */
.section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
}

/* 订单信息 */
.order-info {
    margin-bottom: 30rpx;
}

.info-card {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.info-label {
    font-size: 28rpx;
    color: #666;
    min-width: 160rpx;
}

.info-value {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

/* 表单样式 */
.invoice-form {
    margin-bottom: 30rpx;
}

.form-card {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}

.form-item {
    margin-bottom: 30rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.form-label {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
}

.required {
    color: #f56c6c;
}

.form-input {
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    border: 1rpx solid #e5e5e5;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #333;
    background-color: #fff;
    box-sizing: border-box;

    &:focus {
        border-color: #3c9cff;
    }
}

.form-textarea {
    width: 100%;
    min-height: 120rpx;
    padding: 20rpx;
    border: 1rpx solid #e5e5e5;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #333;
    background-color: #fff;
    box-sizing: border-box;
    resize: none;

    &:focus {
        border-color: #3c9cff;
    }
}

/* 单选按钮组 */
.radio-group {
    display: flex;
    gap: 40rpx;
}

.radio-item {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333;

    radio {
        margin-right: 12rpx;
    }
}

/* 提交按钮 */
.submit-section {
    padding: 30rpx 0;
}

.submit-btn {
    width: 100%;
    height: 88rpx;
    background-color: #3c9cff;
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    border: none;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
        background-color: #ccc;
        color: #999;
    }

    &:active:not(:disabled) {
        opacity: 0.8;
    }
}
</style>
