<template>
    <view class="refund-detail-page">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
            <uni-load-more status="loading" :content-text="{
                contentdown: '加载中...',
                contentrefresh: '加载中...',
                contentnomore: '加载中...'
            }"></uni-load-more>
        </view>

        <!-- 内容区域 -->
        <view v-else>
            <!-- 状态和金额卡片 -->
            <view class="refund-status-card">
                <view class="refund-status-text">{{ refundDetail.status_text || '等待处理' }}</view>
                <view class="refund-amount-row">
                    <text>退款金额</text>
                    <text class="refund-amount">￥{{ refundDetail.amount || '--' }}</text>
                </view>
            </view>

        <!-- 退款信息分组 -->
        <view class="refund-info-group">
            <view class="refund-info-title">退款信息</view>
            <view class="refund-info-row">
                <text>{{ refundDetail.course_name || '--' }}</text>
                <text>￥{{ refundDetail.amount || '--' }}</text>
            </view>
            <view class="refund-info-row">
                <text>退款原因</text>
                <text>{{ refundDetail.reason || '--' }}</text>
            </view>
            <view class="refund-info-row">
                <text>订单号码</text>
                <text>{{ refundDetail.order_no || '--' }}</text>
            </view>
        </view>

        <!-- 退款流程时间线 -->
        <view class="refund-flow-group">
            <view class="refund-info-title">退款流程</view>
            <view class="refund-flow-item" v-for="(item, index) in refundDetail.records" :key="index">
                <view class="refund-flow-dot"></view>
                <view class="refund-flow-content">
                    <view class="refund-flow-main">
                        <text class="refund-flow-bold">{{ item.type_label }}</text>
                        <text class="refund-flow-time">{{ item.created_at || '--' }}</text>
                    </view>
                    <view class="refund-flow-reason" v-html="item.remark"></view>
                </view>
            </view>
        </view>

            <!-- 撤销按钮 -->
            <view class="refund-bottom-bar" v-if="refundDetail.status === 4">
                <button class="refund-cancel-btn" @click="openCancelPopup">撤销退款申请</button>
            </view>
        </view>

        <!-- 撤销弹窗 -->
        <uni-popup ref="cancelPopup" type="center">
            <view class="cancel-dialog">
                <view class="cancel-title">确认撤销退款申请吗？</view>
                <view class="cancel-desc">撤销退款申请后，申请将自动关闭</view>
                <view class="cancel-actions">
                    <button class="cancel-btn" @click="closeCancelPopup">取消</button>
                    <button class="confirm-btn" @click="onCancelRefund">确认撤销</button>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import api from '@/lib/api';
import urls from '@/lib/urls';
import { mapState } from 'pinia';
import { useUserStore } from '@/store/user';

export default {
    data() {
        return {
            enrollId: '',
            loading: true,
            refundDetail: {
                status_text: '',
                amount: 0,
                course_name: '',
                reason: '',
                order_no: '',
                created_at: '',
            },
            showCancelDialog: false,
        };
    },
    computed: {
        ...mapState(useUserStore, ['user', 'orgData'])
    },
    onLoad(options) {
        console.log('refund-detail onLoad options:', options);

        if (options.enrollId) {
            this.enrollId = options.enrollId;
            this.getRefundDetail();
        } else {
            uni.showToast({
                title: '参数错误',
                icon: 'none'
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        }
    },
    onPullDownRefresh() {
        this.getRefundDetail();
    },
    methods: {
        // 获取退款详情数据
        getRefundDetail() {
            if (!this.enrollId) {
                uni.showToast({
                    title: '报名记录ID不存在',
                    icon: 'none'
                });
                return;
            }

            this.loading = true;
            uni.showLoading({
                title: '加载中...'
            });

            console.log('获取退款详情，enrollId:', this.enrollId);

            api.get(urls.enrollRefundDetail(this.enrollId)).then(res => {
                console.log('退款详情数据:', res);
                uni.hideLoading();

                // 设置退款详情数据
                this.refundDetail = {
                    status_text: res.status_text || '等待处理',
                    amount: res.amount || 0,
                    course_name: res.course_name || res.title || '',
                    reason: res.reason || '',
                    order_no: res.order_no || '',
                    created_at: res.created_at || '',
                    // 可能还有其他字段
                    ...res
                };

            }).catch(err => {
                console.error('获取退款详情失败:', err);
                uni.hideLoading();

                uni.showToast({
                    title: err.message || '获取数据失败',
                    icon: 'none'
                });

                // 如果获取失败，延迟返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 2000);

            }).finally(() => {
                this.loading = false;
            });
        },

        openCancelPopup() {
            this.$refs.cancelPopup.open();
        },

        closeCancelPopup() {
            this.$refs.cancelPopup.close();
        },

        onCancelRefund() {
            this.closeCancelPopup();

            // 调用撤销退款API
            uni.showLoading({
                title: '处理中...'
            });

            api.put(urls.enrollCancelRefund(this.enrollId)).then(res => {
                console.log('撤销退款成功:', res);
                uni.hideLoading();

                uni.showToast({
                    title: '撤销退款成功',
                    icon: 'success'
                });

                // 延迟返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);

            }).catch(err => {
                console.error('撤销退款失败:', err);
                uni.hideLoading();

                uni.showToast({
                    title: err.message || '撤销失败，请重试',
                    icon: 'none'
                });
            });
        }
    }
}
</script>

<style lang="scss">
.refund-detail-page {
    min-height: 100vh;
    background: #f5f6fa;
    padding-bottom: 120rpx;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400rpx;
    padding-top: 100rpx;
}

.refund-status-card {
    background: #fff;
    border-radius: 16rpx;
    margin: 32rpx 24rpx 0 24rpx;
    padding: 32rpx 32rpx 24rpx 32rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}
.refund-status-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #222;
    margin-bottom: 32rpx;
}
.refund-amount-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    color: #222;
}
.refund-amount {
    font-size: 32rpx;
    color: #222;
    font-weight: bold;
}

.refund-info-group {
    background: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 0 32rpx 24rpx 32rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}
.refund-info-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #222;
    padding: 32rpx 0 24rpx 0;
}
.refund-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    color: #222;
    padding: 12rpx 0;
}

.refund-flow-group {
    background: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 0 32rpx 24rpx 32rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}
.refund-flow-item {
    display: flex;
    align-items: flex-start;
    margin-top: 24rpx;
    position: relative;
}
.refund-flow-dot {
    width: 24rpx;
    height: 24rpx;
    border-radius: 50%;
    background: #fff;
    border: 4rpx solid #222;
    margin-top: 8rpx;
    margin-right: 24rpx;
    position: relative;
    z-index: 2;
}
.refund-flow-item:not(:last-child) .refund-flow-dot::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translateX(-50%);
    width: 0;
    height: 60rpx;
    border-left: 2rpx dashed #d8d8d8;
    z-index: 1;
}
.refund-flow-content {
    flex: 1;
}
.refund-flow-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.refund-flow-bold {
    font-size: 28rpx;
    font-weight: bold;
    color: #222;
}
.refund-flow-time {
    font-size: 24rpx;
    color: #999;
}
.refund-flow-reason {
    font-size: 24rpx;
    color: #666;
    margin-top: 8rpx;
}

.refund-bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    box-shadow: 0 -2rpx 16rpx rgba(0,0,0,.04);
    padding: 24rpx 24rpx 48rpx 24rpx;
    z-index: 10;
}
.refund-cancel-btn {
    width: 100%;
    height: 88rpx;
    background: #1677ff;
    color: #fff;
    font-size: 32rpx;
    border-radius: 16rpx;
    font-weight: bold;
    border: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-dialog {
    width: 540rpx;
    background: #fff;
    border-radius: 16rpx;
    padding: 48rpx 32rpx 32rpx 32rpx;
    text-align: center;
}
.cancel-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #222;
    margin-bottom: 24rpx;
}
.cancel-desc {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 40rpx;
}
.cancel-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.cancel-btn, .confirm-btn {
    flex: 1;
    height: 80rpx;
    font-size: 32rpx;
    border-radius: 16rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}
.cancel-btn {
    background: #f6f7fa;
    color: #222;
    margin-right: 24rpx;
}
.confirm-btn {
    background: #1677ff;
    color: #fff;
}
</style>