<template>
    <view class="content">
        <!-- 企业名称和图片区域 -->
        <view class="org-header">
            <view class="org-logo-container">
                <!-- 背景图片 -->
                <image v-if="orgInfo.bg_img" :src="orgInfo.bg_img" mode="aspectFill" class="org-bg-img"></image>
                <!-- <image v-else src="/src/images/icon/building-4-line.png" mode="aspectFill" class="org-bg-img"></image> -->

                <!-- 左上角logo -->
                <view class="org-logo-wrapper" v-if="orgInfo.logo">
                    <image :src="orgInfo.logo" mode="aspectFill" class="org-logo"></image>
                </view>

                <!-- 企业名称覆盖层 -->
                <view class="org-name-overlay" v-if="orgInfo.org_name">
                    <text>{{ orgInfo.org_name }}</text>
                </view>
            </view>
        </view>

        <!-- 我的报名记录 -->
        <view class="my-registration">
            <view class="section-title">我的报名</view>
            <uv-skeletons :loading="loading" :skeleton="registrationSkeleton">
                <view v-if="enrollRecord.length > 0" v-for="item in enrollRecord" :key="item.id" class="registration-card">
                    <view class="reg-main">
                        <view class="reg-left">
                            <text class="status-badge" :class="getStatusClass(item.status)">
                                {{ item.status_label }}
                            </text>
                            <view class="reg-title">{{ item.title }}</view>
                            <view class="reg-amount">金额：{{ item.amount }}元</view>
                            <view v-if="item.status === 1 && item.reject_reason" class="reg-reason">
                                {{ item.reject_reason }}
                            </view>
                        </view>
                        <view class="reg-actions">
                            <!-- 待审核状态：无按钮 -->
                            <template v-if="getStatusType(item.status) === 'pending'">
                                <!-- 无操作按钮 -->
                            </template>
                            <!-- 已驳回状态：重新报名 -->
                            <template v-else-if="getStatusType(item.status) === 'rejected'">
                                <button class="action-btn primary" @click="onReapply(item)">重新报名</button>
                            </template>
                            <!-- 待支付状态：支付 -->
                            <template v-else-if="getStatusType(item.status) === 'unpaid'">
                                <button class="action-btn primary" @click="onPay(item)">支付</button>
                            </template>
                            <!-- 已支付状态：申请退款、开发票 -->
                            <template v-else-if="getStatusType(item.status) === 'paid'">
                                <button class="action-btn secondary" @click="onRefund(item)">申请退款</button>
                                <button class="action-btn primary" @click="onInvoice(item)">开发票</button>
                            </template>
                            <!-- 退款中状态：撤销退款、退款详情 -->
                            <template v-else-if="getStatusType(item.status) === 'refunding'">
                                <button class="action-btn secondary" @click="onCancelRefund(item)">撤销退款</button>
                                <button class="action-btn primary" @click="onRefundDetail(item)">退款详情</button>
                            </template>
                            <!-- 退款关闭状态：退款详情、开发票 -->
                            <template v-else-if="getStatusType(item.status) === 'refund_closed'">
                                <button class="action-btn secondary" @click="onRefundDetail(item)">退款详情</button>
                                <button class="action-btn primary" @click="onInvoice(item)">开发票</button>
                            </template>
                            <!-- 已退款状态：退款详情 -->
                            <template v-else-if="getStatusType(item.status) === 'refunded'">
                                <button class="action-btn primary" @click="onRefundDetail(item)">退款详情</button>
                            </template>
                        </view>
                    </view>
                </view>
                <view class="no-registration" v-else>
                    <text>暂无报名记录</text>
                </view>
            </uv-skeletons>
        </view>

        <!-- 报名类型列表 -->
        <view class="registration-types">
            <view class="section-title">请选择报名类型：</view>
            <uv-skeletons :loading="loading" :skeleton="typesSkeleton">
                <view class="type-list">
                    <view
                        class="type-item"
                        v-for="(item, index) in enrollConfig"
                        :key="index"
                        @click="selectType(item)"
                    >
                        <view class="type-content">
                            <text class="type-name">{{ item.title }}</text>
                        </view>
                        <view class="type-arrow">
                            <image src="/src/images/icon/arrow-right-s-line.png" mode="aspectFit"></image>
                        </view>
                    </view>
                </view>
            </uv-skeletons>
        </view>

        <!-- 空数据状态 -->
        <view class="no-data-nomal-box" v-if="!loading && enrollConfig.length === 0">
            <view class="ndnb-icon">
                <image src="/src/images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无可报名的培训类型</text>
        </view>

        <view class="orgfn-eb"></view>
        <view class="org-foot-nav" v-if="isEnrolled">
            <navigator open-type="redirect" url="/packageOrg/organization/enroll/index" class="orgfn-item cur">
                <image src="/src/images/icon/account-box-edit-fill.png"></image>
                <text>报名</text>
            </navigator>
            <navigator open-type="redirect" url="/packageOrg/organization/home" class="orgfn-item">
                <image src="/src/images/icon/graduation-cap-line.png"></image>
                <text>培训</text>
            </navigator>
            <navigator open-type="redirect" url="/packageOrg/organization/me" class="orgfn-item">
                <image src="/src/images/icon/user-4-line.png"></image>
                <text>我的</text>
            </navigator>
        </view>
    </view>
    <pay-alert ref="payAlert" :order="order" :allow-types="allowTypes" @createdOrder="onCreatedOrder" @paymentCallback="onPaymentCallback"></pay-alert>

</template>

<script>
import api from '@/lib/api';
import urls from '@/lib/urls';
import {mapState} from 'pinia';
import {useUserStore} from '@/store/user';
import {alert, openWebView, showDelayLoading} from '@/lib/utils';
import UvSkeletons from "@/uni_modules/uv-skeletons/components/uv-skeletons/uv-skeletons.vue";
import PayAlert from "@/components/pay-alert.vue";
import {loginRequired} from "@/lib/login";

export default {
    components: {PayAlert, UvSkeletons},
    data() {
        return {
            orgId: '',
            loading: true,
            order: {},
            allowTypes: ['amount'],
            // 机构信息
            orgInfo: {
                logo: '',
                bg_img: '',
                org_name: ''
            },
            // 报名记录
            enrollRecord: [],
            // 报名配置
            enrollConfig: [],
            // 是否完成报名（已支付）
            isEnrolled: false,
            registrationSkeleton: [
                {
                    type: 'custom',
                    style: 'width: 100%; height: 120rpx; margin-bottom: 20rpx;'
                }
            ],
            typesSkeleton: [
                {
                    type: 'line',
                    num: 4,
                    gap: '20rpx',
                    style: 'height: 100rpx;'
                }
            ]
        };
    },
    computed: {
        ...mapState(useUserStore, ['user', 'orgData'])
    },
    onLoad(options) {
        console.log('onLoad options:', options);

        // 优先从路由参数获取 orgId
        if (options.orgId) {
            this.orgId = options.orgId;
            console.log('从路由参数获取 orgId:', this.orgId);
        } else if (uni.getStorageSync("org_id")) {
            this.orgId = uni.getStorageSync("org_id");
            console.log('从本地存储获取 orgId:', this.orgId);
        }

        this.isEnrolled = options.isEnrolled === 'true';

        console.log('最终 orgId:', this.orgId);
        // this.initData();
    },
    onShow() {
        this.initData();
    },
    onPullDownRefresh() {
        this.initData();
    },
    methods: {
        initData() {
            console.log('initData 开始执行');
            console.log('当前 orgId:', this.orgId);

            if (!this.orgId) {
                console.error('orgId 为空，无法请求接口');
                uni.showToast({
                    title: '机构ID不存在',
                    icon: 'none'
                });
                this.loading = false;
                return;
            }

            this.loading = true;
            const apiUrl = urls.enroll(this.orgId);
            console.log('请求URL:', apiUrl);

            api.get(apiUrl).then(res => {
                console.log('接口返回数据:', res);

                // 设置机构信息
                this.orgInfo = {
                    logo: res.logo || '',
                    bg_img: res.bg_img || '',
                    org_name: res.org_name || ''
                };

                // 设置报名记录
                this.enrollRecord = res.enrollRecord || [];

                // 设置报名配置
                this.enrollConfig = res.enrollConfig || [];

                // 报名记录里面status大于等于3都要显示导航
                this.isEnrolled = this.enrollRecord.some(item => {
                    console.log('检查报名记录状态:', item);
                    return item.status >= 3;
                });

                console.log('用户报名状态 isEnrolled:', this.isEnrolled);

            }).catch(err => {
                uni.hideLoading();
                console.error('获取数据失败:', err);
                uni.showToast({
                    title: err.message || '获取数据失败',
                    icon: 'none'
                });
            }).finally(() => {
                this.loading = false;
                uni.stopPullDownRefresh();
            });
        },
        selectType(item) {
            if (item.is_apply) {
                uni.showToast({
                    title: '您已提交报名信息',
                    icon: 'none'
                });
                return;
            }
            if (!item.url) {
                uni.showToast({
                    title: '报名链接不存在',
                    icon: 'none'
                });
                return;
            }

            // 使用项目中的openWebView工具函数跳转到H5报名页面
            const title = `${item.title}报名`;
            openWebView(item.url, title);
        },
        onCreatedOrder(buyType) {
            const hideLoading = showDelayLoading("")
            let url = urls.enrollBuyOrder(this.enrollId);
            api.post(url).then(res => {
                for (let key in res) {
                    this.$set(this.order, key, res[key])
                }
                if (buyType === 'amount') {
                    this.$refs.payAlert.payFromAmount()
                }
            }).catch(e => {
                uni.showModal({
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                hideLoading()
            })
        },
        onPaymentCallback() {
            this.initData()
        },

        // 根据接口返回的数字状态设置样式类
        getStatusClass(status) {
            const statusMap = {
                0: 'status-pending',    // 待审核
                1: 'status-rejected',   // 已驳回
                2: 'status-unpaid',     // 待支付
                3: 'status-paid',       // 已支付
                4: 'status-refunding',  // 退款中
                5: 'status-refund-closed', // 退款关闭
                6: 'status-refunded',   // 已退款
            };
            return statusMap[status] || 'status-pending';
        },

        // 获取状态类型用于判断显示哪些按钮
        getStatusType(status) {
            const typeMap = {
                0: 'pending',       // 待审核
                1: 'rejected',      // 已驳回
                2: 'unpaid',        // 待支付
                3: 'paid',          // 已支付
                4: 'refunding',     // 退款中
                5: 'refund_closed', // 退款关闭
                6: 'refunded',      // 已退款
            };
            return typeMap[status] || 'pending';
        },

        // 重新报名
        onReapply(item) {
            openWebView(item.apply_url, '重新报名');
        },

        // 支付
        onPay(item) {
            this.enrollId = item.id;
            loginRequired().then(() => {
                this.$refs.payAlert.buyFromAmount()
            })
        },

        // 申请退款
        onRefund(item) {
            console.log('申请退款:', item);

            // 跳转到申请退款页面，传递报名记录信息
            uni.navigateTo({
                url: `/packageOrg/organization/enroll/apply-refund?enrollId=${item.id}&title=${encodeURIComponent(item.title)}&amount=${item.amount}`
            });
        },

        // 开发票
        onInvoice(item) {
            uni.showToast({
                title: '开发票功能待实现',
                icon: 'none'
            });
        },

        // 撤销退款
        onCancelRefund(item) {
            console.log('撤销退款:', item);

            // 显示确认弹窗
            uni.showModal({
                title: '确认撤销退款申请吗？',
                content: '撤销退款申请，申请将自动关闭',
                confirmText: '确认撤销',
                confirmColor: '#3c9cff',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        // 用户点击确认撤销
                        this.submitCancelRefund(item);
                    }
                }
            });
        },

        // 提交撤销退款请求
        submitCancelRefund(item) {
            api.put(urls.enrollCancelRefund(item.id)).then(res => {
                console.log('撤销退款成功:', res);
                uni.showToast({
                    title: '撤销退款成功',
                    icon: 'success',
                    success: () => {
                        this.initData();
                    }
                });
            }).catch(err => {
                console.error('撤销退款失败:', err);
                uni.showToast({
                    title: err.message || '撤销失败，请重试',
                    icon: 'none'
                });
            });
        },

        // 退款详情
        onRefundDetail(item) {
            console.log('查看退款详情:', item);

            // 跳转到退款详情页面，传递报名记录ID
            uni.navigateTo({
                url: `/packageOrg/organization/enroll/refund-detail?enrollId=${item.id}`
            });
        }
    }
}
</script>

<style lang="scss">
page {
    background-color: #F3F3FF;
}

.content {
    padding: 0 30rpx 30rpx;
}

/* 企业头部区域 */
.org-header {
    margin: 0 -30rpx 30rpx -30rpx;
    overflow: hidden;
}

.org-logo-container {
    position: relative;
    width: 100%;
    height: 400rpx;
    overflow: hidden;
}

.org-bg-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.org-logo-wrapper {
    position: absolute;
    top: 26rpx;
    left: 60rpx;
    width: 58rpx;
    height: 58rpx;
    //border-radius: 8rpx;
    //overflow: hidden;
    //background-color: rgba(255, 255, 255, 0.95);
    //box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.org-logo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.org-name-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgb(35, 107, 145, 0.5);
    padding: 20rpx 30rpx;

    text {
        font-size: 34rpx;
        color: #fff;
        text-align: center;
        display: block;
        width: 100%;
    }
}

/* 章节标题 */
.section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
}

/* 我的报名区域 */
.my-registration {
    margin-bottom: 40rpx;
}

.registration-card {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    margin-bottom: 20rpx;
    border: 1rpx solid #e5e5e5;
}

.reg-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.reg-left {
    flex: 1;
}

.status-badge {
    display: inline-block;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    color: #fff;
    margin-bottom: 16rpx;

    &.status-pending {
        background-color: #3c9cff;
    }

    &.status-rejected {
        background-color: #f56c6c;
    }

    &.status-unpaid {
        background-color: #ff9500;
    }

    &.status-paid {
        background-color: #5ac725;
    }

    &.status-refunding {
        background-color: #ff9500;
    }

    &.status-refund-closed {
        background-color: #999;
    }

    &.status-refunded {
        background-color: #5ac725;
    }
}

.reg-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 12rpx;
    line-height: 1.4;
}

.reg-amount {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 12rpx;
}

.reg-reason {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
}

.reg-actions {
    display: flex;
    flex-direction: row;
    gap: 12rpx;
    align-items: center;
    justify-content: flex-end;
}

.action-btn {
    padding: 12rpx 24rpx;
    border-radius: 8rpx;
    font-size: 26rpx;
    font-weight: 500;
    line-height: 1;
    border: none;
    min-width: 120rpx;
    text-align: center;

    &.primary {
        background-color: #3c9cff;
        color: #fff;
    }

    &.secondary {
        background-color: #f0f0f0;
        color: #666;
    }

    &:active {
        opacity: 0.8;
    }
}

.no-registration {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 60rpx 30rpx;
    text-align: center;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);

    text {
        font-size: 28rpx;
        color: #999;
    }
}

/* 报名类型列表区域 */
.registration-types {
    margin-bottom: 40rpx;
}

.type-list {
    background-color: #fff;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}

.type-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    transition: background-color 0.2s;

    &:last-child {
        border-bottom: none;
    }

    &:active {
        background-color: #f8f8f8;
    }
}

.type-content {
    flex: 1;
    display: flex;
    align-items: center;
}

.type-name {
    font-size: 28rpx;
    color: #333;
    margin-right: 16rpx;
}

.type-level {
    font-size: 26rpx;
    color: #666;
}

.type-arrow {
    width: 32rpx;
    height: 32rpx;

    image {
        width: 100%;
        height: 100%;
    }
}

/* 空数据状态 */
.no-data-nomal-box {
    text-align: center;
    padding: 100rpx 0;

    .ndnb-icon {
        margin-bottom: 30rpx;

        image {
            width: 300rpx;
        }
    }

    .ndnb-tip {
        font-size: 28rpx;
        color: #999;
    }
}

/* 底部导航 */
.org-foot-nav {
    display: flex;
    align-items: center;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    z-index: 2;
}
.orgfn-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
}
.orgfn-item image {
    width: 48rpx;
    height: 48rpx;
}
.orgfn-item text {
    padding-top: 10rpx;
    font-size: 24rpx;
    color: #aaa;
}
.orgfn-item.cur text {
    color: #390ABC;
    font-weight: bold;
}
.orgfn-eb {
    height: 130rpx;
    padding-bottom: env(safe-area-inset-bottom);
}


</style>