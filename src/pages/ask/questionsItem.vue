<template>
    <view class="ask-cnt-list">
        <uv-skeletons :loading="loading" :skeleton="skeleton">
            <view class="acl-item" v-for="(item, index) in questions" :key="index">
                <navigator :url="'/pages/ask/n-detail?sid=' +item.sid" class="acli-top">
                    <view class="aclit-l">
                        <image :src="item.user.avatar" mode="aspectFill"></image>
                    </view>
                    <view class="aclit-r">
                        <view class="aclitr-tit">{{ item.user.nickname }}</view>
                        <view class="aclitr-time">
                            <uni-dateformat :threshold="[0,86400000]" :date="item.created_at"
                                            format="yyyy-MM-dd hh:mm"></uni-dateformat>
                        </view>
                    </view>
                </navigator>
                <view @click="goPage(item)" class="acli-main">
                    <view :url="'/pages/ask/n-detail?sid=' +item.sid" class="aclic-txt">
                        <l-text-ellipsis
                            :rows="4"
                            :content="item.content"
                            expand-text="展开"
                            collapse-text="收起"
                        />
                        <!--                        <mp-html @linktap="onLinkTap"  :content="tagContent(item)"></mp-html>-->
                    </view>
                    <view class="aclic-img" v-if="item.image_urls.length">
                        <image v-for="(itemImg, indexImg) in item.image_urls" :key="indexImg"
                               @click.stop="previewImage(item.image_urls, itemImg, item.sid)" :src="itemImg"
                               mode="aspectFill"></image>
                    </view>
                </view>
                <view class="acli-foot">
                    <button :id="item.sid" open-type="share" @click="share(item)" class="acli-item">
                        <image src="../../images/icon/share-forward-2-line.png"></image>
                        <text>分享</text>
                    </button>
                    <view class="acli-item" @click="attitude(item)">
                        <image v-if="item.is_like" src="../../images/icon/thumb-up-fill.png"></image>
                        <image v-else src="../../images/icon/thumb-up-line.png"></image>
                        <text v-if="item.attitudes_count">{{ item.attitudes_count }}</text>
                        <text v-else>赞</text>
                    </view>
                    <navigator :url="'/pages/ask/n-detail?sid=' +item.sid" class="acli-item">
                        <image src="../../images/icon/chat-4-line.png"></image>
                        <text v-if="item.answer_count">{{ item.answer_count }}</text>
                        <text v-else>评论</text>
                    </navigator>
                </view>
            </view>
        </uv-skeletons>
    </view>
</template>
<script>

import {loginRequired} from "@/lib/login";
import api from "@/lib/api";
import {alert} from "@/lib/utils";
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

export default {
    components: {
        mpHtml
    },
    props: {
        questions: {
            type: Array,
            default: []
        },
        sid: {
            type: String,
            default: ""
        },
        loading: {
            type: Boolean,
            default: true
        },
    },
    data() {
        return {
            sid: '',
            itemLoading: false,
            skeleton: [
                {
                    type: 'flex',
                    num: 5,
                    style: 'backgroundColor: #fff;borderRadius: 12rpx;marginBottom:30rpx;',
                    children: [
                        {
                            type: 'avatar',
                            num: 1,
                            style: 'marginRight: 10rpx;marginLeft: 10rpx;marginTop: 30rpx;marginBottom:30rpx;'
                        }, {
                            type: 'line',
                            num: 2,
                            gap: '30rpx',
                            style: ['width: 200rpx;marginTop:30rpx;', 'width:300rpx;']
                        },
                    ]
                },
            ]
        }
    },
    watch: {
        questions(newValue, oldValue) {
            console.log(newValue)
            if (this.questions.length) {
                this.loading = false
            }
            console.log(this.loading)
        }
    },
    methods: {
        previewImage(data, src, sid) {
            uni.previewImage({
                urls: data,
                current: src,
                success: () => {

                },
                fail: (e) => {
                    console.log(e);
                }
            })
            api.request('qa/questions/' + sid, {
                method: 'HEAD',
            }).catch(err => alert(err.message))
        },
        attitude(data) {
            loginRequired().then(() => {
                let postData = {
                    attitude: 1
                }
                if (this.itemLoading) {
                    return
                }
                this.itemLoading = true
                if (!data.is_like) {
                    api.post("attitude/question/" + data.sid, postData).then(res => {
                        data.attitudes_count++
                        data.is_like = true
                    }).catch(err => alert(err.message)).finally(() => {
                        this.itemLoading = false
                    })
                } else {
                    api.delete("attitude/question/" + data.sid + '?attitude=1').then(res => {
                        data.is_like = false
                        data.attitudes_count--
                    }).catch(err => alert(err.message)).finally(() => {
                        this.itemLoading = false
                    })
                }
            })
        },
        tagContent(data) {
            console.log(data)
            if (data.tags?.length) {
                let content = null
                for (let index in data.tags) {
                    let searchTerm = '#' + data.tags[index].name;
                    let replacement = "<a href='/pages/ask/topic-detail?id=" + data.tags[index].id + "'>#" + data.tags[index].name + "</a>";
                    // 使用 RegExp 构造函数创建一个正则表达式，其中 'g' 表示全局匹配
                    let regex = new RegExp(searchTerm, 'g');

                    if (!content) {
                        content = data.content;
                    }
                    let newStr = content.replace(regex, replacement);
                    content = newStr
                }
                return content;
            } else {
                return data.content;
            }
        },
        goPage(data) {
            uni.navigateTo({
                url: '/pages/ask/n-detail?sid=' + data.sid
            })
        },
        onLinkTap(e) {
            console.log(e)
        },
        share(data) {
            this.$emit("shareSid", data)
        }
    },

}
</script>
<style>
.aclic-img {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 0 30rpx 30rpx;
}

.aclic-img image {
    width: 209rpx;
    height: 209rpx;
    margin-right: 1px;
    margin-bottom: 1px;
    border-radius: 12rpx;
}

/* 内容块 */
.ask-cnt-list {
    margin: 0 30rpx;
}

.acl-item {
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin-bottom: 30rpx;
}

.acli-top {
    display: flex;
    align-items: center;
    padding: 30rpx 30rpx 0;
}

.aclit-l image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 100%;
}

.aclit-r {
    padding-left: 20rpx;
}

.aclitr-tit {
    font-size: 28rpx;
    font-weight: bold;
}

.aclitr-time {
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}

.aclic-txt {
    padding: 30rpx;
    font-size: 28rpx;
    line-height: 1.6;
    white-space: pre-line;
}

.aclic-txt navigator {
    color: #390ABC;
    display: inline-block;
}

.aclic-img {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 0 30rpx 30rpx;
}

.aclic-img image {
    width: 209rpx;
    height: 209rpx;
    margin-right: 1px;
    margin-bottom: 1px;
    border-radius: 12rpx;
}

.acli-foot {
    display: flex;
    align-items: center;
    border-top: 1px solid #f3f3ff;
}

.acli-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}

.acli-item image {
    width: 32rpx;
    height: 32rpx;
}

.acli-item text {
    padding-left: 20rpx;
    font-size: 28rpx;
}

.ask-foot-box {
    position: sticky;
    bottom: 0;
    padding: 16rpx 30rpx;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.afb-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
}

.afb-btn {
    flex: 1;
    height: 90rpx;
    text-align: center;
    line-height: 90rpx;
    background-color: #390ABC;
    color: #fff;
    font-size: 28rpx;
    border-radius: 80rpx;
}

.afb-small-btn {
    width: 200rpx;
    margin-left: 30rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 28rpx;
    color: #390ABC;
}

</style>