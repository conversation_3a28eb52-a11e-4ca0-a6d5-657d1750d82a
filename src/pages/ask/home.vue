<template>
    <view class="content">
        <navigator url="/pages/search/qa" class="qa-search">
            <image src="../../images/icon/search-line.png"></image>
            <text>检索问题</text>
        </navigator>
        <view class="hot-topic">
            <view class="dpb-tit">
                <view class="dpbt-txt">推荐话题</view>
                <navigator url="/pages/ask/topic-list?type=2" class="dpbt-more">
                    <text>查看全部</text>
                    <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                </navigator>
            </view>
            <uv-skeletons :loading="loadingInit" :skeleton="skeleton">
                <scroll-view :show-scrollbar="false" class="sub-sview-list navScroll" scroll-x="true">
                    <navigator :url="'/pages/ask/topic-detail?id='+ item.id" class="sub-sview-item"
                               v-for="(item, index) in tags" :key="index">#{{ item.name }}
                    </navigator>
                </scroll-view>
            </uv-skeletons>
        </view>
        <QuestionsItem :loading="loadingInit" :questions="questions" @shareSid="shareSid"></QuestionsItem>
        <uni-load-more v-if="questions.length" :status="statusLoad"></uni-load-more>

        <view class="ask-foot-box">
            <view class="afb-main">
                <view @click="goPage('/pages/ask/post')" class="afb-btn">发动态</view>
                <view @click="goPage('/pages/me/qa')" class="afb-small-btn">
                    我发表的
                    <text v-if="answerTip" class="nomal-badge"></text>
                </view>
            </view>
        </view>
        <view class="empty-block"></view>
    </view>
</template>

<script>
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {alert, showDelayLoading} from "@/lib/utils";
import api from "@/lib/api";
import {loginRequired} from "@/lib/login";
import QuestionsItem from "./questionsItem";
import {getAppName} from "@/lib/context";

export default {
    components: {QuestionsItem},
    data() {
        return {
            tags: [],
            questions: [],
            next_cursor: '',
            hideLoading: '',
            loading: false,
            loadingInit: true,
            answerTip: false,
            statusLoad: 'loading',
            tagId: '',
            sid: '',
            skeleton: [
                {
                    type: 'flex',
                    children: [
                        {
                            type: 'custom',
                            style: 'width:150rpx;height:70rpx;marginLeft:30rpx; marginBottom:30rpx;'
                        }, {
                            type: 'custom',
                            style: 'width:150rpx;height:70rpx;marginLeft:30rpx;'
                        }, {
                            type: 'custom',
                            style: 'width:150rpx;height:70rpx;marginLeft:30rpx;'
                        },
                    ]
                }
            ],
            appName: getAppName()
        }
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    onLoad() {
        this.qaData()
        this.getTags()
        this.getQuestionList()
        uni.$on('qa_index_change', () => {
            this.questions = [];
            this.getTags()
            this.next_cursor = ''
            this.getQuestionList();

        })
        this.hideLoading = showDelayLoading("请稍后", 200)
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.questions = [];
        this.getTags()
        this.loadingInit = true;
        this.next_cursor = ""
        this.getQuestionList()

    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("qa_index_change")
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.next_cursor) {
            this.getQuestionList();
        }
    },
    methods: {
        qaData() {
            api.get("pages/qa").then(res => {
                this.answerTip = res.answer_tip
            })
        },
        getTags() {
            let data = {
                recommend: 0,
                cursor: "",
            }
            api.get("qa/tags", data).then(res => {
                this.tags = res.data
            }).catch(err => alert(err.message))
        },
        getQuestionList() {
            let data = {
                next_cursor: this.next_cursor,
                tag_id: this.tagId
            }
            this.loading = true
            api.get("qa/questions", data).then(res => {
                this.questions.push(...res.data)
                this.next_cursor = res.next_cursor
                this.hideLoading()
                if (!res.data.length || res.data.length < 19) {
                    this.statusLoad = 'noMore'
                }
            }).catch(err => {
                alert(err.message)
                this.hideLoading()
            }).finally(() => {
                uni.stopPullDownRefresh();
                this.loading = false;
                this.loadingInit = false;

            })
        },
        attitude(data) {
            loginRequired().then(() => {
                let postData = {
                    attitude: 1
                }
                if (this.loading) {
                    return
                }
                this.loading = true
                if (!data.is_like) {
                    api.post("attitude/question/" + data.sid, postData).then(res => {
                        data.attitudes_count++
                        data.is_like = true
                    }).catch(err => alert(err.message)).finally(() => {
                        this.loading = false
                    })
                } else {
                    api.delete("attitude/question/" + data.sid + '?attitude=1').then(res => {
                        data.is_like = false
                        data.attitudes_count--
                    }).catch(err => alert(err.message)).finally(() => {
                        this.loading = false
                    })
                }
            })
        },
        tagChoice(data) {
            this.tagId = data.id
            this.questions = [];
            this.next_cursor = ''
            this.getQuestionList();
        },
        goPage(url) {
            if (url == "/pages/me/qa") {
                if (this.answerTip) {
                    url = url + "?is_tip=1"
                }
                this.answerTip = false
            }
            loginRequired().then(() => {
                uni.navigateTo({
                    url
                })
            })
        },
    },
    onShareAppMessage(res) {
        return {
            title: this.appName,
            path: '/pages/ask/n-detail?sid=' + res.target.id
        };
    },
}
</script>

<style>
page {
    background-color: #f3f3ff;
}

.qa-search {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin: 30rpx;
}

.qa-search image {
    width: 32rpx;
    height: 32rpx;
}

.qa-search text {
    padding-left: 20rpx;
    font-size: 28rpx;
    color: #999;
}

.hot-topic {
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin: 0 30rpx 30rpx;
}

/* 标题 */
.dpb-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 20rpx 0 30rpx;
}

.dpbt-txt {
    font-size: 28rpx;
    font-weight: bold;
}

.dpbt-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}

.dpbt-more text {
    font-size: 24rpx;
    color: #999;
}

.dpbt-more image {
    width: 32rpx;
    height: 32rpx;
}

.sub-sview-list {
    white-space: nowrap;
    width: 100%;
    border-bottom: 1px solid #F3F3FF;
}

.sub-sview-item {
    display: inline-block;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 30rpx;
    text-align: center;
    font-size: 36rpx;
    font-size: 24rpx;
    background-color: #fef7ed;
    border: 1px solid #ff731c;
    color: #ff731c;
    border-radius: 70rpx;
    margin-left: 30rpx;
    margin-bottom: 30rpx;
}

.sub-sview-item.cur {
    background-color: #390ABC;
    color: #fff;
    border: 1px solid #390ABC;
}

/* 内容块 */
.ask-cnt-list {
    margin: 0 30rpx;
}

.acl-item {
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin-bottom: 30rpx;
}

.acli-top {
    display: flex;
    align-items: center;
    padding: 30rpx 30rpx 0;
}

.aclit-l image {
    width: 80rpx;
    height: 80rpx;
    border-radius: 100%;
}

.aclit-r {
    padding-left: 20rpx;
}

.aclitr-tit {
    font-size: 28rpx;
    font-weight: bold;
}

.aclitr-time {
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}

.aclic-txt {
    padding: 30rpx;
    white-space: pre-wrap;
    font-size: 32rpx;
    line-height: 1.6;
}

.aclic-txt navigator {
    color: #390ABC;
    display: inline-block;
}

.aclic-img {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 0 30rpx 30rpx;
}

.aclic-img image {
    width: 209rpx;
    height: 209rpx;
    margin-right: 1px;
    margin-bottom: 1px;
    border-radius: 12rpx;
}

.acli-foot {
    display: flex;
    align-items: center;
    border-top: 1px solid #f3f3ff;
}

.acli-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}

.acli-item image {
    width: 32rpx;
    height: 32rpx;
}

.acli-item text {
    padding-left: 20rpx;
    font-size: 28rpx;
}

.ask-foot-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16rpx 30rpx;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    border-top: 1px solid #e7e7e7;
    z-index: 2;
}

.empty-block {
    height: 130rpx;
    padding-bottom: env(safe-area-inset-bottom);
}

.afb-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
}

.afb-btn {
    flex: 1;
    height: 90rpx;
    text-align: center;
    line-height: 90rpx;
    background-color: #390ABC;
    color: #fff;
    font-size: 28rpx;
    border-radius: 80rpx;
}

.afb-small-btn {
    position: relative;
    width: 200rpx;
    margin-left: 30rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 28rpx;
    color: #390ABC;
}
</style>