<template>
    <view class="content">
        <uv-skeletons :loading="loading" :skeleton="skeleton"></uv-skeletons>

        <view v-if="!loading" class="ask-detail-box">
            <view class="adb-tit">{{ question?.title }}</view>
            <view class="adb-des" v-if="question?.content">
                <mp-html :content="question?.content"/>
            </view>
            <view class="adb-q-foot">
                <view class="adb-info">来自
                    <text v-if="question?.anonymous != 1">{{ question.user?.nickname }}</text>
                    <text v-else>匿名</text>
                    的提问
                </view>
                <view class="adb-time">
                    <uni-dateformat :threshold="[0,86400000]" :date="question?.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat>
                </view>
            </view>
        </view>

        <view class="answer-list-box">
            <view class="alb-tit">
                <text>全部回答</text>
            </view>
            <view class="alb-list">
                <view class="albl-item" v-for="(item, index) in answersList" :key="index">
                    <view class="albli-top">
                        <image v-if="!item.anonymous" :src="item.user?.avatar"></image>
                        <image v-else :src="defaultAvatar"></image>
                        <text v-if="!item.anonymous">{{ item.user?.nickname }}</text>
                        <text v-else>匿名</text>
                    </view>
                    <view class="albli-cnt">
                        <mp-html :content="item.content"/>

                    </view>
                    <view class="albli-foot">
                        <view class="alblif-l">
                            <uni-dateformat :threshold="[0,86400000]" :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat>
                        </view>
                        <view class="alblif-r">
                            <view class="alblifr-item" @click="like(item)">
                                <image v-if="item.is_like" src="../../images/icon/thumb-up-fill.png"></image>
                                <image v-else src="../../images/icon/thumb-up-line.png"></image>
                                <text v-if="item.like_count">{{ item.like_count }}</text>
                            </view>
                            <view class="alblifr-item" @click="dislike(item)">
                                <image v-if="item.dislike" src="../../images/icon/thumb-down-fill.png"></image>
                                <image v-else src="../../images/icon/thumb-down-line.png"></image>
                            </view>
                            <button open-type="share" class="alblifr-item">
                                <image src="../../images/icon/share-forward-2-line.png"></image>
                                <text>分享</text>
                            </button>
                        </view>
                    </view>
                </view>
            </view>
            <uv-skeletons v-if="!answersList.length" :loading="loading" :skeleton="skeleton"></uv-skeletons>

            <view class="no-data-nomal-box" v-if="!answersList.length && !loading">
                <view class="ndnb-icon">
                    <image src="../../images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无回答</text>
            </view>
            <view class="related-answer-box">
                <view class="alb-tit">
                    <text>最新问题</text>
                </view>
                <view class="rab-list">
                    <navigator :url="'/pages/ask/detail?sid=' +item.sid" class="rabl-item"
                               v-for="(item, index) in newList" :key="index">
                        {{ item.title }}？
                    </navigator>
                </view>
            </view>
        </view>

        <view class="qpf-empty-block"></view>
        <view class="qa-part-fixed">
            <view @click="goPage('/pages/ask/answer?sid=' + sid + '&title=' + question.title)" class="qpf-l">
                回答问题
            </view>
            <button open-type="share" class="qpf-r">
                <image src="../../images/icon/share-forward-2-line.png"></image>
                <text>分享问题</text>
            </button>
        </view>
    </view>
</template>

<script>
import api from "../../lib/api";
import {showDelayLoading, alert} from "../../lib/utils";
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {loginRequired} from "@/lib/login";
import { getAppName } from '@/lib/context.js';

export default {
    data() {
        return {
            sid: '',
            question: {},
            bst: {},
            cursor: '',
            defaultAvatar: '',
            answersList: [],
            loading: false,
            newList: [],
            skeleton: [{
                type: 'flex',
                num: 1,
                children: [{
                    type: 'line',
                    num: 3,
                    gap: '30rpx',
                    style: ['width: 200rpx;', null, 'width:400rpx;']
                }]
            }],
            appName: getAppName()
        }
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    onLoad(query) {
        if (query.sid) {
            this.sid = query.sid
            this.initData()
            this.getAnswersList()
        }
        uni.$on('qa_index_answers', () => {
            this.cursor = '';
            this.answersList = [];
            this.getAnswersList()
        })
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.cursor) {
            this.getAnswersList();
        }
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.cursor = '';
        this.answersList = [];
        this.initData()
        this.getAnswersList()

    },
    onUnload() {
        uni.$off("qa_index_answers");
    },
    components: {
        mpHtml
    },
    methods: {
        initData() {
            const hideLoading = showDelayLoading("加载中", 200)

            api.get('qa/questions/' + this.sid).then(res => {
                this.question = res.question
                this.bst = res.bst
                this.defaultAvatar = res.default_avatar
                this.newList = res.new_list
                hideLoading()
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                uni.stopPullDownRefresh();

            })
        },
        getAnswersList() {
            let data = {
                cursor: this.cursor
            }
            if (this.loading) {
                return
            }
            this.loading = true
            api.get('qa/questions/' + this.sid + '/answers', data).then(res => {

                this.cursor = res.next_cursor;
                this.answersList.push(...res.data);

            }).catch(err => alert(err.message)).finally(() => {
                this.loading = false;
                uni.stopPullDownRefresh();

            })
        },
        shareAnswers() {

        },
        like(data) {
            loginRequired().then(() => {
                let postData = {
                    attitude: 1
                }
                if (!data.is_like) {
                    if (this.loading) {
                        return
                    }
                    this.loading = true
                    api.post("attitude/answer/" + data.sid, postData).then(res => {
                        data.is_like = true
                        data.dislike = false
                        data.like_count++
                        uni.showToast({
                            title: "点赞成功",
                            icon: "none"
                        })
                    }).catch(err => alert(err.message)).finally(() => this.loading = false)
                } else {
                    api.delete("attitude/answer/" + data.sid + '?attitude=1').then(res => {
                        data.is_like = false
                        data.like_count--
                    }).catch(err => alert(err.message))
                }
            })
        },
        dislike(data) {
            loginRequired().then(() => {
                let postData = {
                    attitude: 2
                }
                if (!data.dislike) {
                    api.post("attitude/answer/" + data.sid, postData).then(res => {
                        if (data.is_like) {
                            data.like_count--

                        }
                        data.dislike = true
                        data.is_like = false
                    }).catch(err => alert(err.message))
                } else {
                    api.delete("attitude/answer/" + data.sid + '?attitude=2').then(res => {
                        data.dislike = false
                    }).catch(err => alert(err.message))
                }
            })

        },
        goPage(url) {
            loginRequired().then(() => {
                uni.navigateTo({
                    url
                })
            })

        }
    },
    onShareAppMessage() {
        return {
            title: this.appName,
            path: '/pages/ask/detail?sid=' + this.sid
        };
    },
}
</script>

<style>
page {
    background-color: #fff;
}

.content {
    padding: 30rpx;
}

.alb-tit {
    position: relative;
    height: 100rpx;
    line-height: 100rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
}

.alb-tit text {
    display: inline-block;
    position: relative;
}

.alb-tit text::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 24rpx;
    width: 124rpx;
    height: 10rpx;
    background-color: #390ABC;
}


.ask-detail-box {
    padding-bottom: 30rpx;
}

.adb-tit {
    font-size: 48rpx;
    font-weight: bold;
    line-height: 1.6;
}

.adb-des {
    font-size: 28rpx;
    line-height: 1.8;
    padding: 20rpx 0 0;
    word-break: break-all;
}

.adb-q-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 30rpx;
}

.adb-info {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #999;
}

.adb-info text {
    display: inline-block;
    padding: 0 10rpx;
    max-width: 140rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.adb-time {
    font-size: 28rpx;
    color: #999;
}

.albl-item {
    padding: 30rpx 0;
    border-bottom: 1px solid #F3F3FF;
}

.albl-item:last-child {
    border-bottom: 0;
}

.albli-top {
    display: flex;
    align-items: center;
    padding-bottom: 20rpx;
}

.albli-top image {
    width: 60rpx;
    height: 60rpx;
    vertical-align: top;
    border-radius: 100%;
}

.albli-top text {
    padding-left: 20rpx;
    font-size: 24rpx;
}

.albli-cnt {
    line-height: 1.8;
}

.albli-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.alblif-l {
    font-size: 24rpx;
    color: #999;
}

.alblif-r {
    display: flex;
    align-items: center;
}

.alblifr-item {
    display: flex;
    align-items: center;
    height: 100rpx;
    padding: 0 30rpx;
}

.alblifr-item image {
    width: 32rpx;
    height: 32rpx;
    vertical-align: top;
}

.alblifr-item text {
    padding-left: 10rpx;
    font-size: 24rpx;
}

.rabl-item {
    height: 88rpx;
    line-height: 88rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.qa-part-fixed {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 140rpx;
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: 20rpx;
    padding-right: 20rpx;
    border-top: 1px solid #F3F3FF;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.qpf-l {
    flex: 1;
    height: 100rpx;
    background-color: #f3f3ff;
    border-radius: 100rpx;
    text-align: center;
    line-height: 100rpx;
    font-size: 28rpx;
    color: #333;
}

.qpf-r {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 30rpx;
}

.qpf-r image {
    width: 32rpx;
    height: 32rpx;
    vertical-align: top;
}

.qpf-r text {
    font-size: 24rpx;
    padding-top: 10rpx;
}

.qpf-empty-block {
    height: 140rpx;
    padding-bottom: env(safe-area-inset-bottom);
}
</style>