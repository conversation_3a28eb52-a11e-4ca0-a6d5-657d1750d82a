<template>
    <view class="content">
        <!-- 搜索 -->
        <navigator class="dlb-search" url="/pages/search/doc">
            <image src="../../images/icon/search-line.png"></image>
            <text>搜索资料</text>
        </navigator>

        <!-- 热门分类 -->
        <view class="hot-cat-box">
            <view class="hcb-tit">热门分类</view>
            <uv-skeletons :loading="pageLoading" animate :skeleton="hotCateSkeleton">
                <view class="hcb-cnt">
                    <navigator class="hcbc-item" url="/pages/document/category">
                        <view class="hcbci-icon">
                            <image src="../../images/doc-cat/grid-fill.png"></image>
                        </view>
                        <text class="hcbci-txt">全部分类</text>
                    </navigator>
                    <view class="hcbc-item" v-for="category in hotCategoryList" :key="category.sid"
                          @click="goDocList(category.sid)">
                        <view class="hcbci-icon">
                            <image :src="category.logo_src"></image>
                        </view>
                        <text class="hcbci-txt">{{ category.name }}</text>
                    </view>
                </view>
            </uv-skeletons>
        </view>

        <!-- 热门专题 -->
        <view class="hot-cat-box" v-if="specialList.length > 0">
            <view class="dpb-tit" style="padding: 0;">
                <view class="dpbt-txt">热门专题</view>
                <navigator class="dpbt-more" url="/pages/document/topic-list">
                    <text>查看更多</text>
                    <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                </navigator>
            </view>
            <uv-skeletons :loading="pageLoading" animate
                          :skeleton="[{type:'custom',style:'width:630rpx; height:294rpx;'}]">
                <view class="hcb-swiper">
                    <swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay"
                            :interval="interval"
                            :duration="duration">
                        <swiper-item v-for="special in specialList" :key="special.sid" @click="goSpecial(special.sid)">
                            <view class="swiper-item">
                                <image :src="special.cover_src"></image>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>
            </uv-skeletons>
        </view>

        <!-- 安全视频 -->
        <view class="doc-part-box block-video" v-if="securityList.length > 0">
            <view class="dpb-tit">
                <view class="dpbt-txt">{{ securityCateName }}</view>
                <navigator class="dpbt-more" :url="'/pages/document/list?cateId=' + securityCateId">
                    <text>查看更多</text>
                    <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                </navigator>
            </view>
            <scroll-view class="sub-sview-list navScroll" scroll-x="true">
                <view class="sub-sview-item" :class="{'cur': this.securitySupCateId === item.sid}"
                      v-for="item in securityCateList" :key="item.sid"
                      @click="switchAppointList(securityCateId, securityCateName, item.sid,4)">{{ item.name }}
                </view>
            </scroll-view>

            <view class="icb-list" v-if="securityList.length > 0">
                <view class="icbl-item" v-for="item in securityList" :key="item.sid"
                      @click="goDetail(item, 'recommend-security')">
                    <view class="icbli-video">
                        <image :src="item.cover ? item.cover_src : item.resource.video_cover ?? item.cover_src"
                               mode="aspectFill"></image>
                        <view class="icbliv-tip">
                            <text>立即播放</text>
                            <image src="../../images/icon/play-fill.png"></image>
                        </view>
                    </view>
                    <view class="icbli-tit">{{ item.title }}</view>
                    <view class="icbli-foot">
                        <view class="icblif-price">{{ item.views }}浏览</view>
                    </view>
                </view>
            </view>
            <view class="no-data-nomal-box" v-else>
                <view class="ndnb-icon">
                    <image src="../../images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无视频</text>
            </view>
        </view>

        <!-- 培训课件 -->
        <view class="doc-part-box block-cultivate">
            <view class="dpb-tit">
                <view class="dpbt-txt">{{ cultivateCateName }}</view>
                <navigator class="dpbt-more" :url="'/pages/document/list?cateId=' + cultivateCateId">
                    <text>查看更多</text>
                    <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                </navigator>
            </view>
            <scroll-view class="sub-sview-list navScroll" scroll-x="true">
                <view class="sub-sview-item" :class="{'cur': this.cultivateSupCateId === item.sid}"
                      v-for="item in cultivateCateList" :key="item.sid"
                      @click="switchAppointList(cultivateCateId, cultivateCateName, item.sid,5)">{{ item.name }}
                </view>
            </scroll-view>
            <view class="docs-list-box" v-if="cultivateList.length > 0">
                <view class="dlb-item" v-for="item in cultivateList" :key="item.sid" @click="goDetail(item)">
                    <view class="dlbi-img">
                        <doc-icon :format="item.resource?.format"/>
                    </view>
                    <view class="dlbi-cnt">
                        <view class="dlbic-tit">{{ item.title }}</view>
                        <view class="dlbic-foot">
                            <view class="dlbicf-time">时间：
                                <uni-dateformat :date="item.release_at" format="yyyy-MM-dd"></uni-dateformat>
                            </view>
                            <view class="dlbicf-pn">页数：{{ item.resource.page_count }}</view>
                        </view>
                    </view>
                </view>
            </view>

            <view class="no-data-nomal-box" v-else>
                <view class="ndnb-icon">
                    <image src="../../images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无课件</text>
            </view>

        </view>

        <!-- 编辑推荐 -->
        <view class="index-class-box block-recommends" v-if="recommendContentList.length > 0">
            <view class="icb-tit">
                <view class="icbt-txt">编辑推荐</view>
            </view>
            <uv-skeletons :loading="recommendContentLoading" animate :skeleton="docsSkeleton">
                <view class="docs-list-box">
                    <view class="dlb-item" v-for="item in recommendContentList" :key="item.sid"
                          @click="goDetail(item, 'recommend')">
                        <view class="dlbi-img">
                            <doc-icon :format="item.resource?.format"/>
                        </view>
                        <view class="dlbi-cnt">
                            <view class="dlbic-tit">{{ item.title }}</view>
                            <view class="dlbic-foot">
                                <view class="dlbicf-time">时间：
                                    <uni-dateformat :date="item.release_at" format="yyyy-MM-dd"></uni-dateformat>
                                </view>
                                <view class="dlbicf-pn" v-if="item.resource && item.resource.page_count">
                                    页数：{{ item.resource.page_count }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </uv-skeletons>
        </view>

        <!-- 热门资源 -->
        <view class="index-class-box block-hots" style="margin-bottom: 0;" v-if="hotContentList.length > 0">
            <view class="icb-tit">
                <view class="icbt-txt">热门资源</view>
            </view>
            <uv-skeletons :loading="hotContentLoading" animate :skeleton="docsSkeleton">
                <view class="docs-list-box">
                    <view class="dlb-item" v-for="item in hotContentList" :key="item.sid"
                          @click="goDetail(item, 'hot')">
                        <view class="dlbi-img">
                            <doc-icon :format="item.resource?.format"/>
                        </view>
                        <view class="dlbi-cnt">
                            <view class="dlbic-tit">{{ item.title }}</view>
                            <view class="dlbic-foot">
                                <view class="dlbicf-time">时间：
                                    <uni-dateformat format="yyyy-MM-dd" :date="item.release_at"></uni-dateformat>
                                </view>
                                <view class="dlbicf-pn" v-if="item.resource && item.resource.page_count">
                                    页数：{{ item.resource.page_count }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </uv-skeletons>
        </view>
    </view>
</template>

<script>
import DocIcon from "@/components/doc-icon.vue";
import api, {buildQuery} from "../../lib/api";
import {alert, showDelayLoading, getDetailUrl} from "../../lib/utils";
import {tabBarPageLoad} from "@/lib/tabbar.js";
import hotCateSkeleton from "@/skeletons/hot-categories.json";
import docsSkeleton from "@/skeletons/doc-list.json";

export default {
    components: {
        DocIcon
    },
    data() {
        return {
            background: '#fff',
            indicatorDots: true,
            autoplay: true,
            interval: 7000,
            duration: 500,
            hotCategoryList: [],
            specialList: [],
            recommendContentList: [],
            hotContentList: [],
            securityCateList: [],
            cultivateCateList: [],
            securityCateId: "",
            cultivateCateId: "",
            securitySupCateId: "",
            cultivateSupCateId: "",
            securityCateName: "",
            cultivateCateName: "",
            securityList: [],
            cultivateList: [],
            securityUrl: "",
            loading: true,
            pageLoading: true,
            hotCateSkeleton,
            recommendContentLoading: true,
            hotContentLoading: true,
            docsSkeleton
        }
    },
    onLoad() {
        let scrollToSelector = "";
        let loaded = false;

        Promise.all([
            this.getMaterial(),
            this.getRecommendContent(),
            this.getHotContent()
        ]).then(() => {
            if (scrollToSelector) {
                uni.pageScrollTo({selector: scrollToSelector});
            }
        }).catch(err => {
            alert(err.message);
        }).finally(() => {
            loaded = true;
        });

        uni.$on("scrollTo", params => {
            //不是指定的资料首页
            if (params.page != "document") {
                return;
            }
            //已加载完了直接滚，未加载完设个标记让加载完成后再滚
            const selector = ".block-" + params.block;
            if (loaded) {
                uni.pageScrollTo({selector});
            } else {
                scrollToSelector = selector;
            }
        });

        tabBarPageLoad();
    },
    methods: {
        getMaterial() {
            return api.get("pages/materials").then(res => {
                this.hotCategoryList = res.categories;
                this.specialList = res.specials;
                this.pageLoading = false;
                if (res.appoint_categories[0]) {
                    if (res.appoint_categories[0].name === '安全视频') {
                        this.securityCateId = res.appoint_categories[0].sid
                        this.securityCateName = res.appoint_categories[0].name
                        this.getAppointCategory(this.securityCateId, this.securityCateName)
                        this.getAppointList(this.securityCateId, this.securityCateName, 4)
                    } else {
                        this.cultivateCateId = res.appoint_categories[0].sid
                        this.cultivateCateName = res.appoint_categories[0].name
                        this.getAppointCategory(this.cultivateCateId, this.cultivateCateName)
                        this.getAppointList(this.cultivateCateId, this.cultivateCateName, 5)
                    }
                }
                if (res.appoint_categories[1]) {
                    if (res.appoint_categories[1].name === '安全视频') {
                        this.securityCateId = res.appoint_categories[1].sid
                        this.securityCateName = res.appoint_categories[1].name
                        this.getAppointCategory(this.securityCateId, this.securityCateName)
                        this.getAppointList(this.securityCateId, this.securityCateName, 4)
                    } else {
                        this.cultivateCateId = res.appoint_categories[1].sid
                        this.cultivateCateName = res.appoint_categories[1].name
                        this.getAppointCategory(this.cultivateCateId, this.cultivateCateName)
                        this.getAppointList(this.cultivateCateId, this.cultivateCateName, 5)
                    }
                }
            });
        },
        getRecommendContent() {
            return api.get("cms/material/recommend-contents").then(res => {
                this.recommendContentList = res;
                this.recommendContentLoading = false;
            });
        },
        getHotContent() {
            return api.get("cms/material/hot-contents").then(res => {
                this.hotContentList = res;
                this.hotContentLoading = false;
            });
        },
        getAppointCategory(pid, name) {
            return api.get("cms/categories", {classify: "material", sub: 1, pid: pid}).then(res => {
                switch (name) {
                    case this.securityCateName:
                        this.securityCateList = res
                        this.securityCateList.unshift({
                            "name": "推荐",
                            "sid": ""
                        });
                        break
                    case this.cultivateCateName:
                        this.cultivateCateList = res
                        this.cultivateCateList.unshift({
                            "name": "推荐",
                            "sid": ""
                        });
                        break
                }
            });
        },
        getAppointList(cateId, cateName, listRows) {
            return api.get("cms/" + cateId + "/recommend-contents", {list_rows: listRows}).then(res => {
                switch (cateName) {
                    case this.securityCateName:
                        this.securityList = res
                        this.securityUrl = "cms/" + cateId + "/recommend-contents?list_rows=4&type=video"
                        break
                    case this.cultivateCateName:
                        this.cultivateList = res
                        break
                }
            });
        },
        switchAppointList(cateId, cateName, subCateId, listRows) {
            let query = {list_rows: listRows}
            if (subCateId) {
                query.sub_cate_id = subCateId
            }
            const hideLoading = showDelayLoading("加载中", 200);
            api.get("cms/" + cateId + "/recommend-contents", query).then(res => {
                switch (cateName) {
                    case this.securityCateName:
                        this.securityList = res
                        this.securityUrl = "cms/" + cateId + "/recommend-contents"
                        query.type = 'video'
                        this.securityUrl = this.securityUrl + (this.securityUrl.includes("?") ? "&" : "?") + buildQuery(query)
                        this.securitySupCateId = subCateId ?? ""
                        break
                    case this.cultivateCateName:
                        this.cultivateList = res
                        this.cultivateSupCateId = subCateId ?? ""
                        break
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading();
            })
        },
        goSpecial(sid) {
            uni.navigateTo({
                url: "/pages/document/topic-detail?sid=" + sid
            })
        },
        goDetail(data, type = '') {
            let url = getDetailUrl(data.type_label, data.sid)
            if (url === '') {
                alert("未获取到详情地址")
                return
            }

            if (type !== '' && data.type_label === 'video') {
                switch (type) {
                    case 'recommend':
                        url += '&url=cms/material/recommend-contents'
                        break
                    case 'recommend-security':
                        console.log(this.securityUrl)
                        url += `&url=${encodeURIComponent(this.securityUrl)}`
                        break
                    case 'hot':
                        url += '&url=cms/material/hot-contents'
                        break;
                }
            }

            uni.navigateTo({
                url: url
            })
        },
        goDocList(cateId) {
            uni.navigateTo({
                url: "/pages/document/list?cateId=" + cateId
            })
        }
    }
}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx;
}

.dlb-search {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    border-radius: 12rpx;
    background-color: #fff;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.dlb-search image {
    width: 32rpx;
    height: 32rpx;
}

.dlb-search text {
    font-size: 28rpx;
    color: #999;
    padding-left: 20rpx;
}

/* 热门分类 */
.hot-cat-box {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 0 30rpx 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.hcb-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    font-weight: bold;
    height: 100rpx;
}

.hcbt-more {
    display: flex;
    align-items: center;
    justify-content: center;
}

.hcbt-more image {
    width: 32rpx;
    height: 32rpx;
}

.hcbt-more text {
    font-weight: normal;
    color: #999;
}

.hcb-cnt {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.hcbc-item {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0 20rpx;
    border-radius: 12rpx;
}

.hcbc-item image {
    width: 42rpx;
    height: 42rpx;
}

.hcbc-item text {
    font-size: 28rpx;
    padding-top: 20rpx;
    color: #333;
    max-width: 160rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
}

.hcbci-icon {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #F3F3FF;
    border-radius: 100%;
}

.swiper-item {
    display: block;
    text-align: center;
    width: 630rpx;
    height: 294rpx;
    padding: 0;
}

.swiper-item image {
    width: 630rpx;
    height: 294rpx;
    vertical-align: top;
    border-radius: 12rpx;
}


/* 推荐资料 */
.index-class-box {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.icb-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 30rpx;
}

.icbt-txt {
    font-size: 28rpx;
    font-weight: bold;
}

.icbt-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}

.icbt-more text {
    font-size: 24rpx;
    color: #999;
}

.icbt-more image {
    width: 32rpx;
    height: 32rpx;
}

.icb-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
}

.icbl-item {
    width: 300rpx;
    padding-bottom: 30rpx;
}

.icbl-item image {
    width: 300rpx;
    height: 300rpx;
    vertical-align: top;
    border-radius: 12rpx;
}

.icbli-tit {
    height: 70rpx;
    font-size: 28rpx;
    font-weight: bold;
    line-height: 35rpx;
    padding: 20rpx 0;
}

.icbli-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999;
}

/* 资料列表 */
.dlb-item {
    display: flex;
    align-items: center;
    padding: 0 30rpx 30rpx;
}

.dlbic-tit {
    height: 80rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.dlbic-foot {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #999;
    padding-top: 20rpx;
}

.dlbicf-time {
    padding-right: 30rpx;
}

.dlbi-img {
    position: relative;
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
    border-radius: 12rpx;
    border: 1px solid #e7e7e7;
}

.doc-type {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 64rpx;
    height: 64rpx;
}

.dlbi-cnt {
    flex: 1;
}

/* 安全视频框 */
.doc-part-box {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.dpb-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 20rpx 0 30rpx;
}

.dpbt-txt {
    font-size: 28rpx;
    font-weight: bold;
}

.dpbt-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}

.dpbt-more text {
    font-size: 24rpx;
    color: #999;
}

.dpbt-more image {
    width: 32rpx;
    height: 32rpx;
}

.sub-sview-list {
    white-space: nowrap;
    width: 100%;
    padding: 0 0 30rpx;
}

.sub-sview-item {
    display: inline-block;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 30rpx;
    text-align: center;
    font-size: 36rpx;
    font-size: 24rpx;
    border: 1px solid #E7E7E7;
    border-radius: 70rpx;
    margin-left: 30rpx;
    color: #666;
}

.sub-sview-item.cur {
    background-color: #390ABC;
    color: #fff;
    border: 1px solid #390ABC;
}

.icb-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 30rpx;
}

.icbl-item {
    width: 300rpx;
    padding-bottom: 30rpx;
}

.icbl-item image {
    width: 300rpx;
    height: 300rpx;
    vertical-align: top;
    border-radius: 12rpx;
}

.icbli-video {
    position: relative;
}

.icbliv-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 12rpx 0 6rpx;
    height: 40rpx;
    background-color: #fff;
    border-radius: 0 20rpx 0 12rpx;
}

.icbliv-tip text {
    font-size: 24rpx;
    color: #de572e;
    padding-right: 4rpx;
}

.icbliv-tip image {
    width: 18rpx;
    height: 18rpx;
}

.icbli-tit {
    height: 60rpx;
    font-size: 28rpx;
    font-weight: bold;
    line-height: 35rpx;
    padding: 20rpx 0 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.icbli-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}


</style>