<template>
	<view class="content">
        <view class="tl-list">
            <template v-if="list.length > 0">
                <view class="tll-item" v-for="item in list" :key="item.sid" @click="goDetail(item.sid)">
                    <view class="tlli-top">
                        <image :src="item.cover_src" mode="widthFix"></image>
                    </view>
                    <view class="tlli-mid">
                        <view class="tllim-l">
                            <image src="../../static/images/icon/file-zip-fill.png"></image>
                        </view>
                        <view class="tllim-r">
                            <view class="tllim-tit">{{ item.name }}</view>
                            <view class="tllim-foot">
                                <view class="tllimf-l">{{ item.charge_credit }}积分</view>
                                <view class="tllimf-r">{{ item.download_count }}下载</view>
                            </view>
                        </view>
                    </view>
                </view>
                <uni-load-more :status="listStatus"></uni-load-more>
            </template>


            <view class="no-data-nomal-box" v-if="listLoaded && !listLoading && list.length === 0">
                <view class="ndnb-icon">
                    <image src="../../images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无专题</text>
            </view>
        </view>
	</view>
</template>

<script>
import {alert, getDetailUrl, removeURLParameter, showDelayLoading} from "@/lib/utils";
import api from "@/lib/api";

    export default {
        onLoad() {
            this.getList()
        },
        onPullDownRefresh() {
            this.list = [];
            this.cursor = ""
            this.getList()

        },
        onReachBottom() {
            if (this.cursor) {
                this.getList()
            }
        },
        data() {
            return {
                list: [],
                cursor: "",
                listLoaded: false,
                listLoading: false,
                listEnd: false
            }
        },
        computed: {
            listStatus() {
                if (this.listLoading) {
                    return "loading";
                } else if (this.listEnd) {
                    return "noMore";
                } else {
                    return "more";
                }
            }
        },
        methods: {
            getList() {
                if (this.listLoading || this.listEnd) {
                    return;
                }
                let query = {}
                if (this.cursor) {
                    query.cursor = this.cursor
                }
                this.listLoading = true
                const hideLoading = showDelayLoading('加载中', 200)
                api.get("cms/specials", query).then(res => {
                    this.list.push(...res.data)
                    this.cursor = res.next_cursor
                    if (!res.next_cursor) {
                        this.listEnd = true
                    }
                    this.listLoaded = true
                    uni.stopPullDownRefresh();
                }).catch(err => {
                    alert(err.message)
                }).finally(() => {
                    this.listLoading = false
                    hideLoading()
                })
            },
            goDetail(sid) {
                let url = `/pages/document/topic-detail?sid=${sid}`
                uni.navigateTo({
                    url: url
                })
            },
        },
    }
</script>

<style>
page {
    background-color: #F3F3FF;
}
.tll-item {
    background-color: #fff;
    margin: 30rpx 30rpx 0;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}
.tlli-top image {
    width: 100%;
    border-radius: 12rpx 12rpx 0 0;
    vertical-align: top;
}

.tlli-mid {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 20rpx 20rpx 30rpx;
}
.tllim-l {
    padding-right: 20rpx;
}
.tllim-l image {
    width: 48rpx;
    height: 48rpx;
    vertical-align: top;
}
.tllim-r {
    flex: 1;
}
.tllim-tit {
    font-weight: bold;
    font-size: 32rpx;
    line-height: 1.6;
}
.tllim-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999;
    padding-top: 20rpx;
}
</style>
