<template>
    <view class="content">
        <!-- 支持三级分类前端 -->
        <view class="cat-main-box">
            <view class="cmb-left">
                <view class="cmb-item" :class="{cur:item.sid == this.currentCateId}" v-for="item in categoryLeverOneList" :key="item.sid" @click="chooseCategory(item.sid)">{{ item.name }}</view>
            </view>
            <view class="cmb-right">
                <navigator class="cmbr-all" :url="'/pages/document/list?cateId=' + this.currentCateId">
                    <text>全部</text>
                    <image src="../../images/icon/arrow-right-s-line.png"></image>
                </navigator>

                <view class="cmbr-sub" v-if="categoryLeverTwoList.length > 0" v-for="item in categoryLeverTwoList" :key="item.sid">
                    <navigator class="cmbrsub-tit" :url="'/pages/document/list?cateId=' + item.sid">
                        <text>{{ item.name }}</text>
                        <image src="../../images/icon/arrow-right-s-line.png"></image>
                    </navigator>
                    <view class="cmbrsub-box" v-if="item.sub">
                        <navigator class="cmbrsb-item" v-for="sub in item.sub" :key="sub.sid" :url="'/pages/document/list?cateId=' + sub.sid">{{ sub.name }}</navigator>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import {showDelayLoading, alert} from "@/lib/utils";
import api from "@/lib/api";

export default {
    onLoad(e) {
        if (e.cateId) {
            this.currentCateId = e.cateId
        }
        this.getCategoryList()
    },
    data() {
        return {
            currentCateId: undefined,
            currentCateName: undefined,
            categoryLeverOneList: [],
            categoryLeverTwoList: []
        }
    },
    watch: {
        currentCateId(newData, oldData) {
            if (newData && oldData) {
                this.currentCateName = this.categoryLeverOneList.filter(res => res.sid == newData)[0]['name']
            }
        }
    },
    methods: {
        getCategoryList() {
            const hideLoading = showDelayLoading("加载中", 200)
            let query = {classify: "material"}
            api.get("cms/categories", query).then(res => {
                this.categoryLeverOneList = res
                if (!this.currentCateId) {
                    this.currentCateId = this.categoryLeverOneList[0]['sid']
                }
                this.currentCateName = this.categoryLeverOneList.filter(res => res.sid == this.currentCateId)[0]['name']
                query.pid = this.currentCateId
                query.sub = 1
                this.getCategoryLevelTwoList(query)
            }).catch(err => {
                alert(err)
            }).finally(() => {
                hideLoading()
            })
        },
        getCategoryLevelTwoList(query) {
            const hideLoading = showDelayLoading("加载中", 200)
            api.get("cms/categories", query).then(res => {
                this.categoryLeverTwoList = res
            }).catch(err => {
                alert(err)
            }).finally(() => {
                hideLoading()
            })
        },
        chooseCategory(currentCateId) {
            this.currentCateId = currentCateId
            this.getCategoryLevelTwoList({classify: "material", pid: this.currentCateId, sub: 1})
        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
        width: 100%;
        height: 100%;
    }
    .content,
    .cat-main-box {
        width: 100%;
        height: 100%;
    }
    .cat-main-box {
        display: flex;
        justify-content: space-between;
    }
    .cmb-left {
        width: 220rpx;
        height: 100%;
        background-color: #FFF;
        overflow-y: scroll;
        border-radius: 0 12rpx 0 0;
    }
    .cmb-item {
        height: 100rpx;
        text-align: center;
        line-height: 100rpx;
        font-size: 28rpx;
    }
    .cmb-item.cur {
        background-color: #F3F3FF;
        box-sizing: border-box;
        border-left: 6rpx solid #390ABC;
        font-weight: bold;
        color: #390ABC;
    }
    .cmb-right {
        flex: 1;
        box-sizing: border-box;
        overflow-y: scroll;
        padding: 10rpx 30rpx 30rpx;
    }

    .cmb-sub-tit {
        font-size: 28rpx;
        font-weight: bold;
        padding-bottom: 20rpx;
    }
    .cmb-th-box {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    .cmbtb-item {
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 80rpx;
        padding: 0 40rpx;
        border: 1px solid #e7e7e7;
        font-size: 24rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        background-color: #fff;
    }

    .cmbr-part {
        margin-bottom: 20rpx;
    }

    /* 分类优化样式 */
    .cmbr-all {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 88rpx;
        border-radius: 12rpx;
        padding: 0 30rpx;
        margin-bottom: 30rpx;
        background-color: #FFF;
        font-size: 28rpx;
    }
    .cmbr-all image {
        width: 32rpx;
        height: 32rpx;
        vertical-align: top;
    }
    .cmbr-sub {
        background-color: #fff;
        margin-bottom: 30rpx;
        border-radius: 12rpx;
    }
    .cmbrsub-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 88rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
    }
    .cmbrsub-tit image {
        width: 32rpx;
        height: 32rpx;
    }
    .cmbrsub-box {
        border-top: 1px solid #f3f3ff;
        padding: 30rpx 0 10rpx 30rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    .cmbrsb-item {
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 80rpx;
        padding: 0 30rpx;
        border: 1px solid #e7e7e7;
        font-size: 24rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
    }
</style>