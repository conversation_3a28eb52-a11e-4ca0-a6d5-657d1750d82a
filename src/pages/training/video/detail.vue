<template>
    <view class="content" v-if="detail">
        <view class="video-top-banner">
            <sunny-video
                 ref="sunnyVideo"
                 :title=detail.title
                 :src=currentSection.video_src
                 :poster=currentSection.video_cover
                 :autoplay=autoPlay
                 :isExitFullScreen="false"
                 @play="play"
                 @timeupdate="timeUpdate"
                 @rateChange="handleRateChange"
                 @videoEnded="videoEnded"
            />
        </view>
        <view class="tiku-ads" v-if="detail.topic_id" @click="goTopic(detail.topic_id)">
            <image src="https://img.shiwusuo100.com/assets/app-static/tuijiantiku.png"></image>
        </view>
        <view class="vtb-top-cnt">
            <view class="vtbtc-l">
                <view class="vtbcl-tit">{{ detail.title }}</view>
                <view class="vtbcl-txt">
                    <text>{{ detail.resource?.learning_count }}人在学</text>
                    <text class="vtbclt-line"></text>
                    <text>{{ detail.resource?.hour }}学时</text>
                </view>
            </view>
            <button class="vtbcl-share" open-type="share">
                <image src="../../../images/icon/share-forward-2-line.png"></image>
            </button>
        </view>

        <view class="video-chapter-box" v-if="detail">
            <view class="vcb-tit">
                <view class="vcbt-item" :class="{'cur': tab === 1}" @click="this.tab = 1">课表</view>
                <view class="vcbt-item" :class="{'cur': tab === 2}" @click="this.tab = 2">资料</view>
            </view>
            <!-- 课表 -->
            <view class="vcb-cnt" v-if="tab === 1">
                <scroll-view lower-threshold="100" class="tp-list-box-scroll" scroll-y="true" :style="{maxHeight:scrollHeight+'px'}" :scroll-into-view="sectionId">
                    <view class="vcb-chapter" v-for="(chapter, index) in chapters" :key="index">
                        <view class="vcbc-tit-box" @click="showOrHideSection(chapter.show, index)">
                            <view class="vcbc-tit">{{chapter.sn}} {{ chapter.name }}<text>学时{{ chapter.hour }}</text></view>
                            <view class="vcbc-icon">
                                <image src="../../../images/icon/arrow-up-s-line.png" v-if="chapter.show === 1"></image>
                                <image src="../../../images/icon/arrow-down-s-line.png" v-else></image>
                            </view>
                        </view>
                        <template v-for="(section, sectionIndex) in chapter.sections" :key="section.sid">
                            <view class="vcbc-sub-list"  v-if="chapter.show == 1" :id="section.sid" >
                                <view class="sub-item" :class="{cur: section.sid === this.sectionId}" @click=switchCourse(section)>
                                    <view class="si-l" :class="{on: section.sid === this.sectionId}">
                                        <image class="sil-cur-icon" v-if="section.sid === this.sectionId" src="../../../images/icon/voiceprint-line.png"></image>
                                        <image class="sil-play-icon" v-else src="../../../images/icon/play-circle-line.png"></image>
                                        <text>{{section.sn}} {{ section.name }}</text>
                                    </view>
                                    <view v-if="section.see === 2" class="si-r">试看</view>
                                    <view v-if="section.see === 0" class="si-r">
                                        <image src="../../../images/icon/lock-fill.png"></image>
                                    </view>
                                </view>
                                <!--视频学习进度-->
                                <view v-if="section.progress" class="section-progress">{{ section.progress }}</view>
                            </view>
                        </template >
                    </view>
                    <view v-if="!showFooter" class="safe-bottom"></view>
                </scroll-view>
            </view>

            <!-- 资料列表 -->
            <view class="docs-list-box" v-else>
                <view class="dlb-item" v-for="(doc, index) in detail.resource.docs" :key="index" @click="preview(doc)">
                    <view class="dlbi-img">
                        <doc-icon :format="doc.file_type" />
                    </view>
                    <view class="dlbi-cnt">
                        <view class="dlbic-tit">{{ doc.filename }}</view>
                        <view class="dlbic-foot">
                            <view class="dlbicf-time">时间：<uni-dateformat :date="doc.created_at" format="yyyy-MM-dd"></uni-dateformat></view>
                        </view>
                    </view>
                </view>
            </view>

        </view>

        <!-- 底部购买按钮 -->
        <view class=".foot-area" v-if="showFooter">
            <view class="vd-empty-block"></view>
            <view class="vd-foot-box">
                <view class="vdfb-l">
                    <view class="vdfbl-top">
                        <view class="vdfb-l-coin" v-if="detail.view_limit === limitType.credit">{{ detail.charge_credit }}<text>积分</text></view>
                        <view class="vdfb-l-money" v-if="detail.view_limit === limitType.amount"><text>￥</text>{{ amountArr[0] }}<text>.{{ amountArr[1] }}</text></view>
                        <view class="vdfb-l-money" v-if="detail.view_limit === limitType.credit_amount">{{ detail.charge_credit }}<text>积分/</text><text>￥</text>{{ amountArr[0] }}<text>.{{ amountArr[1] }}</text></view>
                    </view>
                    <view class="vdfbl-foot">有效期一年</view>
                </view>
                <view class="vdfb-r">
                    <navigator url="/pages/me/service" class="custom-service">
                        <image src="../../../images/icon/customer-service-line.png"></image>
                        <text>客服</text>
                    </navigator>

                    <!-- 未登录情况 -->
                    <view class="vdfb-btn" v-if="!user" @click="login">点击登录</view>

                    <view class="vdfb-btn" v-if="user && detail.view_limit === limitType.credit" @click="buy">立即购买</view>
                    <view class="vdfb-btn" v-if="user && detail.view_limit === limitType.amount" @click="buy">立即支付</view>
                    <view class="vdfb-btn" v-if="user && detail.view_limit === limitType.credit_amount" @click="buy">立即购买</view>
                </view>

            </view>
        </view>
    </view>

    <pay-alert ref="payAlert" :order="order" :credit="detail?.charge_credit" :allow-types="allowTypes" @creditPay="onCreditPay" @createdOrder="onCreatedOrder" @paymentCallback="onPaymentCallback" title="购买课程"></pay-alert>
    <uni-popup :mask-click="false" ref="contact" type="center">
        <view class="photo-var-box">
            <view class="pvb-tit">身份验证</view>
            <view class="pvb-cnt">
                确保照片包含完整的五官，无遮挡或缺失，以便准确识别面部特行
            </view>
            <view class="pvb-save">
                <view @click="back" class="pvb-btn cancle">返回</view>
                <view @click="goPhotos" class="pvb-btn go-ver">去验证</view>
                <!-- <button>返回</button>
                <navigator :url="`/packageOrg/organization/photos?scene=${this.scene}&org_sid=${this.orgId}&is_pc=0`" >去拍照</navigator> -->
            </view>
        </view>
    </uni-popup>
</template>

<script>
import {showDelayLoading, alert} from "@/lib/utils";
import api from "@/lib/api";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {loginRequired} from "@/lib/login"
import {getAllowTypes} from "@/lib/pay";
import {getReferral} from "@/lib/context";
import {ContentViewLimit as limitType} from "@/lib/enums";
import PayAlert from "@/components/pay-alert.vue";
import DocIcon from "@/components/doc-icon.vue";
import SunnyVideo from "@/components/video-player/sunny-video.vue";
import { handleFileDownload } from "@/lib/fileDownload";

export default {
    components: {
        DocIcon,
        PayAlert,
        SunnyVideo
    },
    onLoad(e) {
        if (!e.sid) {
            uni.showModal({
                content: "参数错误",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        uni.navigateBack()
                    }
                }
            })
        }
        if (e.org_sid) {
            this.orgId = e.org_sid
        }
        if (e.enroll_sid) {
            this.enrollSid = e.enroll_sid
        }
        if (e.need_photo) {
            this.needPhoto = e.need_photo == "1" ? true : false
        }
        this.sid = e.sid
        this.init()
        uni.$on('logged-in', () => {
            this.init()
        });
        uni.$on('photos-pic-update', () => {
            if (this.currentSection.video_src) {
                this.$refs.sunnyVideo.changePlay()
            } else {
                this.getSections()
            }
            this.$refs.contact.close()
        })
    },
    onUnload() {
        uni.$off('logged-in')
        uni.$off('photos-pic-update')
    },
    onShow() {
        // 从info页面返回时检查照片状态
        if (this.orgId && this.sectionId && this.needPhoto && this.hasPhoto) {
            // 如果有照片且当前有视频，继续播放
            if (this.currentSection.video_src) {
                this.$refs.sunnyVideo.changePlay()
            } else {
                this.getSections()
            }
        }
    },
    onShareAppMessage(res) {
        if (res.from === 'button') {// 来自页面内分享按钮
            console.log(res.target)
        }
        let path = "/pages/training/video/detail?sid=" + this.detail.sid;

        if (this.user) {
            path += "&ref=" + this.user.uuid;
        }

        return {
            title: this.detail.title,
            path
        };
    },
    data() {
        return {
            detail: undefined,
            chapters: [],
            sid: "",
            orgId: '',
            sectionId: "",
            enrollSid: "",
            scene: "",
            amountArr: [],
            currentSection: {},
            reportSecond: 10,
            currentPlaySecond: 0,
            currentPos: 0,
            timeupdateNum: 0,
            autoPlay: true,
            buyType: 'credit',
            tab: 1,
            systemPlatform: uni.getSystemInfoSync().platform,
            purchase: false,
            scrollHeight: 0,
            order: {},
            allowTypes: [],
            needPhoto: false,
            hasPhoto: false,
            currentRate: 1.0
        }
    },
    computed: {
        limitType() {
            return limitType
        },
        ...mapState(useUserStore, ['user']),
        showFooter() {
            return !this.detail.download && this.purchase;
        }
    },
    methods: {
        ...mapActions(useUserStore, ["reload"]),
        login() {
            let url = "/pages/login/index"
            uni.navigateTo({
                url
            })
        },
        init() {
            const hideLoading = showDelayLoading("加载中", 200)
            let data = {}
            if (this.enrollSid) {
                data.enroll_sid =  this.enrollSid
            }
            if (this.orgId) {
                data.org_sid = this.orgId
            }

            if (this.needPhoto) {
                api.get(`orgs/${this.orgId}/my-enrollment`).then(res => {
                    console.log(res)
                    if (res.photo) {
                        this.hasPhoto = true
                    }
                });
            }

            api.get("cms/contents/" + this.sid, data).then(res => {
                if (res.resource?.scene) {
                    this.scene = res.resource?.scene
                    this.autoPlay = false
                    this.$refs.contact.open()
                }
                if (res.resource.chapters.length) {
                    if (res.resource?.current_section) {
                        this.sectionId = res.resource?.current_section.sid
                    } else {
                        this.sectionId = res.resource.chapters[0]['sections'][0]['sid']
                        this.currentPos = 0
                    }
                    this.chapters = res.resource.chapters
                    this.chapters.forEach(item => {
                        //let section = item.sections.find(res => res.sid ==this.sectionId)
                        item.show = 1
                    })
                    console.log(this.chapters)
                    this.detail = res
                    this.purchase = res.purchase;
                    this.getDetail(hideLoading)
                } else {
                    uni.showModal({
                        content: "该课程暂无内容",
                        showCancel: false,
                        success: res => {
                            if (res.confirm) {
                                uni.navigateBack()
                            }
                        }
                    })
                    hideLoading()
                }
            }).catch(err => {
                hideLoading()
                alert(err.message)
            })

            if (!this.user) {
                this.autoPlay = false
            }
        },

        checkPhoto() {
            if (!this.needPhoto || this.hasPhoto) {
                return Promise.resolve(true);
            }
            return new Promise((resolve) => {
                uni.showModal({
                    content: "您尚未上传证件照，为了保证您的课程正常学习，请尽快完善",
                    showCancel: true,
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateTo({
                                url: `/packageOrg/organization/me/info`,
                            });
                        } else {
                            uni.navigateBack()
                        }
                    }
                });
            });
        },

        shareReward(uuid) {
            let data = {uuid: uuid, business_type: this.detail.business_type, business_id: this.detail.sid}
            api.post("invitations/share-ref", data).then(res => {

            }).catch(err => {
                console.log(err.message)
            })
        },
        getDetail(hideLoading) {
            hideLoading()
            this.order.total_amount = this.detail.charge_amount
            this.allowTypes = getAllowTypes(this.detail.view_limit)

            this.amountArr = this.getAmount(this.detail.charge_amount)
            this.chapters.forEach(chapter => {
                chapter.show = 1
                if (chapter.sections && !this.orgId) {
                    chapter.sections.forEach(section => {
                        if (section.current === 1 && section.see > 0) {
                            this.currentSection = section
                            setTimeout(() => {
                                this.$refs.sunnyVideo.seek(this.currentPos)
                                // 更新播放次数
                                this.updateVideo(this.currentPos, 'play')
                            }, 500)
                        }
                    })
                }
            })
            if (this.user) {
                if (this.detail.download) {
                    this.getSections()
                } else {
                    this.chapters.forEach(chapter => {
                        let section = chapter.sections.find(res => res.sid == this.sectionId)
                        if (section && section.see == 2) {
                            this.getSections()
                        }
                    })
                }
            }

            let uuid = getReferral(true)
            if (uuid !== '') {
                this.shareReward(uuid)
            }

            //确定滚动区域高度
            this.$nextTick(() => {
                const res = uni.getSystemInfoSync();
                const query = uni.createSelectorQuery().in(this);
                query.select('.tp-list-box-scroll').boundingClientRect(data => {
                    if (data) {
                        if (this.showFooter) {
                            const footerQuery = uni.createSelectorQuery().in(this);
                            footerQuery.select('.foot-area').boundingClientRect(fd => {
                                if (fd) {
                                    this.scrollHeight = res.windowHeight - data.top - fd.height;
                                }
                            }).exec();
                        } else {
                            this.scrollHeight = res.windowHeight - data.top;
                        }
                    }
                }).exec();
            });
        },
        convertNumbers(num) {
            const numberToChinese = {
                0: '零',
                1: '一',
                2: '二',
                3: '三',
                4: '四',
                5: '五',
                6: '六',
                7: '七',
                8: '八',
                9: '九'
            };
            return num.toString().split('').map(digit => numberToChinese[digit]).join('');
        },
        getAmount(number) {
            return number.toString().split('.')
        },
        play() {
            this.$nextTick(() => {
                if (!this.user) {
                    this.$refs.sunnyVideo.changePause()
                    loginRequired()
                    return
                }
                this.checkSee()
            })
        },
        async switchCourse(section) {
            await this.checkPhoto()

            if (!this.user) {
                this.$refs.sunnyVideo.changePause()
                loginRequired()
                return
            }

            this.currentSection = section
            this.sectionId = section.sid
            this.autoPlay = false
            if (!this.checkSee()) {
                return
            }
            this.getSections()
        },
        async getSections() {
            await this.checkPhoto()

            let data = {org_sid: this.orgId}
            if (this.enrollSid) {
                data.enroll_sid =  this.enrollSid
            }
            api.get(`v2/cms/contents/${this.sid}/sections/${this.sectionId}`, data).then(res => {
                    let section = res
                    if (!section.scene && !section.video_src) {
                        // uni.showModal({
                        //     content: "接口数据错误",
                        //     showCancel: false
                        // })
                        return
                    }
                    if (section.scene) {
                        this.scene  = section.scene
                        this.$refs.contact.open()
                    } else {
                        if (section.video_src) {
                            this.currentSection = section
                            this.currentPos = res.pos
                            this.getProgress()
                        }
                    }
            }).catch(err => {
                if (err.code == 406) {
                    this.scene = err.header("X-Scene");
                    this.$refs.sunnyVideo.changePause()

                    this.$refs.contact.open()
                } else {
                    alert(err.message)
                }
            })
        },
        checkSee() {
            if (this.currentSection.see === 0) {
                this.$refs.sunnyVideo.changePause()
                if (!this.user) {
                    loginRequired()
                } else {
                    alert("付费课程请先购买")
                }
                return false
            }
            return true
        },
        preview(doc) {
            if (!this.user) {
                loginRequired()
                return
            }
            if (!this.detail.download) {
                alert("付费课程请先购买")
                return
            }
            uni.showLoading({
                title: '加载中...',
                mask: true
            });

            let filePath = "";

            // #ifdef MP-WEIXIN
            //使用 filePath 可以保证分享出去的文件名，但是弊端是保存的文件会存在微信的用户空间，不会被系统当成临时文件自动清理
            filePath = wx.env.USER_DATA_PATH + '/' + doc.filename + '.' + doc.filepath.split('.').pop();
            // #endif
            let url = ""
            if (this.orgId) {
                url = `?enroll_sid=${this.enrollSid}&org_sid=${this.orgId}`
            }
            api.get(`cms/contents/${this.detail.sid}/docs/${doc.sid}/download${url}`).then(res => {
                // Windows 端特殊处理
                if (this.systemPlatform === 'windows') {
                    uni.downloadFile({
                        url: doc.filepath_src,
                        success: (res) => {
                            if (res.statusCode === 200) {
                                // 保存文件到本地
                                wx.saveFileToDisk({
                                    filePath: res.tempFilePath,
                                    success: () => {
                                        uni.hideLoading();
                                    },
                                    fail: err => {
                                        uni.hideLoading();
                                        uni.showModal({
                                            title: '下载文件失败',
                                            content: err.errMsg,
                                            showCancel: false
                                        });
                                    }
                                });
                            }
                        },
                        fail(err) {
                            uni.hideLoading();
                            uni.showModal({
                                title: '下载文件失败',
                                content: err.errMsg,
                                showCancel: false
                            });
                        }
                    });
                } else {
                    handleFileDownload(doc.filepath_src, filePath);
                }
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            })
        },
        download(url, filePath) {
            const task = uni.downloadFile({
                url: url,
                filePath,
                success(res) {
                    uni.hideLoading()
                    uni.openDocument({
                        filePath: res.tempFilePath || res.filePath,
                        showMenu: true,
                        success: () => {},
                        fail: err => {
                            uni.showModal({
                                title: '打开文件失败',
                                content: err.errMsg,
                                showCancel: false
                            });
                        }
                    });
                },
                fail(err) {
                    uni.hideLoading();
                    uni.showModal({
                        title: '下载文件失败',
                        content: err.errMsg,
                        showCancel: false
                    });
                }
            });

            task.onProgressUpdate(res => {
                uni.showLoading({
                    title: '加载中 ' + res.progress + '%',
                    mask: true
                });
            });
        },
        handleRateChange(rate) {
            this.currentRate = rate;
            // 根据倍速调整上报间隔
            this.reportSecond = Math.max(1, Math.round(10 / rate));
        },
        timeUpdate(e) {
            this.timeupdateNum++
            if (this.timeupdateNum === 4) {
                this.timeupdateNum = 0;
                this.currentPlaySecond++
            }
            if (this.currentPlaySecond === this.reportSecond) {
                console.log('上报视频播放进度', this.currentRate)
                this.updateVideo(Math.round(e.detail.currentTime), 'update')
                this.currentPlaySecond = 0
            }
        },
        videoEnded() {
            this.updateVideo(0, 'update')
            // 自动播放下一个视频
            this.playNextVideo()
        },
        playNextVideo() {
            let section = null
            this.chapters.forEach((chapter, chapterIndex) => {
                chapter.section.forEach((item, sectionIndex) => {
                    if (item.sid === this.sectionId) {
                        if (chapter.section[sectionIndex + 1]) {
                            section = chapter.section[sectionIndex + 1]
                        } else if (this.chapters[chapterIndex + 1].section[0]) {
                            section = this.chapters[chapterIndex + 1].section[0]
                        }
                    }
                })
            })

            if (section) {
                this.switchCourse(section)
            }
        },
        getProgress() {
            const hideLoading = showDelayLoading("加载中", 200)
            let data = {org_sid: this.orgId, type: 'play'}
            if (this.enrollSid) {
                data.enroll_sid =  this.enrollSid
            }

            api.get("cms/contents/" + this.sid + "/progress/" + this.sectionId, data).then(res => {
                this.currentPos = res.pos
                this.$refs.sunnyVideo.seek(this.currentPos)
                this.$refs.sunnyVideo.changePlay()
                // 更新播放次数
                this.updateVideo(this.currentPos, 'play')
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        updateVideo(view_position, type) {
            let data = {view_position: view_position, type: type,org_sid: this.orgId}
            if (this.enrollSid) {
                data.enroll_sid =  this.enrollSid
            }
            api.put("cms/content-sections/" + this.sectionId + "/progress", data).then(res => {

            }).catch(err => {
                if (err.code == 406) {
                    this.scene = err.header("X-Scene");
                    this.$refs.sunnyVideo.changePause()

                    this.$refs.contact.open()
                } else if (err.code === 412) {
                    this.$refs.sunnyVideo.changePause()
                    uni.showModal({

                        content: "您已在其他设备上学习该课程，确定要在当前设备上学习吗？",
                        success: res => {
                            if (res.confirm) {
                                this.$refs.sunnyVideo.changePlay()
                                this.updateVideo(this.currentPos, 'play')
                            } else {
                                uni.navigateBack()
                            }
                        }
                    })
                }  else {
                    alert(err.message)
                }
                console.log(err.message)
            })
        },
        showOrHideSection(show, index) {
            this.chapters[index].show = show ? 0 : 1
        },
        buy() {
            loginRequired().then(() => {
                if (this.allowTypes.length === 0) {
                    uni.showToast({
                        title: "当前内容无需购买",
                        icon: "none",
                        mask: true
                    });
                    return
                }
                if (this.orgId) {
                    this.$refs.payAlert.buyFromAmount()
                    return
                }
                if (this.detail.view_limit === this.limitType.credit) {
                    this.$refs.payAlert.buyFromCredit()
                } else if (this.detail.view_limit === this.limitType.amount && !this.user.balance_show) {
                    this.$refs.payAlert.buyFromAmount()
                } else {
                    this.$refs.payAlert.open()
                }
            })
        },
        onCreatedOrder(buyType) {
            const hideLoading = showDelayLoading("")
            let url = "cms/contents/" + this.detail.sid + "/buy-order";
            if (this.orgId) {
                url += "?org_sid=" + this.orgId + "&enroll_sid=" + this.enrollSid;
            }
            api.post(url).then(res => {
                for (let key in res) {
                    this.$set(this.order, key, res[key])
                }
                if (buyType == 'amount') {
                    this.$refs.payAlert.payFromAmount()
                } else {
                    this.$refs.payAlert.payFromBalance()
                }
            }).catch(e => {
                uni.showModal({
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                hideLoading()
            })
        },
        onCreditPay() {
            // 扣除对应积分
            const hideLoading = showDelayLoading("购买中", 200)
            api.post("cms/contents/" + this.detail.sid + "/credit").then(res => {
                uni.showToast({
                    title: "购买成功",
                    icon: "success"
                });
                this.$refs.payAlert.close()
                hideLoading()
                this.init()
                this.reload()
            }).catch(err => {
                hideLoading()
                if (err.code === 403) {
                    this.$refs.payAlert.creditNotEnough(err.message)
                } else {
                    alert(err.message)
                }
            })
        },
        onPaymentCallback() {
            this.init()
        },
        goTopic(topicId) {
            uni.navigateTo({
                url: "/pages/training/exam/index?topic_id=" + topicId
            })
        },
        goPhotos() {
            uni.navigateTo({
                url: `/packageOrg/organization/photos?scene=${this.scene}&org_sid=${this.orgId}&is_pc=0`
            })
        },
        back() {
            uni.navigateBack()
        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .video-top-banner image {
        width: 750rpx;
        height: 330rpx;
        vertical-align: top;
    }
    .vtb-top-cnt {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        border-bottom: 1px solid #F3F3FF;
        padding: 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .vtbtc-l {
        flex: 1;
    }
    .vtbcl-tit {
        font-weight: bold;
        font-size: 36rpx;
    }
    .vtbcl-txt {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }
    .vtbclt-line {
        position: relative;
        padding-right: 20rpx;
        margin-left: 20rpx;
    }
    .vtbclt-line::after {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        width: 1px;
        height: 20rpx;
        background-color: #cccccc;
        transform: translateY(-50%);
    }
    .vtbcl-txt text:first-child {
        padding-left: 0;
    }
    .vtbcl-share {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 100rpx;
    }
    .vtbcl-share image {
        width: 36rpx;
        height: 36rpx;
    }

    .video-chapter-box {
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .vcb-tit {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f3f3ff;
    }
    .vcbt-item {
        padding: 30rpx;
        color: #333;
    }
    .vcbt-item.cur {
        position: relative;
        font-weight: bold;
        color: #333;
    }
    .vcbt-item.cur::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        width: 40rpx;
        margin-left: -20rpx;
        height: 6rpx;
        background-color: #000;
    }

    .vcbc-tit-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        font-size: 24rpx;
        padding: 0 30rpx;
        border-bottom: 1px solid #f3f3ff;
    }
    .vcbc-tit {
        color: #999;
    }
    .vcbc-tit text {
        padding-left: 20rpx;
        font-size: 24rpx;
    }
    .vcbc-icon image {
        width: 32rpx;
        height: 32rpx;
    }

    .sub-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
    }
    .sub-item.cur {
        background: linear-gradient(to right, #F3F3FF,#fff);
    }
    .sub-item image {
        width: 36rpx;
        height: 36rpx;
        margin-top: 4rpx;
    }
    .si-l {
        flex: 1;
        display: flex;
        align-items: center;
    }
    .si-l text {
        flex: 1;
        padding-left: 20rpx;
    }
    .si-l.on text {
        font-weight: bold;
    }
    .si-r {
        font-size: 28rpx;
        color: #333;
    }

    .section-progress {
        font-size: 26rpx;
        color: #ff8c00;
        padding: 8rpx 30rpx 0;
        margin-top: 8rpx;
    }

    .safe-bottom {
        height: env(safe-area-inset-bottom);
    }

    .vd-foot-box {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 120rpx;
        background-color: #fff;
        padding-bottom: env(safe-area-inset-bottom);
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .vdfb-l {
        padding-left: 30rpx;
        font-size: 48rpx;
        font-weight: bold;
        color: red;
    }
    .vdfb-l text {
        font-size: 24rpx;
    }
    .vdfb-r {
        display: flex;
        align-items: center;
    }
    .vdfbl-foot {
        font-size: 24rpx;
        font-weight: normal;
    }
    .custom-service {
        height: 120rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 40rpx;
    }
    .custom-service image {
        width: 38rpx;
        height: 38rpx;
    }
    .custom-service text {
        font-size: 24rpx;
        padding-top: 6rpx;
    }
    .vdfb-btn {
        height: 120rpx;
        padding: 0 60rpx;
        background-color: red;
        color: #fff;
        line-height: 120rpx;
        font-weight: bold;
    }
    .vd-empty-block {
        height: 120rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .tiku-ads image {
        width: 750rpx;
        height: 100rpx;
        vertical-align: top;
    }

    .tp-list-box-scroll {
        max-height: 600rpx;
    }

    /* 资料列表 */
    .docs-list-box {
        position: relative;
        padding: 0 30rpx 30rpx;
    }
    .dlb-item {
        display: flex;
        align-items: center;
        padding: 30rpx 0 0;
        border-radius: 12rpx;
    }
    .dlbic-tit {
        height: 80rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        font-weight: bold;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    .dlbic-foot {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }
    .dlbicf-time {
        padding-right: 30rpx;
    }

    .dlbi-img {
        position: relative;
        width: 140rpx;
        height: 140rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        border: 1px solid #e7e7e7;
    }
    .doc-type {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        width: 64rpx;
        height: 64rpx;
    }
    .dlbi-cnt {
        flex: 1;
    }


    /* 拍照弹窗 */
    .photo-var-box {
        width: 500rpx;
        background-color: #fff;
        border-radius: 20rpx;
        text-align: center;
        padding: 40rpx;
    }
    .pvb-tit {
        font-size: 32rpx;
        font-weight: bold;
    }
    .pvb-cnt {
        padding: 40rpx 0;
        text-align: center;
        font-size: 28rpx;
        line-height: 1.6;
    }
    .pvb-save {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .pvb-save .pvb-btn {
        height: 90rpx;
        line-height: 90rpx;
        background-color: #390ABC;
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
        text-align: center;
        margin-left: 20rpx;
        border-radius: 12rpx;
    }
    .pvb-save .pvb-btn.cancle {
        background-color: #f3f3f3;
        color: #333;
        margin-right: 20rpx;
        padding: 0 60rpx;
    }
    .pvb-save .pvb-btn.go-ver {
        flex: 1;
    }
</style>