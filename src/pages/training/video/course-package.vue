<template>
    <view class="content">
        <view class="top-course-package">
            <uv-skeletons :loading="contentLoading" animate :skeleton="coverSkeleton">
                <image :src="content.cover_src" mode="widthFix"></image>
            </uv-skeletons>
            <view class="tcp-foot">
                <view class="tcpf-l">{{ content.title || '课程包' }}</view>
                <view class="tcpf-r">学习进度：{{ contentLoading ? '...' : content.resource?.progress_percent }}%</view>
            </view>
        </view>
        <uv-skeletons :loading="contentLoading" animate :skeleton="coursesOddSkeleton">
            <view class="icb-list">
                <view class="icbl-item" v-for="item in courses" :key="item.sid" @click="goCourse(item.sid)">
                    <view class="icbli-video">
                        <image :src="item.cover_src" mode="widthFix"></image>
                        <view class="icbliv-tip">
                            <text>立即学习</text>
                            <image src="../../../images/icon/play-fill.png"></image>
                        </view>
                    </view>
                    <view class="icbli-tit">{{ item.title }}</view>
                    <view class="icbli-foot">
                        <view class="icblif-price">
                            学习进度：{{ item.resource.progress_percent }}%
                        </view>
                    </view>
                </view>
            </view>
        </uv-skeletons>

        <!-- 底部购买按钮 -->
        <view v-if="showFootbar">
            <view class="vd-empty-block"></view>
            <view class="vd-foot-box">
                <view class="vdfb-l">
                    <!-- <view class="vdfb-l-coin">30<text>积分</text></view>
                    <view class="vdfb-l-money"><text>￥</text>3<text>.00</text></view> -->
                    <view class="vdfb-l-money" v-if="content.view_limit !== limitType.free"><template v-if="content.view_limit === limitType.credit || content.view_limit === limitType.credit_amount">{{ content.charge_credit }}<text>积分</text></template><text v-if="content.view_limit === limitType.credit_amount">/</text><template v-if="content.view_limit === limitType.amount || content.view_limit === limitType.credit_amount"><text>￥</text>{{ Math.floor(content.charge_amount) }}<!-- <text>.{{ content.charge_amount.split('.')[1] }}</text> --></template></view>
                    <view class="vdfbl-foot">有效期一年</view>
                </view>

                <view class="vdfb-r">
                    <navigator url="/pages/me/service" class="custom-service">
                        <image src="../../../images/icon/customer-service-line.png"></image>
                        <text>客服</text>
                    </navigator>

                    <!-- 未登录情况 -->
                    <view class="vdfb-btn" v-if="!user" @click="login">点击登录</view>

                    <view class="vdfb-btn" v-else @click="buy">立即购买</view>
                    <!-- <view class="vdfb-btn" v-if="user && detail.view_limit === limitType.amount" @click="buy">立即支付</view>
                    <view class="vdfb-btn" v-if="user && detail.view_limit === limitType.credit_amount" @click="buy">立即购买</view> -->
                </view>

            </view>
        </view>
    </view>

    <pay-alert ref="payAlert" :order="order" :credit="content?.charge_credit" :allow-types="allowTypes" @creditPay="onCreditPay" @createdOrder="onCreatedOrder" @paymentCallback="onPaymentCallback" title="购买课程包"></pay-alert>

</template>

<script>
import api from '@/lib/api';
import { mapState } from "pinia";
import { useUserStore } from "@/store/user";
import coursesOddSkeleton from "@/skeletons/course-list-odd.json";
import { loginRequired } from '@/lib/login.js';
import { ContentViewLimit } from '@/lib/enums.js';
import PayAlert from "@/components/pay-alert.vue";
import {getAllowTypes} from "@/lib/pay";
import {alert, showDelayLoading} from "@/lib/utils";

export default {
    components: {
        PayAlert
    },
    data() {
        return {
            sid: '',
            content: {},
            contentLoading: true,
            paymentType: 'credit',
            coverSkeleton: [
                {
                    "type": "custom",
                    "style": "width: 100%; height: 520rpx;"
                }
            ],
            coursesOddSkeleton,
            limitType: ContentViewLimit,
            order: {},
            allowTypes: [],
            orgSid: '',
            enrollSid: '',
        };
    },
    computed: {
        courses() {
            return this.content.resource?.courses || [];
        },
        ...mapState(useUserStore, ["user", "reload"]),
        showFootbar() {
            if (this.contentLoading) {
                return false;
            } else if (this.content.view_limit === this.limitType.free) {
                return false;
            } else {
                return !this.content.download;
            }
        }
    },
    onLoad(options) {
        this.sid = options.sid;
        if (options.enroll_sid) {
            this.enrollSid = options.enroll_sid;
        }
        if (options.org_sid) {
            this.orgSid = options.org_sid;
        }
        this.loadDetail();
    },
    methods: {
        loadDetail() {
            console.log('orgSid',this.orgSid)
            let url = ""
            if (this.orgSid) {
                url = "?org_sid=" + this.orgSid
            }
            api.get('cms/contents/' + this.sid + url).then(data => {
                this.content = data;
                this.contentLoading = false;
                this.order.total_amount = this.content.charge_amount
                this.allowTypes = getAllowTypes(this.content.view_limit)
            });
        },
        login() {
            loginRequired(false).then(this.loadDetail, () => {});
        },
        buy() {
            loginRequired().then(() => {
                if (this.allowTypes.length === 0) {
                    uni.showToast({
                        title: "当前内容无需购买",
                        icon: "none",
                        mask: true
                    });
                    return
                }
                if (this.content.view_limit === this.limitType.credit) {
                    this.$refs.payAlert.buyFromCredit()
                } else if (this.content.view_limit === this.limitType.amount && !this.user.balance_show) {
                    this.$refs.payAlert.buyFromAmount()
                } else {
                    this.$refs.payAlert.open()
                }
            })
        },
        onCreatedOrder(buyType) {
            const hideLoading = showDelayLoading("")
            api.post("cms/contents/" + this.content.sid + "/buy-order", {org_sid: this.orgSid}).then(res => {
                for (let key in res) {
                    this.$set(this.order, key, res[key])
                }
                if (buyType == 'amount') {
                    this.$refs.payAlert.payFromAmount()
                } else {
                    this.$refs.payAlert.payFromBalance()
                }
            }).catch(e => {
                uni.showModal({
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                hideLoading()
            })
        },
        onCreditPay() {
            // 扣除对应积分
            const hideLoading = showDelayLoading("购买中", 200)
            api.post("cms/contents/" + this.content.sid + "/credit").then(res => {
                uni.showToast({
                    title: "购买成功",
                    icon: "success"
                });
                this.$refs.payAlert.close()
                this.loadDetail();
                this.reload()
            }).catch(err => {
                if (err.code === 403) {
                    this.$refs.payAlert.creditNotEnough(err.message)
                } else {
                    alert(err.message)
                }
            }).finally(() => {
                hideLoading()
            })
        },
        onPaymentCallback() {
            this.loadDetail();
        },
        goCourse(sid) {
            uni.navigateTo({
                url: '/pages/training/video/detail?sid=' + sid + '&org_sid=' + this.orgSid + '&enroll_sid=' + this.enrollSid
            });
        }
    }
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .top-course-package {
        position: relative;
    }
    .top-course-package image {
        width: 100%;
        vertical-align: top;
    }
    .tcp-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 80rpx;
        padding: 0 30rpx;
        background-color: #fff;
        color: #333;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }


    /* 课程列表 */
    .icb-list {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30rpx;
        padding: 30rpx 30rpx 0;
    }
    .icbl-item {
        padding-bottom: 30rpx;
        background-color: #fff;
        border-radius: 12rpx;
    }
    .icbl-item image {
        width: 100%;
        height: 300rpx;
        vertical-align: top;
        border-radius: 12rpx;
    }
    .icbli-video {
        position: relative;
    }
    .icbliv-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 0 12rpx 0 6rpx;
        height: 40rpx;
        background-color: #fff;
        border-radius: 0 20rpx 0 12rpx;
    }
    .icbliv-tip text {
        font-size: 24rpx;
        color: #de572e;
        padding-right: 4rpx;
    }
    .icbliv-tip image {
        width: 18rpx;
        height: 18rpx;
    }
    .icbli-tit {
        height: 70rpx;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 35rpx;
        padding: 20rpx;
    }
    .icbli-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
        padding: 0 20rpx;
    }

    /* 底部购买菜单 */
    .vd-foot-box {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 120rpx;
        background-color: #fff;
        padding-bottom: env(safe-area-inset-bottom);
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .vdfb-l {
        padding-left: 30rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: red;
    }
    .vdfb-l text {
        font-size: 24rpx;
    }
    .vdfb-r {
        display: flex;
        align-items: center;
    }
    .vdfbl-foot {
        font-size: 24rpx;
        font-weight: normal;
    }
    .custom-service {
        height: 120rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 40rpx;
    }
    .custom-service image {
        width: 38rpx;
        height: 38rpx;
    }
    .custom-service text {
        font-size: 24rpx;
        padding-top: 6rpx;
    }
    .vdfb-btn {
        height: 120rpx;
        padding: 0 60rpx;
        background-color: red;
        color: #fff;
        line-height: 120rpx;
        font-weight: bold;
    }
    .vd-empty-block {
        height: 160rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .tiku-ads image {
        width: 750rpx;
        height: 100rpx;
        vertical-align: top;
    }

    .tp-list-box-scroll {
        max-height: 600rpx;
    }
</style>