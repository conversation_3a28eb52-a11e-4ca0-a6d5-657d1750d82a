<template>
    <view class="content">
        <view class="wp-tab">
            <view class="wpt-item cur">法律法规</view>
            <view class="wpt-item">安全管理</view>
            <view class="wpt-item">安全生产技术</view>
        </view>
        <view class="test-part-top">
            <view class="tpt-type">多选题</view>
            <view class="tpt-process"><text>1</text> / 43</view>
            <view class="tpt-btn">背题模式</view>
        </view>
        <view class="test-main-tit">
            下列关于国家领导人关于做好安全生产工作作出的重要指示，其中“安全生产红线意识”指的是（）。
        </view>
        <view class="test-main-sub type-radio type-checkbox">
            <view class="tms-item">
                <view class="tmsi-sub select wrong">A</view>
                <view class="tmsi-cnt">人命关天，发展决不能以牺牲人的生命为代价发展决不能以牺牲人的生命为代价发展决不能以牺牲人的生命为代价</view>
            </view>
            <view class="tms-item">
                <view class="tmsi-sub right">B</view>
                <view class="tmsi-cnt">安全生产，要坚持防患于未然</view>
            </view>
            <view class="tms-item">
                <view class="tmsi-sub select">C</view>
                <view class="tmsi-cnt">始终坚持中国特色社会主义路线</view>
            </view>
            <view class="tms-item">
                <view class="tmsi-sub">D</view>
                <view class="tmsi-cnt">安全生产和重大安全生产事故风险“一票否决”</view>
            </view>
        </view>

        <view class="tmt-current">正确答案：A、C</view>

        <view class="tmt-cnt">
            <view class="tmtc-tit">题目解析：</view>
            <view class="tmtc-cnt">人命关天，发展决不能以牺牲人的生命为代价，这必须作为一条不可逾越的红线</view>
        </view>


        <!-- 底部菜单 -->
        <view class="tfp-empty-block"></view>
        <view class="test-foot-part">
            <view class="tfp-item">
                <image src="../../../images/icon/arrow-left-wide-line.png"></image>
            </view>
            <view class="tfp-mid">
                <view class="tfp-item-mid">
                    <image src="../../../images/icon/delete-bin-line.png"></image>
                    <text>删除</text>
                </view>
                <view class="tfp-item-mid" @click="this.$refs.popup.open('bottom')">
                    <image src="../../../images/icon/calculator-line.png"></image>
                    <text>答题卡</text>
                </view>
                <view class="tfp-item-mid">
                    <image src="../../../images/icon/share-forward-2-line.png"></image>
                    <text>问考友</text>
                </view>
            </view>
            <view class="tfp-item">
                <image src="../../../images/icon/arrow-right-wide-line.png"></image>
            </view>
        </view>

        <!-- 答题卡 -->
        <uni-popup ref="popup" type="bottom">
            <view class="answer-sheet-box" style="display: none;">
                <view class="asb-tit">答题卡
                    <view class="asb-close">
                        <image src="../../../images/icon/close-line.png"></image>
                    </view>
                </view>
                <view class="asb-cnt">
                    <view class="asbc-tit">单选题</view>
                    <view class="asbc-list">
                        <view class="asbcl-item cur">1</view>
                        <view class="asbcl-item">2</view>
                        <view class="asbcl-item">3</view>
                        <view class="asbcl-item">4</view>
                        <view class="asbcl-item">5</view>
                        <view class="asbcl-item">6</view>
                        <view class="asbcl-item">7</view>
                        <view class="asbcl-item">8</view>
                    </view>
                    <view class="asbc-tit">多选题</view>
                    <view class="asbc-list">
                        <view class="asbcl-item">1</view>
                        <view class="asbcl-item">2</view>
                        <view class="asbcl-item">3</view>
                        <view class="asbcl-item">4</view>
                        <view class="asbcl-item">5</view>
                        <view class="asbcl-item">6</view>
                        <view class="asbcl-item">7</view>
                        <view class="asbcl-item">8</view>
                    </view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
</script>

<style>
    page {
        background-color: #FFF;
    }
    .content {
        padding: 0 30rpx 30rpx;
    }
    .test-part-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;
    }
    .tpt-type {
        background-color: #F3F3F3;
        color: #333;
        font-size: 24rpx;
        padding: 10rpx;
        border-radius: 6rpx;
    }
    .tpt-time {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .tpt-time image {
        width: 32rpx;
        height: 32rpx;
    }
    .tpt-time text {
        font-size: 24rpx;
        padding-top: 6rpx;
        color: #EA712E;
    }
    .tpt-process {
        font-size: 24rpx;
        color: #999;
    }
    .tpt-process text {
        font-weight: bold;
        color: #333;
    }
    .test-main-tit {
        font-weight: bold;
        font-size: 28rpx;
        line-height: 1.8;
        margin-bottom: 30rpx;
    }

    .tms-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 40rpx;
    }
    .tmsi-sub {
        background-color: #F3F3F3;
        width: 50rpx;
        height: 50rpx;
        border-radius: 100%;
        text-align: center;
        line-height: 48rpx;
        font-size: 24rpx;
    }
    .type-checkbox .tmsi-sub {
        border-radius: 12rpx;
    }
    .tmsi-sub.select {
        background-color: #065CDF;
        color: #fff;
    }
    .tmsi-sub.wrong {
        background-color: rgb(218, 100, 96);
        color: #fff;
    }
    .tmsi-sub.right {
        background-color: green;
        color: #fff;
    }
    .tmsi-cnt {
        flex: 1;
        font-size: 28rpx;
        padding-left: 20rpx;
        line-height: 1.8;
    }

    .tfp-empty-block {
        height: 120rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .test-foot-part {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
        display: flex;
        align-items: center;
        border-top: 1px solid #f3f3f3;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .tfp-item {
        height: 120rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 30rpx;
    }
    .tfp-item image {
        width: 48rpx;
        height: 48rpx;
    }
    .tfp-mid {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .tfp-item-mid {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 120rpx;
        padding: 0 50rpx;
    }
    .tfp-item-mid image {
        width: 36rpx;
        height: 36rpx;
    }
    .tfp-item-mid text {
        font-size: 28rpx;
        padding-top: 10rpx;
    }
    .tmt-current {
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 12rpx;
        text-align: center;
        color: green;
        background-color: #f3f3f3;
        font-size: 28rpx;
        margin-bottom: 30rpx;
    }

    .tmtc-tit {
        font-weight: bold;
        font-size: 28rpx;
        padding-bottom: 10rpx;
    }
    .tmtc-cnt {
        font-size: 28rpx;
        line-height: 1.8;
    }
    .answer-sheet-box {
        background-color: #FFF;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 5;
        border-top: 1px solid #e7e7e7;
    }
    .asb-tit {
        position: relative;
        height: 120rpx;
        line-height: 120rpx;
        text-align: center;
        font-weight: bold;
    }
    .asb-close {
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        right: 16rpx;
        top: 16rpx;
        height: 88rpx;
        width: 88rpx;
        border-radius: 100%;
    }
    .asb-close image {
        width: 38rpx;
        height: 38rpx;
    }

    .asb-cnt {
        padding: 0 30rpx 30rpx;
    }

    .asbc-tit {
        padding: 20rpx 0;
    }

    .asbc-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    .asbcl-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 88rpx;
        width: 88rpx;
        border-radius: 100%;
        border: 1px solid #e7e7e7;
        margin-right: 22rpx;
        margin-bottom: 22rpx;
        font-size: 28rpx;
    }
    .asbcl-item.cur {
        background-color: #065CDF;
        color: #fff;
        border: 1px solid #065CDF;
    }


    .tpt-btn {
        padding: 10rpx 16rpx;
        background-color: #065CDF;
        color: #fff;
        border-radius: 12rpx;
        font-size: 24rpx;
    }

    .wp-tab {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
    }
    .wpt-item {
        height: 100rpx;
        line-height: 100rpx;
        margin-right: 40rpx;
        font-size: 28rpx;
        color: #999;
    }
    .wpt-item.cur {
        position: relative;
        color: #000;
        font-weight: bold;
    }
    .wpt-item.cur::after {
        content: '';
        position: absolute;
        left: 0;
        left: 50%;
        bottom: 12rpx;
        transform: translateX(-50%);
        width: 20rpx;
        height: 8rpx;
        border-radius: 8rpx;
        background-color: #390ABC;
    }

</style>