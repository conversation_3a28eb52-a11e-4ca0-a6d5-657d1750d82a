<template>
    <view class="content">
        <view class="test-intro">
            <view class="ti-tit">{{ topic.name }}</view>
            <view class="ti-data-list">

                <view class="tidl-item">
                    <view class="tidli-num">{{topic.total_score}}<text>分</text></view>
                    <view class="tidli-txt">试卷总分</view>
                </view>
                <view class="tidl-item">
                    <view class="tidli-num">{{topic.exam_time}}<text>分钟</text></view>
                    <view class="tidli-txt">考试用时</view>
                </view>
            </view>
            <view class="ti-type-intro" v-if="topic.exam_config">
                <view class="tti-tit">题型介绍</view>
                <template v-if="topic.exam_config['judge'].count">
                    <view class="tti-h3">判断选择题</view>
                    <view class="tti-p">共{{topic.exam_config['judge'].count}}道题，每道题{{topic.exam_config['judge'].score}}.0分。每题的备选项中只有1个最符合题意。</view>
                </template>
                <template v-if="topic.exam_config['single_choice'].count">
                    <view class="tti-h3">单项选择题</view>
                    <view class="tti-p">共{{topic.exam_config['single_choice'].count}}道题，每道题{{topic.exam_config['single_choice'].score}}.0分。每题的备选项中只有1个最符合题意。</view>
                </template>
                <template v-if="topic.exam_config['multiple_choice'].count">
                    <view class="tti-h3">多项选择题</view>
                    <view class="tti-p">共{{topic.exam_config['multiple_choice'].count}}道题，每道题{{topic.exam_config['multiple_choice'].score}}.0分。每题的备选项中，有2个或2个以上符合题意，至少有1个错项，错选不得分，少选，所选的每个选项的0.5分。</view>
                </template>

            </view>
            <view class="start-test-btn" @click="start(3)">开始考试</view>
        </view>

        <view class="test-history-list">
            <view class="thl-tit">考试记录</view>
            <view class="thl-cnt">
                <uni-table border stripe emptyText="暂无更多数据">
                    <uni-tr>
                        <uni-th align="left" width="140rpx">考试时间</uni-th>
                        <uni-th align="left" width="120rpx">考试得分</uni-th>
                        <uni-th align="left" width="160rpx">操作</uni-th>
                    </uni-tr>
                    <uni-tr v-for="(item, index) in testList" :key="index">
                        <uni-td><uni-dateformat :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></uni-td>
                        <uni-td>{{ item.score }}分</uni-td>
                        <uni-td><text class="tb-link" @click="check(item)">查看</text>  <text class="tb-link" @click="delTest(item, index)" >删除</text></uni-td>
                    </uni-tr>
                </uni-table>
            </view>
        </view>
    </view>
</template>

<script>
import {showDelayLoading, alert, showToast} from "../../../lib/utils";
import api from "../../../lib/api";

export default {
    data() {
        return{
            topicSid:0,
            judge:0,
            multi:0,
            single:0,
            name:'',
            scroll_id:'',
            orgId:'',
            topic: {},
            testList: []
        }
    },
    onLoad(query) {
        if (query.org_sid) {
            this.orgId =  query.org_sid
        }
        if (query.topic_sid){
            this.topicSid = query.topic_sid
        }
        if (query.name){
            this.name = query.name
        }
        this.initData();
        this.initTopic();
        uni.$on('topic_test_complete', ()=>{
            this.testList = [];
            this.scroll_id = '';
            this.initData()
        })
    },
    onUnload() {
        uni.$off("topic_test_complete")
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.scroll_id){
            this.getCourseList();
        }
    },
    methods:{
        initData(){
            api.get("training/tests/"+this.topicSid+"/exam/record").then(res => {
                this.testList.push(...res.data)
                this.scroll_id = res.scroll_id
            }).catch(err => alert(err.message))
        },
        initTopic() {
            api.get("training/topics/"+this.topicSid).then(res => {
                this.topic = res.topic
            }).catch(err => alert(err.message))
        },
        start(type) {
            // if(this.isPurchased == false){
            //     uni.showModal({
            //         content:"请先购买该题库",
            //         showCancel: false
            //     })
            // }


            api.get("training/tests/current/"+this.topicSid+"?type=" +type).then(res => {
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.test.sid
                })

            }).catch(err =>{
                if (err.code == 404){
                    this.createTest(type)
                }else {
                    uni.hideLoading();alert(err.message)
                }
            })
        },
        createTest(type){
            const hideLoading = showDelayLoading("请稍后", 200)

            api.post("training/"+this.topicSid+"/tests",{type : type, org_sid: this.orgId}).then(res => {
                hideLoading()
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.sid
                })
            }).catch(err => {uni.hideLoading();alert(err.message)})
        },
        check(data){
           uni.navigateTo({
               url: "/pages/training/exam/end-test?test_id=" + data.sid
           })
        },
        delTest(data, index){
            uni.showModal({
                title: "删除",
                content: "确定要删除考试记录吗？",
                confirmText: "确定删除",
                success: (res) => {
                    if (res.confirm) {
                        api.delete("training/tests/"+data.sid).then(res => {
                            uni.showToast({
                                title: "删除成功",
                                icon: "none"
                            })
                            this.testList.splice(index, 1)
                        })
                    }
                }
            })
        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }

    .test-intro {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ti-tit {
        text-align: center;
        font-weight: bold;
        padding-bottom: 30rpx;
    }
    .ti-data-list {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #F3F3FF;
        border-radius: 12rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
    }
    .tidl-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .tidli-num {
        font-size: 36rpx;
        font-weight: bold;
    }
    .tidli-num text {
        font-size: 24rpx;
        color: #999;
        font-weight: normal;
        padding-left: 10rpx;
    }
    .tidli-txt {
        font-size: 28rpx;
        color: #999;
        padding-top: 10rpx;
    }

    .tti-tit {
        position: relative;
        font-weight: bold;
        padding: 10rpx 0 30rpx;
    }
    .tti-tit::after {
        content: '';
        position: absolute;
        left: 0;
        width: 126rpx;
        height: 8rpx;
        background-color: #390ABC;
        bottom: 24rpx;
    }
    .tti-h3 {
        font-size: 28rpx;
        font-weight: bold;
        padding-bottom: 10rpx;
    }
    .tti-p {
        font-size: 28rpx;
        line-height: 1.8;
        padding-bottom: 30rpx;
    }

    .start-test-btn {
        height: 100rpx;
        line-height: 100rpx;
        border-radius: 12rpx;
        background-color: #390ABC;
        color: #fff;
        text-align: center;
        font-size: 28rpx;
    }
    .test-history-list {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .thl-tit {
        position: relative;
        font-weight: bold;
        padding: 10rpx 0 30rpx;
    }
    .thl-tit::after {
        content: '';
        position: absolute;
        left: 0;
        width: 126rpx;
        height: 8rpx;
        background-color: #390ABC;
        bottom: 24rpx;
    }


    .tb-link {
        padding: 20rpx 10rpx;
        color: #390ABC;
        text-decoration: underline;
    }
</style>