<template>
    <view v-if="subjectsList.length" class="content">
        <view class="test-part-top">
            <view class="vlb-tab" v-if="test.type == 4">
                <scroll-view class="sub-sview-list navScroll" scroll-x="true">
                    <view class="sub-sview-item" @click="clickTopic(item)" :class="{'cur': item.sid == topicSid}"
                          v-for="(item, index) in topicList" :key="index">{{ item.name }}
                    </view>
                </scroll-view>
            </view>
            <view class="test-part-top-title">
                <view class="tpt-type">
                    <text v-if="subjectsTest?.type ==1">单选题</text>
                    <text v-else-if="subjectsTest?.type ==2">多选题</text>
                    <text v-else-if="subjectsTest?.type ==3">判断题</text>
                    <text v-else-if="subjectsTest?.type == 4 ">问答题</text>
                </view>
                <view class="tpt-time" v-if="test.type == 3 || test.type == 9">
                    <image src="@/images/icon/time-line.png"></image>
                    <text>{{ countdownText }}</text>
                </view>
                <view class="tpt-process" v-if="!loading">
                    <text>{{ quantity }}</text>
                    / {{ total }}
                </view>

                <view class="tpt-type-switch" v-show="!isExam && test.type != 9 "
                      @click="isRecitationQuestion = !isRecitationQuestion">
                    <view class="tptts-item" :class="{'cur': !isRecitationQuestion}">答题模式</view>
                    <view class="tptts-item" :class="{'cur': isRecitationQuestion}">背题模式</view>
                </view>
            </view>
        </view>
        <swiper class="swiperList" :autoplay="false"
                v-if="!loadingPage"
                easing-function="easeOutCubic"
                :duration="200" disable-programmatic-animation
                :current="noProdsDone" @change="eventHandle">
            <swiper-item
                :item-id="listItem.sid"
                v-for="(listItem, listIndex) in subjectsList" :key="listIndex">
                <scroll-view
                    v-if="listIndex == noProdsDone ||  (listIndex  >= noProdsDone  - 1  && listIndex  <= noProdsDone + 1 ) "
                    scroll-y="true" style="height: 100%">
                    <view style="padding-bottom: 30rpx;">
                        <template v-if="listItem.type == 4">
                            <view class="bg-info">
                                <text class="bgi-tit">「背景资料」</text>
                                <mp-html :content="listItem.example.content"></mp-html>
                            </view>
                        </template>
                        <view class="test-main-tit">
                            <mp-html :content="listItem.intro"></mp-html>
                        </view>
                        <view class="test-main-sub"
                              :class="{'type-radio': listItem.type != 2, 'type-checkbox': listItem.type == 2}">
                            <template>
                                <!--单选 多选-->
                                <template v-if="listItem?.type == 1 || listItem?.type == 2"
                                          v-for="(item, index) in listItem.option"
                                          :key="index">
                                    <view class="tms-item" @click="selectSubjectsTest(item, index)">
                                        <view class="tmsi-sub"
                                              :class="{wrong: wrongData(listItem, item), right: rightData(listItem, item),
                                              select:listItem.multiple?.indexOf(item.sid) > -1 && !isRecitationQuestion
                                              }"
                                              @click.stop="selectSubjectsTest(item, index)"
                                        >
                                            <image v-if="rightData(listItem, item) === true"
                                                   src="@/images/icon/check-line.png"></image>
                                            <image v-else-if="wrongData(listItem, item) === true"
                                                   src="@/images/icon/close-line-white.png"></image>
                                            <text v-else>{{ abcd[index] }}</text>
                                        </view>
                                        <view class="tmsi-cnt" @click.stop="selectSubjectsTest(item, index)">
                                            <mp-html :content="item.name"></mp-html>
                                        </view>
                                    </view>
                                </template>
                                <!--判断-->
                                <template v-else-if="listItem.type == 3">
                                    <template v-for="(item, index) in optionTf"
                                              :key="index">
                                        <view class="tms-item"
                                              @click="selectSubjectsTest(item, index)"
                                        >
                                            <view class="tmsi-sub"
                                                  :class="{
                                                wrong: judgeWrongData(listItem, item),
                                                right: judgeRightData(listItem, item),
                                                select:listItem.multiple?.indexOf(item.sid) > -1 && !isRecitationQuestion
                                            }"
                                            >
                                                <image v-if="judgeWrongData(listItem, item) === true"
                                                       src="@/images/icon/close-line-white.png"></image>
                                                <image v-else-if="judgeRightData(listItem, item) === true"
                                                       src="@/images/icon/check-line.png"></image>
                                                <text v-else>{{ abcd[index] }}</text>
                                            </view>
                                            <view class="tmsi-cnt">
                                                <mp-html :content="item.name"></mp-html>
                                            </view>
                                        </view>
                                    </template>
                                </template>
                            </template>
                        </view>

                        <view  class="tmt-textarea" v-if="listItem.type == 4">
                            <textarea :maxlength="-1" v-model="listItem.answerText" placeholder="请在此作答">

                            </textarea>
                        </view>
                        <view class="tmt-submit" @click="submitMultiple"
                              v-if="(!listItem.test_option?.option_id && (listItem?.type == 2 || listItem?.type == 4) && !isExam && !isRecitationQuestion) ">
                            确认提交
                        </view>

                        <view class="tmt-current"
                              v-if="(listItem.test_option?.option_id && !isExam  && listItem.type != 4) || isRecitationQuestion">
                            正确答案：{{ listItem?.rightText }}
                        </view>
                        <view class="tmt-cnt questions"
                              v-else-if="(listItem.test_option && !isExam && listItem.type == 4) || isRecitationQuestion">
                            <view class="tmtc-tit">正确答案：</view>
                            <mp-html :content="listItem?.answer"></mp-html>
                        </view>

                        <view class="tmt-cnt" v-show="listItem.analysis"
                              v-if="(listItem.test_option?.option_id && !isExam) || isRecitationQuestion || subjectsId">
                            <view class="tmtc-tit">题目解析：</view>
                            <view class="tmtc-cnt">
                                <mp-html :content="listItem.analysis"></mp-html>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </swiper-item>
        </swiper>
        <view v-else>
            <view class="test-main-tit">
                <mp-html :content="subjectsTest.intro"></mp-html>
            </view>
            <view class="test-main-sub"
                  :class="{'type-radio': subjectsTest.type != 2, 'type-checkbox': subjectsTest.type == 2}">
                <view class="tms-item" v-if="subjectsTest.type != 3" @click="selectSubjectsTest(item, index)"
                      v-for="(item, index) in subjectsTest.option" :key="index">
                    <view class="tmsi-sub" :class="{select:subjectsTest.multiple?.indexOf(item.sid) > -1 }">
                        {{ abcd[index] }}
                    </view>
                    <view class="tmsi-cnt">
                        <mp-html :content="item.name">
                        </mp-html>
                    </view>
                </view>
                <template v-else>
                    <view class="tms-item" v-for="(item, index) in optionTf"
                          @click="selectSubjectsTest(item, index)"
                          :key="index">
                        <view class="tmsi-sub" :class="{select:subjectsTest.multiple?.indexOf(item.sid) > -1 }">
                            {{ abcd[index] }}
                        </view>
                        <view class="tmsi-cnt">{{ item.name }}</view>
                    </view>
                </template>
            </view>

        </view>


        <!-- 底部菜单 -->
        <view class="tfp-empty-block"></view>
        <view class="test-foot-part">
            <view class="tfp-item" @click="clickTopTopic">
                <image src="@/images/icon/arrow-left-wide-line.png"></image>
                <text>上一题</text>
            </view>
            <view class="tfp-mid">
                <view @click.stop="delTestWrong()" class="tfp-item-mid" v-if="test.type == 4">
                    <image src="../../../images/icon/delete-bin-line.png"></image>
                    <text>删除</text>
                </view>
                <view @click="favorite()" class="tfp-item-mid" v-if="!isExam">
                    <image v-if="!subjectsTest?.favorite" src="@/images/icon/star-line.png"></image>
                    <image v-else src="@/images/icon/star-fill.png"></image>
                    <text>收藏</text>
                </view>
                <view class="tfp-item-mid" @click="this.$refs.popup.open('bottom')">
                    <image src="@/images/icon/calculator-line.png"></image>
                    <text>答题卡</text>
                </view>
                <view class="tfp-item-mid" @click="goChapter()" v-if="!isExam">
                    <image src="@/images/icon/layout-horizontal-line.png"></image>
                    <text>章节</text>
                </view>
                <view class="tfp-item-mid" @click="submitExam()" v-if="isExam && !subjectsId">
                    <image src="@/images/icon/file-upload-line.png"></image>
                    <text>提交试卷</text>
                </view>
            </view>
            <view class="tfp-item" @click="clickNexTopic">
                <text>下一题</text>
                <image src="@/images/icon/arrow-right-wide-line.png"></image>
            </view>
        </view>

        <!-- 答题卡 -->
        <uni-popup ref="popup" type="bottom">
            <view class="answer-sheet-box">
                <view class="asb-tit">答题卡
                    <view class="asb-close" @click="this.$refs.popup.close()">
                        <image src="@/images/icon/close-line.png"></image>
                    </view>
                </view>
                <view class="pdb-info" v-if="!isExam">
                    <view class="pdbi-item">
                        <text class="right"></text>
                        答对
                    </view>
                    <view class="pdbi-item">
                        <text class="wrong"></text>
                        答错
                    </view>
                    <view class="pdbi-item">
                        <text class="no"></text>
                        未答
                    </view>
                    <view @click="emptyTest()" class="pdbi-item pdbi-clean">
                        <image src="../../../images/icon/delete-bin-line.png"></image>
                        <text>清空记录</text>
                    </view>
                </view>
                <view class="pdb-info" v-else>
                    <view class="pdbi-item">
                        <text class="right"></text>
                        已答
                    </view>
                    <view class="pdbi-item">
                        <text class="no"></text>
                        未答
                    </view>
                    <!--
                    <view class="pdbi-item">
                        <text class="now"></text>
                        进行中
                    </view>
                    -->
                </view>
                <view class="asb-cnt">
                    <scroll-view lower-threshold="100" v-if="test.type == 3 || test.type == 9" class="tp-list-box-scroll" scroll-y="true">
                        <view>
                            <view class="asbc-tit">单选题</view>
                            <view class="asbc-list">
                                <template v-for="(item, index) in subjectsPage" :key="index">
                                    <view class="asbcl-item" v-if="item.type != 2"
                                          @click="selectSubjectsData(item, index)" :class="{
                                                cur: subjectsTest.sid == item.sid,
                                                multiple: item.multiple && !subjectsId,
                                                right:item.test_option?.correct==1 && subjectsId,
                                                wrong:item.test_option?.correct==2 && subjectsId,
                                            }"
                                    >
                                        {{ (index + 1) + (currentPage - 1) * 100 }}
                                    </view>
                                </template>
                            </view>
                            <view class="asbc-tit">多选题</view>

                            <view class="asbc-list">
                                <template v-for="(item, index) in subjectsPage" :key="index">
                                    <view class="asbcl-item" v-if="item.type == 2"
                                          @click="selectSubjectsData(item, index)" :class="{
                                                cur: subjectsTest.sid == item.sid,
                                                multiple: item.multiple && !subjectsId,
                                                right:item.test_option?.correct==1 && subjectsId,
                                                wrong:item.test_option?.correct==2 && subjectsId,
                                            }"
                                    >
                                        {{ (index + 1) + (currentPage - 1) * 100 }}
                                    </view>
                                </template>
                            </view>
                        </view>
                    </scroll-view>
                    <scroll-view lower-threshold="100" v-else class="tp-list-box-scroll" scroll-y="true">
                        <view class="asbc-list">
                            <view class="asbcl-item" @click="selectSubjectsData(item, index)" :class="{
                                        right:item.test_option?.correct==1,
                                        wrong:item.test_option?.correct==2,
                                        cur: subjectsTest.sid == item.sid
                                    }"
                                  v-for="(item, index) in subjectsPage" :key="index">
                                {{ (index + 1) + (currentPageList - 1) * 100 }}
                            </view>
                            <view class="page-box" v-if="pageData?.total > 100">
                                <button class="pb-left" :class="{disabled: currentPageList== 1}" @click="topPageClick()"
                                        :disabled=" currentPageList <= 1">
                                    <image src="@/images/icon/arrow-left-wide-line.png"></image>
                                    <text>上一页</text>
                                </button>
                                <button class="pb-right" :class="currentPageList == pageData?.last_page"
                                        @click="netPageClick()"
                                        :disabled="currentPageList >= pageData?.last_page">
                                    <text>下一页</text>
                                    <image src="@/images/icon/arrow-right-wide-line.png"></image>
                                </button>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </uni-popup>
    </view>
    <view class="no-data-nomal-box" v-if="!subjectsList.length && !loading">
        <view class="ndnb-icon">
            <image src="@/images/empty.png" mode="widthFix"></image>
        </view>
        <text class="ndnb-tip">暂无{{ title }}</text>
    </view>
</template>

<script>
import api from "../../../lib/api";
import {alert, debounce, showDelayLoading} from "../../../lib/utils";
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

export default {
    components: {
        mpHtml
    },
    data() {
        return {
            abcd: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],
            animationData: {},

            testId: 0,
            test: {},
            topicList: [],
            answersTotal: 1,//已经做完总题数
            noProdsDone: 0,//当时选择的题目位置
            currentPage: 1,//当前页数
            currentPageList: 1,//当前页数
            startTime: 0, // 考试开始时间
            listShow: true,
            duration: 0,
            right: 0,
            error: 0,
            topicSid: 0,
            countdownSeconds: 0, //考试结束时间
            subjectsList: [],
            subjectsPage: [],
            total: 1,
            direction: 0,
            multiple: [],
            quantityTopic: 0,
            countdownText: "",
            intervalCountdown: '',
            title: '',
            isRecitationQuestion: false,
            isAgreeBack: true,
            loading: true,
            loadingPage: true,
            pageData: {},
            subjectsId: '',
            optionTf: [
                {
                    name: '正确',
                    sid: 1
                },
                {
                    name: '错误',
                    sid: 2
                }
            ]
        }
    },
    onLoad(query) {
        if (query.subjects_id) {
            this.subjectsId = query.subjects_id
        }
        if (query.sid) {
            this.testId = query.sid
            this.initData()
        }
        uni.setNavigationBarTitle({
            title: ''
        })
    },
    onUnload() {
        let testId = this.testId
        clearInterval(this.intervalCountdown);
        let number = 0
        this.subjectsList.forEach(res => {
            if (res.multiple) {
                number++
            }
        })
        let subjectsList = this.subjectsList
        if (this.isExam && number != this.subjectsList.length && this.isAgreeBack && !this.subjectsId) {
            uni.showModal({
                title: '退出考试',
                content: '您正在进行模拟考试，确定要退出考试吗？',
                confirmText: '继续考试',
                cancelText: '退出',
                success(res) {
                    let data = {}
                    let arr = []
                    subjectsList.forEach(res => {
                        arr.push({sid: res.sid, multiple: res.multiple})
                    })

                    if (res.confirm) {
                        uni.setStorageSync("training_test_" + testId, arr)
                        uni.navigateTo({
                            url: '/pages/training/exam/test?sid=' + testId
                        })
                    } else {

                        data.testList = JSON.stringify(arr)
                        api.post("training/tests/" + testId + "/submit", data).then(res => {
                            uni.redirectTo({
                                url: '/pages/training/exam/end-test?test_id=' + testId
                            })
                        }).catch(err => {

                        })
                    }
                }
            })
        }
    },
    watch: {
        noProdsDone(data, newData) {
            if (newData < 0) {
                this.noProdsDone = 0
            }
        }
    },
    computed: {
        isExam() {
            return (this.test.type == 3 || this.test.type == 9)
        },
        subjectsTest() {
            if (this.subjectsList?.length) {

                let subject = this.subjectsList[this.noProdsDone]
                if (subject == undefined) {
                    return {}
                }
                let right = []
                if (subject?.type == 4) {
                    subject.rightText = "正确";
                    return subject
                }
                if (subject?.type == 3) {
                    subject.rightText = subject.judge_correct == 1 ? "正确" : "错误";
                } else {
                    if (subject?.option) {
                        subject?.option?.forEach((res, index) => {
                            if (res.is_correct == 1) {
                                right.push(this.abcd[index])
                            }
                        })
                        subject.rightText = right.join("、")
                        subject.rightArr = right
                    }

                }
                return subject
            }
            return {sid: ''};
        },
        quantity() {

            let index = this.subjectsList.findIndex(res => this.subjectsTest.sid == res.sid)
            if (index > -1) {
                if (this.currentPage > 1) {
                    index--
                }
                return (index + 1) + ((this.currentPage - 1) * 100)
            } else {
                return 1
            }

            // let index = this.noProdsDone
            // if (index) {
            //     if (this.currentPage > 1) {
            //         index--
            //     }
            //     return (index + 1) + ((this.currentPage - 1) * 100)
            // } else {
            //     return 1
            // }
        }
    },
    methods: {

        initData() {

            api.get("training/tests/" + this.testId).then(res => {
                this.test = res.test;
                this.answersTotal = res.answers_total;
                if (this.isExam) {
                    this.noProdsDone = res.no_prods_done > 0 ? res.no_prods_done - 1 : 0;
                } else {
                    this.noProdsDone = res.no_prods_done
                    if (res.current_page != 1) {
                        this.noProdsDone++
                    }
                }
                this.currentPage = res.current_page;
                this.currentPageList = res.current_page;
                this.startTime = res.start_time;
                this.topicSid = res.topicSid;
                this.topicList = res.topic_list;
                this.right = res.right;
                this.error = res.error;
                this.setTitle()

                this.countdownSeconds = res.countdown_seconds;
                // 调用倒计时函数
                if (this.isExam && !this.subjectsId) {
                    this.countdown();
                    // 每隔一秒调用倒计时函数
                    this.intervalCountdown = setInterval(() => {
                        this.countdown()
                    }, 1000);
                }
                this.getSubjectsList()
            }).catch(err => alert(err.message))
        },
        setTitle() {
            let title = ''
            if (this.test.type == 1) {
                title = "顺序练习"
            } else if (this.test.type == 3) {
                title = "模拟考试"
            } else if (this.test.type == 4) {
                title = "错题本"
            } else if (this.test.type == 5) {
                title = "题目收藏"
            } else if (this.test.type == 8) {
                title = "章节训练"
            } else if (this.test.type == 9) {
                title = "考试"
            } else {
                title = "专项训练"
            }
            this.title = title
            uni.setNavigationBarTitle({
                title: title
            })
        },
        getSubjectsList(noProdsDone = undefined) {
            const hideLoading = showDelayLoading("加载中", 200)
            this.loadingPage = true
            return new Promise((resolve, reject) => {
                api.get("training/tests/" + this.testId + "/subjects?page=" + this.currentPage).then(res => {
                    res.topicList.forEach( (res, index) => {
                        if (res.test_option) {
                            res.answerText = res.test_option.answer;
                        } else {
                            res.answerText = "";
                        }
                    })
                    this.subjectsList = res.topicList
                    if (noProdsDone) {
                        this.noProdsDone = noProdsDone
                    }
                    if (this.isExam) {
                        let storageTrainingTest = uni.getStorageSync("training_test_" + this.testId)
                        console.log(storageTrainingTest)
                        if (storageTrainingTest) {
                            storageTrainingTest.forEach((res, index) => {
                                if (res.multiple) {
                                    this.subjectsList[index].multiple = res.multiple
                                }
                            })
                        }
                        uni.removeStorageSync("training_test_" + this.testId)
                    }
                    this.subjectsPage = JSON.parse(JSON.stringify(res.topicList))
                    if (!this.isExam) {
                        if (res.head_tail?.top && this.currentPage != 1) {
                            res.head_tail.top.page = 'topPage'
                            this.subjectsList.unshift(res.head_tail.top)
                        }
                        if (res.head_tail.end && res.page.total > 100) {
                            res.head_tail.end.page = 'endPage'
                            this.subjectsList.push(res.head_tail.end)
                        }
                    }
                    this.total = res.page.total
                    this.quantityTopic = this.quantity
                    this.pageData = res.page
                    this.duration = 350
                    if (this.subjectsId) {
                        let index = this.subjectsPage.findIndex(res => res?.sid == this.subjectsId)
                        if(index > 0) {
                            this.noProdsDone = index

                            this.subjectsPage.forEach(res => {
                                res.multiple = res.test_option.sid.split(",")
                            })
                        }

                    }
                    resolve()
                    hideLoading()
                }).catch(err => {
                    alert(err.message);
                    hideLoading()
                }).finally(() => {
                    this.loading = false
                    this.loadingPage = false
                })
            })

        },
        /**
         * 点击上一题
         */
        clickTopTopic() {
            debounce("click_top_topic", () => {
                if (this.quantity == 1) {
                    uni.showToast({
                        title: "这已经是第一题了",
                        icon: 'none'
                    })
                } else {
                    if (this.noProdsDone > 0) {
                        console.log("上一题")
                        this.quantityTopic--
                        this.noProdsDone--
                    }
                }
            }, 150)

        },
        /**
         * 点击下一题
         */
        clickNexTopic() {
            debounce("click_nex_topic", () => {
                if (this.quantity == this.total) {
                    //题库最后一个 可以交卷了
                    console.log("题库最后一个 可以交卷了")
                    let multipleNum = 0
                    this.subjectsList.forEach(res => {
                        if (res.multiple) {
                            multipleNum++
                        }
                    })
                    if (this.test?.type != 3) {
                        uni.showToast({
                            icon: 'none',
                            title: "已经是最后一题了"
                        })
                    } else {
                        let number = this.total - multipleNum
                        let text = number ? "已经是最后一题了，您还有" + number + "题未作答" : "做完题，要记得交卷哦"
                        uni.showToast({
                            title: text,
                            icon: 'none'
                        })
                    }
                } else {
                    this.noProdsDone++
                    this.nextTopic()
                }
            }, 150)

        },
        /**
         * 下一题
         */
        nextTopic() {
            if (this.noProdsDone + 1 == this.subjectsList.length && this.quantity + 1 < this.total && !this.isExam) {
                console.log("可以下一页11")
                this.netPage()
            } else if (this.noProdsDone < this.subjectsList.length - 1) {
                this.quantityTopic++
            } else if (this.subjectsTest.sid == this.subjectsList[this.subjectsList.length - 1].sid) {
                console.log("最后一题")
            }
        },
        /**
         * 点击下一页
         */
        netPageClick() {

            if (this.currentPageList < this.pageData?.last_page && !this.isExam) {
                console.log("可以下一页33")
                if (this.loading) {
                    return
                }
                this.currentPageList++

                api.get("training/tests/" + this.testId + "/subjects?page=" + this.currentPageList)
                    .then(res => {
                        this.subjectsPage = res.topicList
                    }).finally(() => {
                    this.loading = false
                })
            }
        },
        /**
         * 点击上一页
         */
        topPageClick() {

            if (this.currentPageList > 1 && !this.isExam) {
                console.log("可以上")
                if (this.loading) {
                    return
                }
                this.currentPageList--

                api.get("training/tests/" + this.testId + "/subjects?page=" + this.currentPageList)
                    .then(res => {
                        this.subjectsPage = res.topicList
                    }).finally(() => {
                    this.loading = false
                })
            }
        },
        /**
         * 上一题
         */
        question() {
            if (this.subjectsTest.sid == this.subjectsList[0].sid && this.quantityTopic == 0) {
                console.log("第一题")
            } else if (this.noProdsDone > 0) {
                console.log("上一题")
                this.quantityTopic--

            } else if (this.subjectsTest.sid == this.subjectsList[0].sid && this.quantityTopic != 0) {
                console.log("上一页")
            }
        },
        // 上一页
        topPage() {
            if (this.loading) {
                return
            }
            this.currentPage--
            this.currentPageList--
            this.quantityTopic = this.currentPage
            this.listShow = false
            this.loading = true
            let noProdsDone = 100
            if (this.currentPage == 1) {
                noProdsDone = 99
            }
            this.getSubjectsList(noProdsDone).then(() => {
                if (this.currentPage == 1) {
                    this.noProdsDone = 99
                } else {
                    this.noProdsDone = 100
                }
                this.listShow = true
                this.testPut(this.subjectsPage[this.subjectsPage.length - 1].sid);
            })
        },
        //下一页
        netPage() {
            if (this.loading) {
                return
            }
            this.currentPage++
            this.currentPageList++
            this.quantityTopic = this.currentPage
            this.listShow = false
            this.loading = true

            this.getSubjectsList(1).then(() => {
                this.listShow = true
                this.loading = false
                //this.noProdsDone = 1
                this.testPut(this.subjectsPage[0].sid);
            })
        },
        eventHandle(e) {
            let isNext = e.detail.current > this.noProdsDone;
            this.noProdsDone = e.detail.current

            let noProdsDone = e.detail.current
            if (this.quantity >= this.total) {
                console.log("题库最后一页了")
                return;
            } else if (this.subjectsList[noProdsDone].page == "topPage" && this.quantityTopic != 0 && !this.isExam) {
                console.log('可以上一页')
                this.duration = 0
                this.topPage()
                return
            } else if (this.subjectsList[noProdsDone].page == "nextPage" && this.quantity + 1 < this.total && !this.isExam) {
                console.log('可以下一页22')
                this.duration = 0
                this.netPage()
                return;
            }
            if (isNext) {
                this.nextTopic()
            } else {
                this.question()
            }
            debounce("eventHandle_topic_list_test", () => {
                this.testPut()
            }, 2000)

        },
        testPut(test_subject_id = "") {
            let data = {
                test_subject_id: test_subject_id ? test_subject_id : this.subjectsTest.sid
            }
            if (data.test_subject_id == 'nextPage' || data.test_subject_id == "topPage") {
                return
            }
            return api.put("training/tests/" + this.testId, data).catch(err => alert(err.message))
        },
        selectSubjectsData(data, index) {
            this.duration = false
            if (this.currentPageList == this.currentPage) {
                this.noProdsDone = this.subjectsList.findIndex(res => data.sid == res.sid);
                this.quantityTopic = this.noProdsDone + (this.currentPage - 1) * 100
            } else {
                this.testPut(data.sid).finally(() => {
                    this.loading = true
                    this.initData()
                })

            }
            this.$refs.popup.close()
        },
        judgeWrongData(listItem, item) {
            if (this.isExam && !this.subjectsId) {
                return false
            }
            if (listItem.test_option?.correct == 2 && !this.isRecitationQuestion && item.sid == listItem.test_option?.option_id) {
                return true
            }
            return 0
        },
        judgeRightData(listItem, item) {
            if (this.isExam && !this.subjectsId) {
                return false
            }
            if ((item.sid == listItem.judge_correct && item.sid == listItem.test_option?.option_id) || (item.sid == listItem.judge_correct && this.isRecitationQuestion)) {
                return true
            }
            return 0
        },
        wrongData(listItem, item) {
            if (this.isExam && !this.subjectsId) {
                return false
            }
            if (listItem.test_option?.correct == 2 && listItem.test_option?.option_id.indexOf(item.sid) > -1 && !this.isRecitationQuestion) {
                return true
            }

            return 0
        },
        rightData(listItem, item) {
            if (this.isExam && !this.subjectsId) {
                return false
            }
            if (listItem.type == 2 && listItem.test_option?.option_id && item.is_correct == 1) {
                return true
            }
            if ((item.is_correct == 1 && listItem.test_option?.option_id.indexOf(item.sid) > -1) || (this.isRecitationQuestion && item.is_correct == 1)) {
                return true
            }
            return 0;
        },
        /**
         * 选择答案
         * @param data
         * @param index
         */
        selectSubjectsTest(data, index) {
            if (this.isRecitationQuestion || this.subjectsId) {
                return;
            }

            if (this.subjectsList[this.noProdsDone]?.test_option && !this.isExam) {
                return;
            }
            if (this.subjectsTest?.type == 2) {
                this.selectMultiple(data, index)
            } else {
                let is_correct = data.is_correct
                if (this.subjectsTest?.type == 3) {
                    is_correct = this.subjectsList[this.noProdsDone].judge_correct == data.sid ? 1 : 2;
                }
                if (is_correct == 1) {
                    this.right++
                } else {
                    this.error++
                    is_correct = 2;
                }
                let test_option = {
                    correct: is_correct,
                    option_id: data?.sid,
                }
                this.subjectsList[this.noProdsDone].correct = is_correct
                this.subjectsList[this.noProdsDone].test_option = test_option
                this.subjectsList[this.noProdsDone].option_id = test_option

                this.subjectsList[this.noProdsDone].multiple = [data?.sid]
                let subjectsPageKey = this.subjectsPage.findIndex(res => res.sid == this.subjectsTest.sid)
                this.subjectsPage[subjectsPageKey].multiple = [data?.sid]
                this.subjectsPage[subjectsPageKey].test_option = test_option
                this.subjectsPage[subjectsPageKey].option_id = test_option
                if (!this.isExam) {
                    this.submitAnswer()
                }
                //this.nextTopic()
            }
        },
        /**
         * 多选答案
         * @param data
         * @param index
         */
        selectMultiple(data, index) {
            if (!this.subjectsList[this.noProdsDone]?.multiple) {
                this.subjectsTest.multiple = [data.sid]
                return
            }

            let multipleIndex = this.subjectsList[this.noProdsDone].multiple.findIndex(res => res == data.sid)
            if (multipleIndex > -1) {
                this.subjectsList[this.noProdsDone].multiple.splice(multipleIndex, 1)
            } else {
                if (this.subjectsList[this.noProdsDone].multiple) {
                    this.subjectsList[this.noProdsDone].multiple.push(data.sid)
                }
            }
            if (this.subjectsList[this.noProdsDone]?.multiple && this.subjectsPage[this.noProdsDone]) {
                this.subjectsPage[this.noProdsDone].multiple = this.subjectsList[this.noProdsDone].multiple
            }
        },
        questionAnswers() {
            let subject = this.subjectsList[this.noProdsDone]
            let test_option = {
                correct: 1,
                option_id: "零"
            }

            this.subjectsList[this.noProdsDone].test_option = test_option
            let subjectsPageKey = this.subjectsPage.findIndex(res => res.sid == this.subjectsTest.sid)

            this.subjectsPage[subjectsPageKey].multiple = this.subjectsTest.multiple
            this.subjectsPage[subjectsPageKey].test_option = test_option
            this.subjectsPage[subjectsPageKey].option_id = test_option
            this.submitAnswer()
        },

        /**
         * 多选提交
         */
        submitMultiple() {
            console.log(this.subjectsTest)
            if (this.subjectsList[this.noProdsDone].type == 4) {
                this.questionAnswers()
                return
            }
            if (this.subjectsTest.multiple) {
                /**
                 * 问答提交
                 */



                let ids = this.subjectsTest.multiple.sort()
                let rightIds = []
                this.subjectsTest.option.forEach(res => {
                    if (res.is_correct == 1) {
                        rightIds.push(res.sid)
                    }
                })
                rightIds = rightIds.sort()
                let is_correct = 2
                if (rightIds.toString() == ids.toString()) {
                    is_correct = 1
                }
                let test_option = {
                    correct: is_correct,
                    option_id: ids.toString()
                }
                if (is_correct == 1) {
                    this.right++
                } else {
                    this.error++
                }

                this.subjectsList[this.noProdsDone].test_option = test_option
                let subjectsPageKey = this.subjectsPage.findIndex(res => res.sid == this.subjectsTest.sid)

                this.subjectsPage[subjectsPageKey].multiple = this.subjectsTest.multiple
                this.subjectsPage[subjectsPageKey].test_option = test_option
                this.subjectsPage[subjectsPageKey].option_id = test_option
                if (!this.isExam) {
                    this.submitAnswer()
                }
                if ((this.quantityTopic + 1 != this.total && is_correct == 1) || this.isExam) {
                    //this.noProdsDone++
                    debounce("eventHandle_topic_list_test", () => {
                        this.testPut();
                    }, 10)
                }
            }
        },
        /**
         * 清空试卷
         */
        emptyTest() {
            uni.showModal({
                content: '是否确认删除做题记录',
                confirmText: '确认删除',
                success: res => {
                    if (res.confirm) {
                        api.post("training/tests/" + this.testId + "/submit").then(res => {
                            console.log(this.test)
                            this.createTest(this.topicSid, this.test.type);
                        }).catch(err => {
                            alert(err.message)
                        })
                    }
                }
            })

        },
        goChapter() {
            uni.$once("train_test_id", (data) => {
                this.testId = data.sid
                this.initData()
            })
            uni.navigateTo({
                url: "/pages/training/exam/chapter?topic_id=" + this.topicSid + "&is_back=1"
            })
        },
        //考试倒计时
        countdown() {
            // 获取当前时间的毫秒数
            var currentTime = new Date().getTime();

            // 计算剩余时间（以毫秒为单位）
            var remainingTime = this.startTime + this.countdownSeconds - currentTime;
            // 将剩余时间转换为小时、分钟、秒
            var hours = Math.floor(remainingTime / (1000 * 60 * 60));
            var minutes = Math.floor((remainingTime % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((remainingTime % (1000 * 60)) / 1000);

            // 格式化时间，确保为两位数
            hours = ("0" + hours).slice(-2);
            minutes = ("0" + minutes).slice(-2);
            seconds = ("0" + seconds).slice(-2);

            // 更新倒计时显示
            this.countdownText = hours + ":" + minutes + ":" + seconds
            // 判断倒计时是否结束
            if (remainingTime <= 0) {
                clearInterval(this.intervalCountdown);  // 清除定时器
                this.endTopic()
            }
        },
        endTopic() {
            uni.showModal({
                title: '提示',
                content: "考试时间已到，系统已自动交卷",
                confirmText: "我知道了",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        this.isAgreeBack = false
                        uni.redirectTo({
                            url: '/pages/training/exam/end-test?test_id=' + this.testId
                        })
                    }
                }
            });
        },
        /**
         * 提交答案
         */
        submitAnswer() {
            let data = {
                option_id: this.subjectsTest.test_option.option_id,
                correct: this.subjectsTest.test_option.correct,
                subject_id: this.subjectsTest.sid,
                answer: this.subjectsTest.answerText
            }
            if (data.correct == 1) {
                setTimeout(() => {
                    if (this.subjectsTest.sid != this.subjectsList[this.subjectsList.length - 1].sid) {
                        this.noProdsDone++
                        this.nextTopic()
                    }
                }, 500)

            }
            if (this.isRecitationQuestion) {
                return
            }
            api.post("training/tests/" + this.testId + "/subjects", data).then(res => {
                debounce("eventHandle_topic_list_test", () => {
                    this.testPut()
                }, 10)
            }).catch(err => alert(err.message))
        },
        /**
         * 收藏
         */
        favorite() {
            if (!this.subjectsTest.favorite) {
                api.post("favorites/subject/" + this.subjectsTest.sid).then(res => {
                    uni.showToast({
                        title: "收藏成功",
                        icon: "none"
                    })
                    this.subjectsList[this.noProdsDone].favorite = true
                }).catch(err => alert(err.message))
            } else {
                api.delete("favorites/subject/" + this.subjectsTest.sid).then(res => {
                    uni.showToast({
                        title: "取消收藏成功",
                        icon: "none"
                    })
                    this.subjectsList[this.noProdsDone].favorite = false

                }).catch(err => alert(err.message))
            }
        },
        submitExam() {
            uni.showModal({
                content: '确定要交卷吗？',
                cancelText: '继续答题',
                confirmText: '现在交卷',
                success: res => {
                    if (res.confirm) {
                        this.isAgreeBack = false
                        this.submitPaper()
                    }
                }
            })
        },
        /**
         * 交卷
         */
        submitPaper() {
            let data = {}
            if (this.isExam) {
                let arr = []
                this.subjectsList.forEach(res => {
                    arr.push({sid: res.sid, multiple: res.multiple, answer: res.answerText})
                })
                data.testList = JSON.stringify(arr)
            }

            api.post("training/tests/" + this.testId + "/submit", data).then(res => {
                if (this.isExam) {
                    uni.redirectTo({
                        url: '/pages/training/exam/end-test?test_id=' + this.testId
                    })
                    uni.$emit('topic_test_complete')
                } else {
                    uni.showToast({
                        title: "练习完成",
                        icon: "none"
                    })
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1000)
                }
            }).catch(err => {

            })
        },
        clickTopic(data) {
            let sid = data.sid
            api.get("training/tests/current/" + sid + "?type=4").then(res => {
                uni.redirectTo({
                    url: "/pages/training/exam/test?sid=" + res.test.sid
                })

            }).catch(err => {
                if (err.code == 404) {
                    this.createTest(sid, 4)
                } else {
                    uni.hideLoading();
                    alert(err.message)
                }
            })
        },
        delTestWrong() {

            api.delete("training/wrong-subjects/" + this.test.sid + "/" + this.subjectsTest.sid).then(res => {
                this.total--
                if (!this.total) {
                    this.subjectsList = []
                }
                this.subjectsList.splice(this.noProdsDone, 1)
                if (this.noProdsDone != 0) {
                    this.noProdsDone--
                }

            }).catch(err => alert(err.message)).finally(() => {
            })
        },
        htmlLoad() {

        },
        createTest(sid, type = 4) {
            const hideLoading = showDelayLoading("请稍后", 200)

            api.post("training/" + sid + "/tests", {type: type}).then(res => {
                hideLoading()
                uni.redirectTo({
                    url: "/pages/training/exam/test?sid=" + res.sid
                })
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            })
        },
    }
}
</script>

<style>
page {
    background-color: #FFF;

}

.content {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.test-part-top {

    margin-bottom: 20rpx;
    padding: 0 30rpx;
}

.test-part-top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tpt-type {
    background-color: #F3F3F3;
    color: #333;
    font-size: 24rpx;
    padding: 10rpx;
    border-radius: 6rpx;
}

.tpt-type-switch {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 24rpx;
}

.tptts-item {
    background-color: #F3F3F3;
    padding: 10rpx;
    border-radius: 6rpx 0 0 6rpx;
    font-size: 24rpx;
    line-height: 1;
}

.tptts-item:last-child {
    border-radius: 0 6rpx 6rpx 0;
}

.tptts-item.cur {
    background-color: #065CDF;
    color: #fff;
}

.tpt-time {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tpt-time image {
    width: 32rpx;
    height: 32rpx;
}

.tpt-time text {
    font-size: 24rpx;
    padding-top: 6rpx;
    color: #EA712E;
}

.tpt-process {
    font-size: 24rpx;
    color: #999;
}

.tpt-process text {
    font-weight: bold;
    color: #333;
}

.bg-info {
    padding: 0 30rpx;
    font-size: 28rpx;
    line-height: 1.6;
    padding-bottom: 20rpx;
}

.bgi-tit {
    font-weight: bold;
}

.test-main-tit {
    font-weight: bold;
    font-size: 32rpx;
    line-height: 1.6;
    margin-bottom: 10rpx;
    padding: 0 30rpx;
}

.tmt-textarea {
    display: flex;
    padding: 30rpx;
}
.tmt-textarea textarea {
    flex: 1;
    background-color: #f3f3ff;
    border-radius: 20rpx;
    padding: 30rpx;
}

.tms-item {
    display: flex;
    align-items: flex-start;
    padding: 20rpx;
    border-radius: 12rpx;
}

.tms-item:hover {
    background-color: #f3f3ff;
}

.test-main-sub {
    padding: 0 10rpx;
}

.tmsi-sub {
    background-color: #F3F3F3;
    width: 50rpx;
    height: 50rpx;
    border-radius: 100%;
    font-size: 24rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.type-checkbox .tmsi-sub {
    border-radius: 12rpx;
}

.tmsi-sub.select {
    background-color: #065CDF;
    color: #fff;
}

.tmsi-sub.wrong {
    background-color: #fd3c3f;
    color: #fff;
}

.tmsi-sub.right {
    background-color: #03c713;
    color: #fff;
}


.tmsi-sub image {
    width: 32rpx;
    height: 32rpx;
}

.tmsi-cnt {
    flex: 1;
    font-size: 32rpx;
    padding-left: 20rpx;
    line-height: 1.6;
}

.tmsi-cnt image {
    max-width: 500rpx !important;
    border-radius: 12rpx;
    vertical-align: top;
}

.tfp-empty-block {
    height: 140rpx;
    padding-bottom: env(safe-area-inset-bottom);
}

.test-foot-part {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    display: flex;
    align-items: center;
    border-top: 1px solid #f3f3f3;
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
}

.tfp-item {
    height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10rpx;
}

.tfp-item image {
    width: 32rpx;
    height: 32rpx;
}
.tfp-item text {
    padding: 0 10rpx;
    font-size: 24rpx;
}

.tfp-mid {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tfp-item-mid {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 120rpx;
    padding: 0 30rpx;
}

.tfp-item-mid image {
    width: 36rpx;
    height: 36rpx;
}

.tfp-item-mid text {
    font-size: 28rpx;
    padding-top: 10rpx;
}

.tmt-current {
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 12rpx;
    text-align: center;
    color: #03c713;
    background-color: #f3f3f3;
    font-size: 28rpx;
    margin: 30rpx;
}
.tmt-cnt.questions{
    color: #ff731c;
}
.answer_text {
    border-radius: 12rpx;
    text-align: left;
    color: #03c713;
    font-size: 28rpx;
    margin: 30rpx;
}
.tmt-submit {
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 12rpx;
    text-align: center;
    color: green;
    background-color: #090ABC;
    font-size: 28rpx;
    margin: 30rpx 30rpx 0;
    color: #fff;
}

.tmt-cnt {
    padding: 0 30rpx;
}

.tmtc-tit {
    font-weight: bold;
    font-size: 28rpx;
    padding-bottom: 10rpx;
}

.tmtc-cnt {
    font-size: 32rpx;
    line-height: 1.8;
}

.answer-sheet-box {
    background-color: #FFF;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 5;
    border-top: 1px solid #e7e7e7;
}

.asb-tit {
    position: relative;
    height: 120rpx;
    line-height: 120rpx;
    text-align: center;
    font-weight: bold;
}

.asb-close {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    right: 16rpx;
    top: 16rpx;
    height: 88rpx;
    width: 88rpx;
    border-radius: 100%;
}

.asb-close image {
    width: 38rpx;
    height: 38rpx;
}

.asb-cnt {
    padding: 0 30rpx 30rpx;
}

.asbc-tit {
    padding: 20rpx 0;
}

.asbc-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.asbcl-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    width: 88rpx;
    border-radius: 100%;
    border: 1px solid #E7E7E7;
    margin-right: 22rpx;
    margin-bottom: 22rpx;
    font-size: 28rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.asbcl-item.cur {
    position: relative;
}
.asbcl-item.cur::after {
    content: "当前";
    position: absolute;
    background-color: rgba(0, 0, 0, .5);
    padding: 2rpx 8rpx;
    font-size: 24rpx;
    bottom: -16rpx;
    color: #fff;
    transform: scale(.9);
}

.asbcl-item.right {
    background-color: #36b913;
    color: #fff;
    border: 1px solid #36b913;
}

.asbcl-item.wrong {
    background-color: #fd3c3f;
    color: #fff;
    border: 1px solid #fd3c3f;
}

.asbcl-item.multiple {
    background-color: #36b913;
    color: #fff;
    border: 1px solid #35fc1f;

}

.tp-list-box-scroll {
    max-height: 800rpx;
}


.tpt-btn {
    background-color: #065CDF;
    color: #fff;
    border-radius: 12rpx;
    font-size: 24rpx;
}

/* 分页样式 */
.page-box {
    display: flex;
    width: 400rpx;
    height: 100rpx;
    margin: 30rpx auto;
    background-color: #f3f3ff;
    border-radius: 100rpx;
}

.pb-left,
.pb-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 100rpx;
}

.pb-left {
    border-radius: 100rpx 0 0 100rpx;
    margin: 10rpx 2rpx 10rpx 10rpx;
}

.pb-right {
    border-radius: 0 100rpx 100rpx 0;
    margin: 10rpx 10rpx 10rpx 2rpx;
}

.pb-left image,
.pb-right image {
    width: 32rpx;
    height: 32rpx;
    vertical-align: top;
    margin-top: 4rpx;
}

.pb-left text {
    font-size: 28rpx;
    padding-left: 10rpx;
}

.pb-right text {
    font-size: 28rpx;
    padding-right: 10rpx;
}

.disabled {
    background-color: #F3F3FF;
}

.disabled text {
    color: #999;
}

.topic_list {
    display: flex;
}

.topic_item {

}

.topic_item_cur {
    background-color: #065CDF;
    color: #fff;
}

.wp-tab {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
}

.wpt-item {
    height: 100rpx;
    line-height: 100rpx;
    margin-right: 40rpx;
    font-size: 28rpx;
    color: #999;
}

.wpt-item.cur {
    position: relative;
    color: #000;
    font-weight: bold;
}

.wpt-item.cur::after {
    content: '';
    position: absolute;
    left: 0;
    left: 50%;
    bottom: 12rpx;
    transform: translateX(-50%);
    width: 20rpx;
    height: 8rpx;
    border-radius: 8rpx;
    background-color: #390ABC;
}

.swiperList {
    background-color: #FFFFFF;
    width: 100%;
    flex: 1;
}


/* 题目收藏顶部分类 */
.vlb-tab {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    background-color: #FFFFFF;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin-bottom: 30rpx;
}

.sub-sview-list {
    white-space: nowrap;
    width: 100%;
    padding: 20rpx 0;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    display: flex;
    align-items: center;
    background-color: #FFFFFF;
}

.sub-sview-item {
    display: inline-block;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 30rpx;
    text-align: center;
    font-size: 36rpx;
    font-size: 24rpx;
    border-radius: 70rpx;
    margin-left: 30rpx;
    color: #666;
    background-color: #fff;
    border: 1px solid #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.sub-sview-item.cur {
    background-color: #390ABC;
    color: #fff;
    border: 1px solid #390ABC;
}


.pdb-info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 40rpx;
}

.pdbi-item {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #999;
    margin-right: 60rpx;
}

.pdbi-item text {
    display: inline-block;
    width: 30rpx;
    height: 30rpx;
    border-radius: 100%;
    background-color: #fff;
    border: 1px solid #e7e7e7;
    margin-right: 10rpx;
}

.pdbi-item text.right {
    background-color: #03c713;
    border: 1px solid #03c713;
}

.pdbi-item text.wrong {
    background-color: #fd3c3f;
    border: 1px solid #fd3c3f;
}

.pdbi-item text.now {
    background-color: #065CDF;
    border: 1px solid #065CDF;
}

.pdbi-item text.no {
    background-color: #fff;
    border: 1px solid #e7e7e7;
}

.pdbi-item.pdbi-clean {
    margin-right: 0;
}

.pdbi-clean image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 6rpx;
}

.pdbi-clean text {
    width: auto;
    border: 0;
}
</style>