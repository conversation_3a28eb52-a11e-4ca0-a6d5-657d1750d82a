<template>
    <view class="content">
        <view class="sel-package-box">
            <view class="spb-tit">选择套餐</view>
            <view class="spb-list">
                <view class="spb-item select"  @click="changeBuy(index)" v-for="(item, index) in amountList" :key="index">
                    <view class="spbi-tit">学习有效期{{ item.bays }}天</view>
                    <view class="spbi-price"><text>￥</text>
                        {{ item.amount }}</view>
                    <view class="spbi-check" v-if="amountIndex == index">
                        <image src="../../../images/icon/check-line.png"></image>
                    </view>
                </view>

            </view>
            <view class="spb-foot-btn" @click="buyTopic">立即支付</view>
        </view>
    </view>
    <pay-alert ref="payAlert" :order="order" :allow-types="allowTypes" @createdOrder="onCreatedOrder" @paymentCallback="onPaymentCallback" title="购买题库"></pay-alert>
</template>

<script>
import api from "@/lib/api";
import {alert, showDelayLoading} from "@/lib/utils";
import PayAlert from "@/components/pay-alert.vue";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {loginRequired} from "@/lib/login";

export default {
    components: {PayAlert},
    data() {
        return{
            amountList: [],
            amountIndex: 'month',
            sid:'',
            org_sid:'',
            enroll_sid:'',
            loading: false,
            order: {},
            allowTypes: ['amount', 'balance']
        }
    },
    onLoad(query) {
        this.sid = query.sid
        this.org_sid = query.org_sid
        this.enroll_sid = query.enroll_sid
        this.initData()
    },
    computed: {
        ...mapState(useUserStore, ['user']),
    },
    methods: {
        initData(){
            api.get('pages/topic/buy', {org_sid: this.org_sid, topic_sid: this.sid}).then(res => {
                this.amountList = res.amount_list
                this.order.total_amount = this.amountList[this.amountIndex].amount
            }).catch(err => {
                alert(err.message)
            })
        },
        changeBuy(index) {
            this.amountIndex = index
            this.$set(this.order, 'total_amount', this.amountList[this.amountIndex].amount)
        },
        onCreatedOrder(buyType) {
            const hideLoading = showDelayLoading("")
            let postData = {
                amount_index: this.amountIndex,
                org_sid: this.org_sid,
                enroll_sid: this.enroll_sid,
            }
            api.post("training/topics/"+this.sid+"/order", postData).then(res => {
                for (let key in res) {
                    this.$set(this.order, key, res[key])
                }
                if (buyType == 'amount') {
                    this.$refs.payAlert.payFromAmount()
                } else {
                    this.$refs.payAlert.payFromBalance()
                }
            }).catch(e => {
                uni.showModal({
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                hideLoading()
            })
        },
        buyTopic(){
            loginRequired().then(() => {
                if (!this.user.balance_show) {
                    this.$refs.payAlert.buyFromAmount()
                } else {
                    this.$refs.payAlert.open()
                }
            })
        },
        onPaymentCallback() {
            this.isPurchased = true
            uni.$emit("buy_topic_success")
            setTimeout(() =>{
                uni.navigateBack()
            },2000)
        }
    },
}

</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .spb-tit {
        font-size: 28rpx;
        padding-bottom: 30rpx;
    }
    .spb-list {
        padding-bottom: 30rpx;
    }
    .spb-item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        height: 120rpx;
        padding: 0 60rpx 0 30rpx;
    }
    .spbi-tit {
        font-weight: bold;
    }
    .spbi-price {
        color: red;
        font-weight: bold;
        font-size: 36rpx;
    }
    .spbi-price text {
        font-weight: normal;
        font-size: 28rpx;
    }
    .spbi-check {
        display: none;
        position: absolute;
        right: 0;
        bottom: 0;
        width: 38rpx;
        height: 38rpx;
        background-color: #390ABC;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 12rpx 0 12rpx 0;
    }
    .spbi-check image {
        width: 32rpx;
        height: 32rpx;
    }
    .select .spbi-check {
        display: flex;
    }

    .spb-foot-btn {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        border-radius: 12rpx;
        background-color: #390ABC;
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
    }
</style>