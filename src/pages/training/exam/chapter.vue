<template>
    <view class="content">
        <view class="exam-chapter-list">
            <view class="ecl-item " @click="start(item)" :class="{learned: statusText(item) == 2,goon : statusText(item) == 1}" v-for="(item, index) in list" :key="index">
                <view class="ecli-l">
                    <view class="eclil-tit">{{item.name}}</view>
                    <text class="eclil-data">共{{item.subjects_count}}题</text>
                    <text class="eclil-data done-count" v-if="statusText(item) != 0"> 已做{{item.test[0].subject_completed_count}}题</text>
                </view>
                <view class="ecli-r">
                    <text v-if="statusText(item) == 0">待学</text>
                    <text v-else-if="statusText(item) == 2">已学</text>
                    <text v-else>继续</text>

                    <image src="../../../images/icon/arrow-right-wide-line.png"></image>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import {showDelayLoading, alert} from "../../../lib/utils";
import api from "../../../lib/api";

export default {
    data() {
        return {
            topicId: 0,
            judge: 0,
            multi: 0,
            single: 0,
            list: [],
            is_back: 0,
        }
    },
    onLoad(query) {
        if (query.topic_id) {
            this.topicId = query.topic_id
        }
        if (query.judge) {
            this.judge = query.judge
        }
        if (query.multi) {
            this.multi = query.multi
        }
        if (query.single) {
            this.single = query.single
        }
        if (query.is_back) {
            this.is_back = query.is_back
        }
        this.initData()
    },
    methods: {
        initData() {
            api.get(`training/chapters/${this.topicId}`).then(res => {
                this.list = res
            }).catch(err => alert(err.message))
        },
        start(data) {
            // if(this.isPurchased == false){
            //     uni.showModal({
            //         content:"请先购买该题库",
            //         showCancel: false
            //     })
            // }


            api.get("training/tests/current/" + this.topicId + "?chapter_id=" + data.sid + "&type=8").then(res => {
                if (this.is_back) {
                    uni.$emit("train_test_id", {sid: res.test.sid})
                    uni.navigateBack({
                        delta: 1
                    })
                } else {
                    uni.navigateTo({
                        url: "/pages/training/exam/test?sid=" + res.test.sid
                    })
                }

            }).catch(err => {
                if (err.code == 404) {
                    this.createTest(8, data.sid)
                } else {
                    uni.hideLoading();
                    alert(err.message)
                }
            })
        },
        createTest(type, chapterSid) {
            const hideLoading = showDelayLoading("请稍后", 200)

            api.post("training/" + this.topicId + "/tests", {type: type, chapter_id: chapterSid}).then(res => {
                hideLoading()
                if (this.is_back) {
                    uni.$emit("train_test_id", {sid: res.sid})
                    uni.navigateBack({
                        delta: 1
                    })
                } else {
                    uni.navigateTo({
                        url: "/pages/training/exam/test?sid=" + res.sid
                    })
                }

            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            })
        },
        statusText(data) {
            if (!data.test.length) {
                return 0
            } else {
                let endTest = data.test.filter(res => res.status == 1)
                if (endTest.length) {
                    return 2
                } else {
                    return 1;
                }
            }
        }
    }
}
</script>

<style>
page {
    background-color: #f3f3ff;
}
.exam-chapter-list {
    padding: 30rpx;
}

.ecl-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}
.ecli-l {
    flex: 1;
}
.eclil-tit {
    font-weight: bold;
    line-height: 1.6;
}
.eclil-data {
    color: #999;
    font-size: 24rpx;
    padding-top: 10rpx;
}
.ecli-r {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20rpx;
}
.ecli-r text {
    padding-right: 10rpx;
    font-size: 28rpx;
}
.ecli-r image {
    width: 32rpx;
    height: 32rpx;
}


.ecl-item.learned {
    color: #999;
}
.ecl-item.goon {
    color: #EA712E;
}
.ecl-item.goon .eclil-data {
    color: #EA712E;
}
</style>