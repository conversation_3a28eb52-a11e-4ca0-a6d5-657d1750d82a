<template>
    <view class="content">
        <view class="tsb-fixed-box">
            <view class="top-search-box">
                <view class="tsb-icon">
                    <image src="../../images/icon/search-line.png"></image>
                </view>
                <input class="uni-input" type="text" confirm-type="search" v-model="keywords" @confirm="search()" :focus="focus" placeholder="输入关键词" @input="clearInput"/>
                <view class="search-clear-btn" v-if="showClearIcon" @click="clearIcon">
                    <view class="scb-icon">
                        <image src="../../images/icon/close-line-white.png"></image>
                    </view>
                </view>
                <button class="esb-btn" @click="search()" :disabled="disabled">搜索</button>
            </view>
            <scroll-view class="sub-sview-list navScroll" scroll-x="true">
                <view :class="{ 'sub-sview-item': true, 'cur': item.sid === selectCategoryId }" v-for="item in searchCategories" :key="item.sid" @click="chooseCategory(item.sid)">{{ item.name }}</view>
            </scroll-view>
            <view class="doc-format-list">
                <view class="dfl-item" :class="{'cur': item.type === selectType}" v-for="(item, index) in typeList" :key="index" @click="chooseType(item.type)"><text>{{ item.name }}</text></view>
            </view>
        </view>

        <view class="search-history" v-if="showHistory && this.searchHistories.length > 0">
            <view class="sh-tit">
                <text>最近搜索</text>
                <view class="sht-delete" @click="clearHistory">
                    <image src="../../images/icon/delete-bin-line.png"></image>
                </view>
            </view>
            <view class="sh-list">
                <view class="shl-item" v-for="(history, index) in searchHistories" :key=index @click="search(history)">{{ history }}</view>
            </view>
        </view>

        <view class="docs-list-box" v-if="materials.length > 0">
            <view class="dlb-item" v-for="material in materials" :key="material.sid" @click="go(material)">
                <view class="dlbi-img">
                    <doc-icon :format="material.resource?.format" />
                </view>
                <view class="dlbi-cnt">
                    <view class="dlbic-tit">{{ material.title }}</view>
                    <view class="dlbic-foot">
                        <view class="dlbicf-time">时间：<uni-dateformat :date="material.release_at" format="yyyy-MM-dd"></uni-dateformat></view>
                        <view class="dlbicf-pn" v-if="material.resource && material.resource.page_count">页数：{{ material.resource.page_count }}</view>
                    </view>
                </view>
            </view>
            <uni-load-more :status="listStatus"></uni-load-more>
        </view>

        <view class="no-data-nomal-box" v-if="listLoaded && !listLoading && materials.length === 0">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">抱歉，未检索到相关资料</text>
        </view>

        <SideLinkAi />
    </view>
</template>

<script>
import {alert, getDetailUrl, getImageByFormat, showDelayLoading, removeURLParameter} from "@/lib/utils";
import api, {buildQuery} from "@/lib/api";
import DocIcon from "@/components/doc-icon.vue";
import SideLinkAi from "@/components/side-link/ai.vue";

export default {
    components: {
        DocIcon,
        SideLinkAi
    },
    onLoad(e) {
        if (e.cateId) {
            this.selectCategoryId = e.cateId
        }
        if (e.q) {
            this.keywords = e.q
            this.showClearIcon = true
        }
        this.getHistory()
        this.getCategory()
        this.getListType()
        if (this.keywords !== '') {
            this.loadMaterials()
            this.focus = false
            this.showHistory = false
            this.addHistory()
        }
    },
    data() {
        return {
            selectCategoryId: 'material',
            searchCategories: [],
            materials: [],
            nextCursor: "",
            listRows: 20,
            listLoaded: false,
            listLoading: false,
            listEnd: false,
            keywords: "",
            searchHistories: [],
            searchHistoryKey: "search_materials",
            showClearIcon: false,
            disabled: true,
            focus: true,
            showHistory: true,
            typeList: [],
            selectType: ""
        }
    },
    computed: {
        listStatus() {
            if (this.listLoading) {
                return "loading";
            } else if (this.listEnd) {
                return "noMore";
            } else {
                return "more";
            }
        }
    },
    onReachBottom() {
        this.loadMaterials()
    },
    watch: {
        keywords(e) {
            if (e.length > 0) {
                this.showClearIcon = true
                this.disabled = false
            } else {
                this.showClearIcon = false
                this.disabled = true
            }
        }
    },
    methods: {
        getListType() {
            api.get("pages/list-type").then(res => {
                this.typeList = res
            }, err => {
                alert(err.message)
            })
        },
        getCategory() {
            const hideLoading = showDelayLoading("加载中", 200)
            api.get("cms/categories?classify=material").then(res => {
                const categories = res;
                if (categories.length > 0) {
                    categories.unshift({
                        "name": "全部",
                        "sid": "material",
                        "logo_src": ""
                    });
                }

                this.searchCategories = categories;
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        loadMaterials(refresh) {
            if (this.listLoading || this.listEnd) {
                return;
            }

            let query = {list_rows: this.listRows}
            if (this.nextCursor !== '') {
                query.next_cursor = this.nextCursor
            }
            if (this.keywords !== '') {
                query.keywords = this.keywords
            }
            if (this.selectType !== '') {
                query.type = this.selectType
            }

            this.listLoading = true;
            const hideLoading = refresh ? showDelayLoading('加载中', 200) : null

            this.showHistory = false
            let url = "cms/" + this.selectCategoryId + "/contents"
            api.get(url, query).then(res => {
                this.url = url + (url.includes("?") ? "&" : "?") + buildQuery(query)
                this.nextCursor = res.next_cursor
                if (refresh) {
                    this.materials = res.data
                } else {
                    this.materials.push(...res.data)
                }
                this.listLoaded = true;
                if (res.next_cursor === '') {
                    this.listEnd = true
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                this.listLoading = false
                if (hideLoading) {
                    hideLoading()
                }
            })
        },
        chooseCategory(cateId) {
            this.selectCategoryId = cateId
            if (this.keywords === '') {
                return
            }
            this.materials = []
            this.listEnd = false
            this.nextCursor = ""
            this.loadMaterials(true)
        },
        chooseType(type) {
            this.selectType = type
            if (this.keywords === '') {
                return
            }
            this.materials = []
            this.listEnd = false
            this.nextCursor = ""
            this.loadMaterials(true)
        },
        search(history) {
            if (history) {
                this.keywords = history
            }
            this.chooseCategory('material')
            this.addHistory()
        },
        getHistory() {
            uni.getStorage({
                key: this.searchHistoryKey,
                success: (res) => {
                    this.searchHistories = res.data
                },
            })
        },
        addHistory() {
            if (this.keywords === '') {
                return
            }
            const key = this.searchHistoryKey;
            uni.getStorage({
                key,
                success: (res) => {
                    let list = res.data;
                    const i = list.indexOf(this.keywords);

                    if (i != -1) {
                        list.splice(i, 1);
                    }

                    list.unshift(this.keywords);

                    if (list.length > 10) {
                        list.splice(10);
                    }

                    uni.setStorage({
                        key,
                        data: list,
                        success: () => {
                            this.searchHistories = list
                        },
                        fail: () => {}
                    });
                },
                fail: () => {
                    uni.setStorage({
                        key,
                        data: [this.keywords],
                        success: () => {
                            this.searchHistories = [this.keywords]
                        },
                        fail: () => {}
                    });
                }
            });
        },
        clearHistory() {
            uni.removeStorage({
                key: this.searchHistoryKey,

                success: () => {
                    this.searchHistories = []
                },

                fail(err) {}
            });
        },
        go(data) {
            let url = getDetailUrl(data.type_label, data.sid)
            if (url === '') {
                alert("未获取到详情地址")
                return
            }
            uni.navigateTo({
                url: url,
                success: () => {
                    if (data.type_label === 'video') {
                        this.url = removeURLParameter(this.url, 'next_cursor')
                        this.url = removeURLParameter(this.url, 'sid')
                        this.url += '&sid=' + data.sid
                        uni.$emit("video-url", {
                            url: this.url
                        })
                    }
                }
            })
        },
        clearInput(e) {
            if (e.detail.value.length > 0) {
                this.showClearIcon = true;
            } else {
                this.showClearIcon = false;
            }
        },
        clearIcon() {
            this.keywords = '';
            this.showClearIcon = false;
            this.materials = []
            this.showHistory = true
            this.listLoaded = false
        }
    }
}
</script>

<style>
    page {
        background-color: #FFF;
    }

    .tsb-fixed-box {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        z-index: 2;
    }

    .sub-sview-list {
        white-space: nowrap;
        width: 100%;
        padding: 20rpx 0;
    }
    .sub-sview-item {
        display: inline-block;
        height: 70rpx;
        line-height: 70rpx;
        padding: 0 30rpx;
        text-align: center;
        font-size: 36rpx;
        font-size: 24rpx;
        border: 1px solid #E7E7E7;
        border-radius: 70rpx;
        margin-left: 30rpx;
        color: #666;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .sub-sview-item.cur {
        background-color: #390ABC;
        color: #fff;
        border: 1px solid #390ABC;
    }
    .st-eb {
        height: 250rpx;
    }
    .top-search-box {
        display: flex;
        align-items: center;
        height: 100rpx;
        background-color: #F3F3FF;
        border-radius: 12rpx;
        padding: 0 10rpx 0 20rpx;
        margin: 30rpx 30rpx 0;
    }
    .tsb-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
    }
    .tsb-icon image {
        width: 32rpx;
        height: 32rpx;
    }
    .top-search-box .uni-input {
        flex: 1;
        font-size: 28rpx;
        padding-left: 20rpx;
        height: 100rpx;
        line-height: 100rpx;
    }
    .esb-btn {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #390ABC;
    }
    .clear {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
    }

    .tsb-relative .tsb-fixed-box {
        position: relative;
    }

    .tsb-relative .st-eb {
        display: none;
    }

    .search-history {
        padding: 0 30rpx;
    }
    .sh-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10rpx;
    }
    .sh-tit text {
        font-size: 28rpx;
        color: #999;
    }
    .sht-delete {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        width: 80rpx;
    }
    .sht-delete image {
        width: 32rpx;
        height: 32rpx;
    }
    .sh-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }
    .shl-item {
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 30rpx;
        margin: 0 30rpx 30rpx 0;
        background-color: #fff;
        border-radius: 80rpx;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }

    /* 资料列表 */
    .docs-list-box {
        position: relative;
        padding: 0 30rpx 30rpx;
    }
    .dlb-item {
        display: flex;
        align-items: center;
        padding: 30rpx 0 0;
        border-radius: 12rpx;
    }
    .dlbic-tit {
        height: 80rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        font-weight: bold;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    .dlbic-foot {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }
    .dlbicf-time {
        padding-right: 30rpx;
    }

    .dlbi-img {
        position: relative;
        width: 140rpx;
        height: 140rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        border: 1px solid #e7e7e7;
    }
    .doc-type {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        width: 64rpx;
        height: 64rpx;
    }
    .dlbi-cnt {
        flex: 1;
    }

    /* 搜索清楚按钮 */
    .search-clear-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        padding: 0 20rpx;
    }
    .scb-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 36rpx;
        height: 36rpx;
        border-radius: 100%;
        background-color: rgba(0, 0, 0, .3);
        margin-right: 10rpx;
    }
    .search-clear-btn image {
        width: 28rpx;
        height: 28rpx;
    }


    .doc-format-list {
        padding: 0 20rpx 20rpx 20rpx;
        display: flex;
        align-items: center;
    }
    .dfl-item {
        height: 60rpx;
        line-height: 60rpx;
        padding: 0 20rpx;
        font-size: 24rpx;
        color: #999;
        margin-right: 12rpx;
    }
    .dfl-item.cur text {
        font-weight: bold;
        border-radius: 60rpx;
        color: #390ABC;
    }
</style>