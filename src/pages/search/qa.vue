<template>
    <view class="content">
        <view class="esb-fixed">
            <view class="exam-search-box">
                <image src="../../images/icon/search-line.png"></image>
                <input v-model="content" class="uni-input" :focus="focus" placeholder="请输入关键词" @input="clearInput"/>
                <view class="search-clear-btn" v-if="content" @click="clearIcon">
                    <view class="scb-icon">
                        <image src="../../images/icon/close-line-white.png"></image>
                    </view>
                </view>
                <button class="esb-btn" :disabled="content.length === 0" @click="search">搜索</button>
            </view>
        </view>

        <!-- 搜索记录 -->
        <view class="search-history" v-if="searchTitle.length && !contentText">
            <view class="sh-tit">
                <text>最近搜索</text>
                <view class="sht-delete" @click="delSearchTitle">
                    <image src="../../images/icon/delete-bin-line.png"></image>
                </view>
            </view>
            <view class="sh-list">
                <view class="shl-item" @click="getSearch(item)" v-for="(item, index) in searchTitle" :key='index'>{{ item }}</view>

            </view>
        </view>
        <view class="es-info" v-if="isSearch">与<text>{{contentText}}</text>相关的动态：</view>
    </view>
    <questions-item :loading="loadingInit" :questions="list"></questions-item>
    <uni-load-more  v-if="list.length" :status="statusLoad"></uni-load-more>
    <!-- 暂无数据 -->
    <view class="no-data-nomal-box" v-if="!loading && list.length === 0">
        <view class="ndnb-icon">
            <image src="../../images/empty.png" mode="widthFix"></image>
        </view>
        <text class="ndnb-tip">抱歉，未检索到相关动态</text>
    </view>
</template>

<script>
import api from "../../lib/api";
import {alert, showDelayLoading} from "../../lib/utils";
import {loginRequired} from "@/lib/login";
import QuestionsItem from "@/pages/ask/questionsItem.vue";
import {getAppName} from "@/lib/context";

export default {
    components: {QuestionsItem},
    data() {
        return{
            list: [],
            content: '',
            contentText: '',
            scroll_id: '',
            isSearch: false,
            loading: true,
            loadingInit: false,
            focus: true,
            statusLoad: 'loading',
            loadingContentText: true,
            searchTitle:[],
            showClearIcon: false,
            appName: getAppName()
        }
    },
    onLoad(query) {
        if (uni.getStorageSync('search_title_qa')){
            this.searchTitle = uni.getStorageSync('search_title_qa');
        }
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.scroll_id ){
            this.getQuestions();
        }
    },
    watch:{
      content(){
          if (!this.list.length){
             // this.isSearch = false
          }
      }
    },
    methods:{
        search() {
            this.scroll_id = ''
            this.list = []

            if (!this.content) {
                uni.showToast({
                    title: "请输入搜索关键词",
                    icon: "none"
                })
                return
            }
            if (!this.searchTitle.includes(this.content)) {
                this.searchTitle.unshift(this.content); // 将新数据添加到数组的开头

                if (this.searchTitle.length > 20) {
                    this.searchTitle.pop(); // 删除数组中的最后一个元素
                }
                uni.setStorageSync("search_title_qa", this.searchTitle)
            }
            this.contentText = this.content
            this.getQuestions()
        },
        getSearch(data){
            this.content = data
            this.search()
        },
        getQuestions() {
            let data = {
                content: this.contentText,
                scroll_id: this.scroll_id
            }

            this.isSearch = true
            this.loading = true
            const hideLoading = showDelayLoading("搜索中", 200)

            api.get("qa/search", data).then(res => {
                this.list.push(...res.data)
                this.scroll_id = res.scroll_id
                if (!res.data.length || res.data.length < 19){
                    this.statusLoad = 'noMore'
                }
                hideLoading()

            }).catch(err => {
                hideLoading()
                alert(err.message)
            }).finally(() => {
                this.loading = false
                this.loadingInit = false
            })
        },
        attitude(data){
            loginRequired().then(() =>{
                let postData = {
                    attitude: 1
                }
                if (this.loading){
                    return
                }
                this.loading = true
                if (!data.is_like) {
                    api.post("attitude/question/" + data.sid, postData).then(res => {
                        data.attitudes_count++
                        data.is_like = true
                    }).catch(err => alert(err.message)).finally(() =>{
                        this.loading = false
                    })
                }else {
                    api.delete("attitude/question/" + data.sid + '?attitude=1').then(res => {
                        data.is_like = false
                        data.attitudes_count--
                    }).catch(err => alert(err.message)).finally(() =>{
                        this.loading = false
                    })
                }
            })
        },
        delSearchTitle(){
            this.searchTitle = []
            uni.setStorageSync("search_title_qa", this.searchTitle)
        },
        clearInput(e) {
            if (e.detail.value.length > 0) {
                this.showClearIcon = true;
            } else {
                this.showClearIcon = false;
            }
        },
        clearIcon() {
            this.content = '';
            this.contentText = '';
            this.list = []
            this.loadingInit = false
            this.loading = true
            this.showClearIcon = false;
            this.isSearch = false
        }
    },
    onShareAppMessage(res) {
        return {
            title: this.appName,
            path: '/pages/ask/n-detail?sid=' + res.target.id
        };
    },
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .esb-fixed {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        background-color: #F3F3FF;
        margin-bottom: 30rpx;
    }
    .esb-eb {
        height: 130rpx;
    }
    .exam-search-box {
        display: flex;
        align-items: center;
        background-color: #FFF;
        border-radius: 12rpx;
        padding: 0 0 0 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .exam-search-box image {
        width: 32rpx;
        height: 32rpx;
    }
    .exam-search-box .uni-input {
        flex: 1;
        height: 100rpx;
        padding-left: 20rpx;
        font-size: 28rpx;
    }
    .esb-btn {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #390ABC;
    }
    .clear {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
    }

    .es-info {
        padding: 0 0 30rpx;
        font-size: 28rpx;
        color: #999;
    }
    .es-info text {
        font-weight: bold;
        font-style: italic;
        padding: 0 10rpx;
        color: #333;
    }


    .esrl-jiexi-cnt text {
        font-weight: bold;
    }



    /* 内容块 */
    .ask-cnt-list {
        margin: 0 30rpx;
    }
    .acl-item {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
    }
    .acli-top {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 0;
    }
    .aclit-l image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
    }
    .aclit-r {
        padding-left: 20rpx;
    }
    .aclitr-tit {
        font-size: 28rpx;
        font-weight: bold;
    }
    .aclitr-time {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .aclic-txt {
        padding: 30rpx;
        white-space: pre-wrap;
        font-size: 28rpx;
        line-height: 1.6;
    }
    .aclic-txt navigator {
        color: #390ABC;
        display: inline-block;
    }

    .aclic-img {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 0 0 30rpx 30rpx;
    }
    .aclic-img image {
        width: 209rpx;
        height: 209rpx;
        margin-right: 1px;
        margin-bottom: 1px;
        border-radius: 12rpx;
    }

    .acli-foot {
        display: flex;
        align-items: center;
        border-top: 1px solid #f3f3ff;
    }
    .acli-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
    }
    .acli-item image {
        width: 32rpx;
        height: 32rpx;
    }
    .acli-item text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }

    .ask-foot-box {
        position: sticky;
        bottom: 0;
        padding: 16rpx 30rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .afb-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .afb-btn {
        flex: 1;
        height: 90rpx;
        text-align: center;
        line-height: 90rpx;
        background-color: #390ABC;
        color: #fff;
        font-size: 28rpx;
        border-radius: 80rpx;
    }
    .afb-small-btn {
        width: 200rpx;
        margin-left: 30rpx;
        height: 90rpx;
        line-height: 90rpx;
        text-align: center;
        font-size: 28rpx;
        color: #390ABC;
    }


    /* 搜索历史 */
    .sh-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10rpx;
    }
    .sh-tit text {
        font-size: 28rpx;
        color: #999;
    }
    .sht-delete {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        width: 80rpx;
    }
    .sht-delete image {
        width: 32rpx;
        height: 32rpx;
    }
    .sh-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }
    .shl-item {
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 30rpx;
        margin: 0 30rpx 30rpx 0;
        background-color: #fff;
        border-radius: 80rpx;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }

    /* 搜索清楚按钮 */
    .search-clear-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        padding: 0 20rpx;
    }
    .scb-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 36rpx;
        height: 36rpx;
        border-radius: 100%;
        background-color: rgba(0, 0, 0, .3);
        margin-right: 10rpx;
    }
    .search-clear-btn image {
        width: 28rpx;
        height: 28rpx;
    }
</style>