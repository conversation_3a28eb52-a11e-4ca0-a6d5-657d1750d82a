<template>
    <view class="content">
        <view class="service-intro-cnt">
            <image src="https://img.shiwusuo100.com/assets/app-static/selftest/01.png" mode="widthFix"></image>
        </view>

        <view class="sic-button" @click="goList">
                开始自测
        </view>
        <!--
        <view class="sic-foot-eb"></view>
        <view class="sic-foot">
            <view class="sicf-btn" @click="goList">
                <text>开始自测</text>
            </view>
        </view>
        -->
    </view>
</template>

<script>
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {getAppName} from "@/lib/context";

export default{
    data() {
        return {
            topicId:0,
            appName: getAppName()
        }
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    methods: {
        goList() {
            if (!this.user) {
                uni.navigateTo({
                    url: '/pages/login/index'
                })
            } else {
                uni.navigateTo({
                    url: '/pages/selftest/industry'
                })
            }

        }
    },
    onShareAppMessage() {
        let path = "/pages/index/index?ref=";

        return {
            title: this.appName,
            path,
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
        };
    },
}
</script>

<style>
page {
    background-color: #bf2321;
}
.service-intro-cnt image {
    width: 100%;
}
.sic-button {
    width: 300rpx;
    height: 100rpx;
    border-radius: 20rpx;
    text-align: center;
    line-height: 100rpx;
    margin: 0 auto;
    background-color: #fffdc2;
    color: #c62724;
    font-weight: bold;
}
.content {
}
.sic-foot {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    z-index: 10;
}
.sicf-btn {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #c52624;
    color: #fff;
    border-radius: 12rpx;
    margin-bottom: env(safe-area-inset-bottom);
}
.sicf-btn image {
    width: 36rpx;
    height: 36rpx;
}
.sicf-btn text {
    font-weight: bold;
}
.sicf-btn.disabled {
    background-color: #999;
}
.sic-foot-eb {
    height: 148rpx;
    padding-bottom: env(safe-area-inset-bottom);
}
</style>