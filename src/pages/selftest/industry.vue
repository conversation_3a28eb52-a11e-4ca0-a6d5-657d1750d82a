<template>
    <view class="content">
        <view class="top-cnt">选择所属的机构行业</view>
        <view class="industry-list">
            <view class="il-item" @click="goTest(item, index)" :class="{cur: listIndex === index}" v-for="(item, index) in list" :key="index">
                <view class="ili-l">{{item.name}}・{{subjectsCount(item.subjects_count, index)}}题</view>
                <image src="../../images/icon/arrow-right-wide-line.png"></image>
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert} from "@/lib/utils";

export default {
    data() {
        return {
            list: [],
            listIndex: ""
        }
    },
    onLoad(e) {
        this.initData()
    },
    methods: {
        initData() {
            api.get("punish/topic").then(res => {
                this.list = res
            }).catch(err => alert(err.message))
        },
        goTest(data, index) {
            this.listIndex = index;
            uni.navigateTo({
                url: "/pages/selftest/subject?topic_id=" + data.id
            })
        },
        subjectsCount(count, index) {
            let substrateCount = this.list[0].subjects_count;
            if (index == 0) {
                return substrateCount
            }
            return substrateCount + count

        }
    }
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .top-cnt {
        padding: 60rpx 0;
        text-align: center;
        font-size: 36rpx;
        font-weight: bold;
    }
    .industry-list {
        padding: 0 30rpx 30rpx;
    }
    .il-item {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 0 30rpx;
        font-size: 32rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        border: 1px solid transparent;
    }
    .il-item.cur {
        border: 1px solid #390ABC;
        font-weight: bold;
        color: #390ABC;
        background-color: #f3f3ff;
    }
    .il-item image {
        width: 32rpx;
        height: 32rpx;
    }
</style>