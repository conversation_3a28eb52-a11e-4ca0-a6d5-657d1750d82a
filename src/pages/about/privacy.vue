<template>
    <view class="content">
        <view class="rich-text">
          <p> 更新日期：2022年10月20日 </p>
          <p> {{ appName }}小程序（以下简称“{{ appName }}”）是由东方首选(北京)注册安全工程师事务所有限公司开发，为安全人提供资料下载、在线学习、安全问答以及事件解读等服务，运营管理权归东方首选(北京)注册安全工程师事务所有限公司所有。 </p>
          <p><text>{{ appName }}小程序隐私政策（以下简称“本政策”）帮助您了解以下内容：</text> </p>
          <p> 一、我们如何收集和使用您的个人信息 </p>
          <p> 二、我们如何对外提供您的个人信息 </p>
          <p> 三、我们如何保护您的个人信息 </p>
          <p> 四、您的权利 </p>
          <p> 五、我们如何处理儿童的个人信息 </p>
          <p> 六、本政策如何更新 </p>
          <p> 七、其他 </p>
          <p> 八、如何联系我们 </p>
          <p> 我们非常重视用户的隐私和个人信息保护。我们将按照法律法规要求，采取相应的安全保护措施来保护您的个人信息。 </p>
          <p> 在使用{{ appName }}小程序前，请您仔细阅读并了解本政策。若您使用我们的产品和服务，即表示您同意我们在本政策中所述内容。如您有问题，请联系我们。 </p>
          <h3> 一、我们如何收集和使用您的个人信息 </h3>
          <p> 个人信息是指以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人身份或者反映特定自然人活动情况的各种信息。 </p>
          <p> 在您使用{{ appName }}小程序的过程中，本小程序会按照如下方式收集您在使用时主动提供或因为使用而产生的个人信息，用以向您提供服务及优化我们的服务： </p>
          <p> 1、当您使用{{ appName }}小程序时，为使我们的服务正常运行以保障您的正常使用，改进优化您的使用体验，我们仅<text>会收集您的手机号信息</text>，这类信息是为提供服务必须收集的基础信息。 </p>
          <p> 2、当您参与{{ appName }}小程序的互动留言活动时，需要您完成实名认证才能使用；因{{ appName }}使用的是微信登录，所以您不需要单独提供相关信息。<text>此类信息属于个人敏感信息，我们会严格保护您的个人信息，您可以根据自身需要选择是否参与互动留言活动。</text> </p>
          <p> 3、<text>当您使用{{ appName }}小程序下载资源时或小程序用阅读器打开应用文章关联的PDF文件时，会获取设备的存储权限，修改或读取存储卡中的内容。</text> </p>
          <h3> 二、我们如何对外提供您的个人信息 </h3>
          <p> 1.为了更全面地为您提供服务，我们会使用专业第三方服务单位提供的软件服务工具包（简称SDK），包括在使用分享功能时所调用的分享SDK（微博、微信、QQ等提供），在浏览网页时所调用的浏览服务SDK（腾讯提供）。这些SDK可能会收集、使用或存储您的个人信息。我们会尽到审慎义务，对所使用SDK的来源进行严格审查。对于第三方SDK收集的信息，由第三方服务单位承担相应责任，详情请查阅第三方服务单位的服务条款及隐私政策。 </p>
          <p> 2.根据相关法律法规及国家标准，在以下情形中，我们可能会共享或公开披露个人信息无需事先征得您的同意： </p>
          <p> （1）与国家安全、国防安全直接相关的； </p>
          <p> （2）与公共安全、公共卫生、重大公共利益直接相关的； </p>
          <p> （3）与刑事侦查、起诉、审判和判决执行等直接相关的； </p>
          <p> （4）出于维护个人信息主体或其他个人的生命、财产等重大合法权益但又很难得到您本人同意的； </p>
          <p> （5）法律法规规定的其他情形。 </p>
          <h3> 三、我们如何保护您的个人信息 </h3>
          <p> 我们会采取一切合理可行的措施，保护您的个人信息。我们会采取一切合理可行的措施，确保未收集无关的个人信息。 </p>
          <h3> 四、您的权利 </h3>
          <p> 按照中国相关的法律、法规、标准，我们保障您对自己的个人信息行使法定权利。 </p>
          <h3> 五、我们如何处理儿童的个人信息 </h3>
          <p> {{ appName }}小程序非常重视对未成年人个人信息的保护。根据相关法律法规的规定，若您是未成年人，在使用{{ appName }}小程序的服务前，应事先取得您的家长或法定监护人的书面同意。 </p>
          <p> 我们不会收集包括未成年人在内的个人信息。尽管当地法律和习俗对儿童的定义不同，但我们将不满14周岁的任何人均视为儿童。 </p>
          <h3> 六、本政策如何更新 </h3>
          <p> 我们可能会适时对本政策进行修订。当本政策的条款发生变更时，我们会在版本更新时向您展示变更后的《{{ appName }}小程序隐私政策》。 </p>
          <h3> 七、其他 </h3>
          <p> 《{{ appName }}小程序用户协议》中所规定的用户权利及信息安全保障措施均适用于{{ appName }}小程序用户。如《{{ appName }}小程序用户协议》与本政策存在不一致或矛盾之处，请以本政策为准。 </p>
          <p> 本政策的版权由{{ appName }}小程序所有，{{ appName }}小程序保留一切解释和修改权利。 </p>
          <h3> 八、如何联系我们 </h3>
          <p> 如果您对本政策的相关事宜有任何问题、意见或建议，请发送邮件至【<EMAIL>】与我们联系。 </p>
        </view>
    </view>
</template>

<script>
    import {getAppName} from "@/lib/context";

    export default {
        data(){
            return{
                appName: getAppName()
            }
        }
    }
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 20rpx;
    }
    .rich-text {
        background-color: #fff;
        padding: 30rpx;
        border-radius: 12rpx;
        overflow: hidden;
        color: #222;
        font-size: 17px;
        word-wrap: break-word;
        -webkit-hyphens: auto;
        -ms-hyphens: auto;
        hyphens: auto;
        text-align: justify;
        position: relative;
        z-index: 0;
    }
    .rich-text h3 {
        padding-bottom: 20rpx;
        padding-top: 30rpx;
    }
    .rich-text p {
        padding-bottom: 20rpx;
    }
    .rich-text view {
        padding-bottom: 20rpx;
    }
    .rich-text text {
        font-weight: bold;
    }
</style>
