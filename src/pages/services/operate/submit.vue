<template>
    <view class="content">
        <view class="ss-group-box">
            <view class="ssgb-tit">基本信息</view>
            <view class="ssgb-cnt">
                <view class="single-txt">
                    <view class="st-name">机构名称<text>*</text></view>
                    <view class="st-input">
                        <input type="number" placeholder="请输入机构名称" />
                    </view>
                    <view class="st-unit">元</view>
                </view>
                <view class="single-txt">
                    <view class="st-name">居民身份证<text>*</text></view>
                    <view class="st-input">
                        <input type="number" placeholder="请输入机构名称" />
                    </view>
                </view>
                <view class="single-txt">
                    <view class="st-name">机构名称<text>*</text></view>
                    <view class="st-input">
                        <input type="number" placeholder="请输入机构名称" />
                    </view>
                </view>



                <view class="single-txt">
                    <view class="st-name">机构名称<text>*</text></view>
                    <view class="dropdown-part">
                        <text>请选择</text>
                        <image src="../../../images/icon/arrow-down-s-line.png"></image>
                    </view>
                </view>
            </view>
        </view>

        <view class="mtextarea">
            <view class="mt-tit">培训要求<text>*</text></view>
            <view class="mt-cnt">
                <textarea value="" placeholder="请输入培训要求" />
            </view>
        </view>

        <view class="upload-file">
            <view class="uf-tit">5.上传应急联系电话（政府、医疗、急救、应急、消防、园区、企业、周边企业）<text>*</text></view>
            <view class="uf-tips">支持PDF、DOC、PPT、PNG、JPG、RAR等格式文件</view>
            <view class="adidci-doc-block">
                <view class="adidcidb-l">
                    <!-- <image src="../../../images/icon/file-excel-fill.png"></image> -->
                    <!-- <image src="../../../images/icon/file-word-fill.png"></image> -->
                    <!-- <image src="../../../images/icon/file-pdf-2-fill.png"></image> -->
                    <!-- <image src="../../../images/icon/file-ppt-fill.png"></image>-->
                    <image src="../../../images/icon/file-zip-fill.png"></image>
                    <!-- <image src="../../../images/icon/file-image-fill.png"></image> -->
                    <view class="adidcidbl-txt">
                        <view class="adidcidblt-tit">如果文本长度超过了元素的宽度，它就会被截断，并在末尾显示省略号。</view>
                        <view class="adidcidblt-size">ZIP - 12.22 KB</view>
                    </view>
                </view>
                <view class="adidcidb-r">
                    <image src="../../../images/icon/close-line.png"></image>
                </view>
            </view>

            <!-- 审核信息 -->
            <!-- <view class="uf-review-tip not-pass">审核不通过：文档不全</view> -->
            <view class="uf-review-tip pass">审核通过</view>
        </view>

        <view class="upload-file">
            <view class="uf-tit">培训要求<text>*</text></view>
            <view class="uf-tips">支持PDF、DOC、PPT、PNG、JPG、RAR等格式文件</view>
            <view class="uf-upbtn">
                <image src="../../../images/icon/upload-2-line.png"></image>
                <text>上传文件</text>
            </view>
        </view>


        <view class="ss-group-box">
            <view class="ssgb-tit">上传文件组</view>
            <view class="ssgb-cnt">
                <view class="upload-file">
                    <view class="uf-tit">培训要求<text>*</text></view>
                    <view class="uf-tips">支持PDF、DOC、PPT、PNG、JPG、RAR等格式文件</view>
                    <view class="uf-upbtn">
                        <image src="../../../images/icon/upload-2-line.png"></image>
                        <text>上传文件</text>
                    </view>
                </view>
                <view class="upload-file">
                    <view class="uf-tit">培训要求<text>*</text></view>
                    <view class="uf-tips">支持PDF、DOC、PPT、PNG、JPG、RAR等格式文件</view>
                    <view class="uf-upbtn">
                        <image src="../../../images/icon/upload-2-line.png"></image>
                        <text>上传文件</text>
                    </view>
                </view>
            </view>
        </view>

        <view class="sic-foot-eb"></view>
        <view class="sic-foot">
            <view class="sicf-btn">
                <text>提交需求</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            sid: '',
            flow_step_id: '',
            inputs: []
        };
    },
    onLoad(e) {
        this.sid = e.sid
        this.flow_step_id = e.flow_step_id
        this.initData()
    },
    methods: {
        initData() {

        }
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }

    .ssgb-tit {
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        color: #999;
        padding: 0 30rpx;
    }
    .ss-group-box {
        margin-bottom: 30rpx;
    }

    /* 单行文本 */
    .single-txt {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        background-color: #fff;
        padding: 0 30rpx;
        font-size: 28rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .st-input {
        flex: 1;
    }
    .st-input input {
        height: 100rpx;
        line-height: 100rpx;
    }
    .st-unit {
        font-size: 24rpx;
        color: #999;
    }
    .st-name {
        width: 200rpx;
    }
    .st-name text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }
    .ssgb-cnt .single-txt {
        margin-bottom: 1px;
    }
    .ssgb-cnt .upload-file {
        margin-bottom: 1px;
    }

    /* 多行文本 */
    .mtextarea {
        margin-bottom: 30rpx;
    }
    .mt-tit {
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 30rpx;
        color: #999;
        font-size: 28rpx;
    }
    .mt-tit text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }
    .mt-cnt {
        display: flex;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .mt-cnt textarea {
        flex: 1;
        padding: 30rpx;
        font-size: 28rpx;
    }

    /* 单选 */
    .dropdown-part {
        display: flex;
        align-items: center;
    }
    .dropdown-part text {
        padding-right: 20rpx;
        color: #999;
    }
    .dropdown-part image {
        width: 32rpx;
        height: 32rpx;
    }

    /* 上传框 */
    .upload-file {
        position: relative;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding-bottom: 1px;
        margin-bottom: 30rpx;
    }
    .uf-tit {
        font-size: 28rpx;
        padding: 30rpx 30rpx 0;
    }
    .uf-tit text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }
    .uf-tips {
        font-size: 24rpx;
        color: #999;
        padding: 10rpx 30rpx 20rpx;
    }
    .uf-upbtn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        color: #390ABC;
    }
    .uf-upbtn image {
        width: 32rpx;
        height: 32rpx;
    }
    .uf-upbtn text {
        font-size: 28rpx;
        padding-left: 10rpx;
        font-weight: bold;
    }

    /* 上传后文件样式 */
    .adidci-doc-block {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #e7e7e7;
        border-radius: 12rpx;
        padding: 20rpx;
    }
    .adidcidbl-txt {
        padding-left: 20rpx;
    }
    .adidcidb-l {
        display: flex;
        align-items: center;
    }
    .adidcidb-l image {
        width: 64rpx;
        height: 64rpx;
    }
    .adidcidblt-tit {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300rpx;
        font-size: 28rpx;
    }
    .adidcidblt-size {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .adidcidb-r image {
        width: 32rpx;
        height: 32rpx;
    }

    .upload-file .adidci-doc-block {
        margin: 0 30rpx 30rpx;
        background-color: #F3F3FF;
        border: none;
    }

    .uf-review-tip {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 24rpx;
        background-color: red;
        color: #fff;
        padding: 4rpx 8rpx;
    }
    .uf-review-tip.pass {
        background-color: #07c160;
    }


    /* 底部按钮 */
    .sic-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 15rpx 15rpx env(safe-area-inset-bottom);
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .sicf-btn {
        height: 90rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390abc;
        color: #fff;
        border-radius: 12rpx;
    }
    .sicf-btn.disabled {
        background-color: #999;
        color: #fff;
    }
    .sicf-btn image {
        width: 36rpx;
        height: 36rpx;
    }
    .sicf-btn text {
        font-weight: bold;
    }
    .sic-foot-eb {
        height: 100rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
</style>