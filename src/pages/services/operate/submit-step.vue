<template>
    <view class="content">
        <view class="select-warp-box">
            <view class="swb-tit">选择类别</view>
            <view class="swb-cnt">
                <picker  @change="industryChange" mode='selector' :value="0" :range="industryRange" range-key="name">
                    <view class="select-nomal">
                        <view class="sn-tit">行业类别</view>
                        <view class="sn-txt" >
                            <text>{{industryText}}</text>
                            <image src="../../../images/icon/arrow-down-s-line.png"></image>
                        </view>
                    </view>
                </picker>
                <picker class="swb-cnt" @change="enterpriseChange" mode='selector' :value="0" :range="enterpriseRange" range-key="name">
                    <view class="select-nomal" >
                        <view class="sn-tit">企业类别</view>
                        <view class="sn-txt" >
                            <text>{{enterpriseText}}</text>
                            <image src="../../../images/icon/arrow-down-s-line.png"></image>
                        </view>
                    </view>
                </picker>
            </view>
        </view>

        <view class="ss-file-list" v-if="desc.length">
            <view class="ssfl-tit">需准备的资料清单</view>
            <view class="ssfl-cnt">
                <view class="ssflc-item" v-for="(item, index) in desc" :key="index">{{ index + 1 }}、{{item}}；</view>
            </view>
        </view>

        <view class="sic-foot-eb"></view>
        <view class="sic-foot">
            <view class="sicf-btn" :class="{disabled: disabledSubmit}" @click="submit">
                <text>准备好了，开始提交</text>
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert} from "@/lib/utils";

export default {
    data() {
        return {
            industryRange: [],
            enterpriseRange: [],
            desc: [],
            industryIndex: '',
            enterpriseIndex: ''
        }
    },
    emits: ['update:industryId', 'update:enterpriseId', 'formStep'],
    props:["industryId", 'enterpriseId', 'project_id'],
    computed: {
        industryText() {
            if (this.industryIndex) {
                return this.industryRange[this.industryIndex].name
            } else  {
                return '请选择'
            }
        },
        enterpriseText() {
            if (this.enterpriseIndex) {
                return this.enterpriseRange[this.enterpriseIndex].name
            } else  {
                return '请选择'
            }
        },
        disabledSubmit() {
            if (!this.enterpriseIndex || !this.industryIndex) {
                return true
            }
            if (!this.desc.length) {
                return true;
            }
            return false
        }
    },
    mounted() {
        this.getEnterprise()
        this.getIndustry()
    },
    methods: {
        getEnterprise() {
            api.get(`ers/enterprises?project_id=${this.project_id}&industry_id=${this.industryId}`).then(res => {
                this.enterpriseRange = res
            }).catch(err => {
                alert(err.message)
            })
        },
        getIndustry() {
            api.get(`ers/industries?project_id=${this.project_id}&enterprise_id=0`).then(res => {
                this.industryRange = res

            }).catch(err => {
                alert(err.message)
            })
        },
        enterpriseChange(e) {
            this.enterpriseIndex = e.detail.value
            this.$emit('update:enterpriseId', this.enterpriseRange[this.enterpriseIndex].id)
            this.$nextTick(() => {
                //this.getIndustry()
                this.getDesc()
            })
        },
        industryChange(e) {
            this.industryIndex = e.detail.value
            this.$emit('update:industryId', this.industryRange[this.industryIndex].id)
            this.$nextTick(() => {
                this.getEnterprise()
                this.getDesc()
            })
        },
        getDesc() {
            if (this.industryId && this.enterpriseId) {
                let data = {
                    project_id: this.project_id,
                    industry_id: this.industryId,
                    enterprise_id: this.enterpriseId,
                }
                api.get("ers/form/preview", data).then(res => {
                    this.desc = res.desc
                }).catch(err => alert(err.message))
            }
        },
        submit() {
            if (this.disabledSubmit) {
                return
            }
            if (this.enterpriseIndex && this.industryIndex) {
                this.$emit("formStep")
            }
        }
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .select-warp-box {
        margin-bottom: 30rpx;
    }
    .swb-tit {
        height: 80rpx;
        line-height: 80rpx;
        color: #999;
        padding: 0 30rpx;
        font-size: 28rpx;
    }
    .select-nomal {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        height: 100rpx;
        font-size: 28rpx;
        margin-bottom: 1px;
    }
    .sn-tit {
        padding: 0 30rpx;
        width: 200rpx;
    }
    .sn-tit text {
        padding-left: 10rpx;
        font-weight: bold;
        color: red;
    }
    .sn-txt {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 30rpx;
    }
    .sn-txt image {
        width: 32rpx;
        height: 32rpx;
    }
    .sn-txt text {
        padding-right: 20rpx;
        color: #999;
    }


    .ssfl-tit {
        height: 80rpx;
        line-height: 80rpx;
        color: #999;
        padding: 0 30rpx;
        font-size: 28rpx;
    }
    .ssfl-cnt {
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding: 30rpx;
        font-size: 28rpx;
    }
    .ssflc-item {
        padding-bottom: 20rpx;
        line-height: 1.6;
    }
    .ssflc-item:last-child {
        padding-bottom: 0;
    }


    /* 底部按钮 */

    .sic-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 20rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        z-index: 10;
    }
    .sicf-btn {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390abc;
        color: #fff;
        border-radius: 12rpx;
        margin-bottom: env(safe-area-inset-bottom);
    }
    .sicf-btn image {
        width: 36rpx;
        height: 36rpx;
    }
    .sicf-btn text {
        font-weight: bold;
    }
    .sicf-btn.disabled {
        background-color: #999;
    }
    .sic-foot-eb {
        height: 148rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .sicf-btn.disabled {
        background-color: #999;
    }
</style>