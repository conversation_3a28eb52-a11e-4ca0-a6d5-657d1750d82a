<template>
    <view class="content">
        <view class="services-detail-info">
            <view class="sdi-sn">编号：{{ sid }}</view>
            <view class="sdi-top">
                <view class="sdit-name">{{title}}</view>
                <view class="sdit-staus processed"  v-if="status == 1" :class="{cur: status == 1}">待办</view>
                <view class="sdit-staus processed" v-else-if="status == 2" :class="{cur: status == 2}">受理中</view>
                <view class="sdit-staus end" v-else :class="{cur: status == 3}">已完结</view>
            </view>
            <view class="sdi-foot">
                提交于 <uni-dateformat :date="created_at" format="yyyy年MM月dd日 hh:mm"></uni-dateformat>
            </view>
            <view class="contact-services" @click="goCustomer">
                <image src="../../images/icon/customer-service-line-02.png" mode=""></image>
            </view>
        </view>
        <view class="sdi-doc">
            <view class="sdid-tit">服务信息</view>
            <view class="sdid-cnt">
                <template  v-for="(item, index) in forms" :key="index">
                    <view class="data-fold-item " :class="{unfold: item.unfold}" v-if="item.type == 'image' || item.type == 'file'">
                        <view class="dfi-top" @click="item.unfold = !item.unfold">
                            <view class="dfit-tit">{{item.title}}</view>
                            <view class="dfit-r" v-if="item.value">
                                <template>
                                    <image  src="@/images/icon/arrow-down-s-line.png" v-if="!item.unfold  != 'false'"></image>
                                    <image  src="@/images/icon/arrow-up-s-line.png" v-else></image>
                                </template>
                            </view>
                            <view class="dfit-r"  v-else>
                                <view class="dfitr-txt" >暂未提交</view>
                            </view>
                        </view>
                        <view class="dfi-cnt" v-if="item.value">
                            <template  v-for="(item, index) in item.value" :key="index">
                                <file_img :mime="item.mime" :item="item"/>
                            </template>
                        </view>
                    </view>
                    <template v-else>
                        <view class="data-fold-item" :class="{unfold: item.unfold}" >
                            <view class="dfi-top" @click="item.unfold = !item.unfold">
                                <view class="dfit-tit">{{item.title}}</view>
                                <view class="dfit-r" v-if="item.value">
                                    <template>
                                        <image  src="@/images/icon/arrow-down-s-line.png" v-if="item.unfold != 'false'"></image>
                                        <image  src="@/images/icon/arrow-up-s-line.png" v-else></image>
                                    </template>
                                </view>
                                <view class="dfit-r" v-else>
                                    <view class="dfitr-txt" >暂未提交</view>
                                </view>
                            </view>
                            <view class="dfi-cnt" v-if="item.value">
                                <template v-if="item.type == 'checkbox'">
                                    <view class="dfic-txt" >
                                      <text v-for="(checkbox, checkboxIndex) in item.value" :key="checkboxIndex">{{checkbox}}
                                          <template v-if="checkboxIndex + 1  != item.value?.length">,</template>
                                      </text>
                                    </view>
                                </template>
                                <view class="dfic-txt" v-else>
                                    {{item.value}}
                                </view>
                            </view>
                        </view>
                    </template>
                </template>
            </view>
        </view>

        <view class="sdi-record-box">
            <view class="sdirb-tit">服务记录</view>

            <template v-for="(item, index) in flows" :key="index">
                <template v-for="(flow, flowIndex) in item.sub_flows" :key="flowIndex">
                    <flows_payment_final v-if="flow.action === 'payment_final' || flow.action === 'payment'" :status="flow.status" :data="item" :flow="flow" :people="flow?.people" />
                    <flows_payment_stage v-else-if="flow.action === 'payment:pay'" :status="flow.status" :data="item" :flow="flow" :people="flow?.people"/>
                    <flows_solution_download v-else-if="flow.action === 'solution_download:submit'" :status="flow.status" :data="item" :flow="flow" :people="flow?.people"/>
                    <flows_solution_preview v-else-if="flow.action === 'solution_preview:confirm'" :status="flow.status" :data="item" :flow="flow" :people="flow?.people" />
                    <flow_general v-else :status="flow?.status" :data="item" :people="flow?.people" :flow="flow" ></flow_general>
                </template>
            </template>

            <view class="sdirb-end" v-if="status == 3">
                <view class="sdirbe-circle"></view>
                <view class="sdirbe-txt" >完结 <uni-dateformat :date="finished_at" format="MM-dd hh:mm" v-if="finished_at"></uni-dateformat></view>
            </view>
            <view class="sdirb-end" v-else>
                <view class="sdirbe-circle"></view>
                <view class="sdirbe-txt">等待完结</view>
            </view>
        </view>

        <view class="sdi-foot-eb"></view>
        <view class="sdi-foot-box">
            <view class="adifb-main" @click="handle">
                <view class="adifbm-process" v-if="status == 2">需求受理中，请稍候</view>
                <view class="adifbm-todo-btn" v-else-if="status == 1 && (paymentStage?.status == 1)">支付预付款</view>
                <view class="adifbm-todo-btn" v-else-if="status == 1 && (paymentFinal?.status == 1)">确认方案并付款</view>
                <view class="adifbm-todo-btn" v-else-if="status == 1 && (solutionPreview?.status == 1)">确认方案</view>
                <view class="adifbm-todo-btn" v-else-if="status == 1 && (solutionDownload?.status == 1)">下载方案</view>
                <view class="adifbm-todo-btn" v-else-if="status == 1 && form?.status == 1">完善资料</view>
                <view class="adifbm-todo-btn" v-else-if="status == 1 && payment">去支付</view>
                <view class="adifbm-process" v-else-if="status == 3">完结</view>
                <!-- <view class="adifbm-more-btn">
                    <image src="../../images/icon/more-2-line.png"></image>
                </view> -->
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert, getFormatType, showDelayLoading} from "@/lib/utils";
import progress_status from "@/pages/services/progress_status.vue";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import Flows_form from "@/pages/services/form/flows_form.vue";
import Flows_payment_final from "@/pages/services/form/flows_payment_final.vue";
import Flows_payment_stage from "@/pages/services/form/flows_payment_stage.vue";
import Flows_solution_download from "@/pages/services/form/flows_solution_download.vue";
import Flows_solution_preview from "@/pages/services/form/flows_solution_preview.vue";
import File_img from "@/pages/services/form/file_img.vue";
import Flow_general from "@/pages/services/form/flow_general.vue";
import {getAppName} from "@/lib/context";
export default {
    data() {
        return {
            sid: '',
            project_id: '',
            forms: [],
            flows: [],
            status: '',
            title: '',
            created_at: "",
            finished_at: "",
            project: {},
            appName: getAppName()
        }
    },
    computed: {
        ...mapState(useUserStore, ['user']),
        form() {
            return this.flows.find(res => res.module == 'form')
        }
        ,paymentStage() {
            return this.flows.find(res => res.module == 'payment_stage')
        },
        solutionPreview() {
            return this.flows.find(res => res.module == 'solution_preview')
        },
        paymentFinal() {
            return this.flows.find(res => res.module == 'payment_final')
        },
        solutionDownload() {
            return this.flows.find(res => res.module == 'solution_download')
        },
        payment() {
            return this.flows.find(res => res.module == 'payment')
        },
    },
    components: {
        Flow_general,
        File_img,
        Flows_solution_preview,
        Flows_solution_download,
        Flows_payment_stage,
        Flows_payment_final,
        Flows_form,
        progress_status
    },
    onLoad(e) {
        this.sid = e.sid
        this.project_id = e.project_id
        this.initData()
        this.initProjects()
        uni.$on("services_list_order", () => {
            this.initData()
        })
    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("services_list_order")
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.initData()

    },
    methods: {
        initProjects() {
            api.get(`ers/projects/${this.project_id}`).then(res => {
                this.title = res.title
            }).catch(err => alert(err.message))
        },
        initData() {
            const hideLoading = showDelayLoading("加载中", 200)

            api.get(`ers/orders/${this.sid}?project_id=${this.project_id}`).then(res => {
                this.flows = res.flows
                this.finished_at = res.finished_at
                console.log(this.finished_at)
                this.forms = this.flows.find(res => res.module == 'form')

                this.forms = this.forms.data
                this.forms.forEach(res => {
                    res.unfold = false

                })
                this.status = res.status
                this.created_at = res.created_at
                this.project = res.project
                hideLoading()
            }).catch(err => {
                hideLoading()
                alert(err.message)
            }).finally(() => {
                uni.stopPullDownRefresh();
            })
        },
        getFormatType(type) {
            return getFormatType(type);
        },
        handle() {
            if (this.status == 2) {
                return;
            }
            let module = this.flows.find( res => res.status == 1);
            if (module == undefined) {
                return;
            }
            if (module.module == "form") {
                uni.navigateTo({
                    url: '/pages/services/form/form?sid=' + this.sid + "&flow_step_id=" + module.id+ "&project_id=" + this.project_id
                })
            } else if(module.module == 'payment_stage' || module.module == 'payment_final' ) {
                uni.navigateTo({
                    url: '/pages/services/operate/advance-pay?sid=' + this.sid + "&flow_step_id=" + module.id + "&project_id=" + this.project_id
                })
            } else if(module.module == 'solution_preview') {
                uni.navigateTo({
                    url: '/pages/services/operate/solution_preview?sid=' + this.sid + "&flow_step_id=" + module.id + "&project_id=" + this.project_id
                })
            } else if(module.module == 'solution_download') {
                uni.navigateTo({
                    url: '/pages/services/operate/download?sid=' + this.sid + "&flow_step_id=" + module.id + "&project_id=" + this.project_id
                })
            } else if(module.module == 'payment') {
                uni.navigateTo({
                    url: '/pages/services/operate/pay?sid=' + this.sid + "&flow_step_id=" + module.id + "&project_id=" + this.project_id
                })
            }
        },
        goCustomer() {
            uni.navigateTo({
                url: '/pages/me/service'
            })
        }
    },
    onShareAppMessage() {
        return {
            title: this.appName,
            path: '/pages/services/index'
        };
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }

    .content {
        padding-bottom: 30rpx;
    }

    .services-detail-info {
        position: relative;
        background-color: #fff;
        border-radius: 12rpx;
        margin: 30rpx 30rpx 0;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding: 30rpx;
    }
    .sdi-sn {
        font-size: 24rpx;
        color: #999;
        padding-bottom: 6rpx;
    }
    .sdi-top {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
    }
    .sdit-name {
        font-size: 32rpx;
        font-weight: bold;
    }
    .sdit-staus {
        font-weight: bold;
        font-size: 28rpx;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
        margin-left: 20rpx;
    }
    .sdit-staus.todo {
        border: 1px solid #FF0000;
        color: #FF0000;
    }
    .sdit-staus.processed {
        border: 1px solid #090abc;
        color: #090abc;
    }
    .sdit-staus.end {
        border: 1px solid #999;
        color: #999;
    }
    .sdi-cnt {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
    }
    .sdi-cnt image {
        width: 60rpx;
        height: 60rpx;
        border-radius: 100%;
    }
    .sdi-cnt text {
        padding-left: 20rpx;
        font-size: 24rpx;
    }
    .sdi-foot {
        font-size: 24rpx;
        color: #999;
    }

    .contact-services {
        position: absolute;
        right: 10rpx;
        bottom: 0;
        width: 100rpx;
        height: 100rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .contact-services image {
        width: 40rpx;
        height: 40rpx;
    }


    .sdi-doc {
        background-color: #fff;
        border-radius: 12rpx;
        margin: 30rpx 30rpx 0;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .sdid-tit {
        padding: 30rpx;
        font-size: 32rpx;
        font-weight: bold;
    }
    .sdid-cnt {
        font-size: 28rpx;
    }
    .adidc-item {
        padding: 0 30rpx 30rpx;
    }
    .adidci-tit {
        color: #999;
        padding-bottom: 10rpx;
    }
    .adidci-txt {
    }

    .adidci-doc-block {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 12rpx;
        padding: 20rpx;
        background-color: #f3f3ff;
    }
    .adidcidbl-txt {
        padding-left: 20rpx;
    }
    .adidcidb-l {
        display: flex;
        align-items: center;
    }
    .adidcidb-l image {
        width: 64rpx;
        height: 64rpx;
    }
    .adidcidblt-tit {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300rpx;
        font-size: 28rpx;
    }
    .adidcidblt-size {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .adidcidb-r image {
        width: 32rpx;
        height: 32rpx;
    }


    /* 服务记录 */
    .sdi-record-box {
        background-color: #fff;
        border-radius: 12rpx;
        margin: 30rpx 30rpx 0;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .sdirb-tit {
        padding: 30rpx;
        font-size: 32rpx;
        font-weight: bold;
    }
    .sdirb-node-box {
        padding: 0 30rpx 0 20rpx;
    }
    .sdirbnbr-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .sdirbnbrt-l {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .sdirbnb-r {
        flex: 1;
        padding-left: 30rpx;
    }
    .snb-head-img {
        width: 100rpx;
        height: 100rpx;
        border-radius: 100%;
        border: 10rpx solid #fff;
    }
    .sdirbnbpl-name {
        padding-left: 20rpx;
    }
    .sdirbnbpln-tit {
        font-size: 28rpx;
        font-weight: bold;
    }
    .sdirbnbpln-txt {
        font-size: 28rpx;
        color: #999;
        padding-top: 10rpx;
    }
    .sdirbnbp-staus image {
        width: 32rpx;
        height: 32rpx;
    }

    .sdirbnbp-staus view {
        display: flex;
        align-items: center;
        font-size: 28rpx;
    }
    .sdirbnbp-staus view text {
        padding-left: 10rpx;
        font-weight: bold;
    }
    .sdirbnbp-staus .todo {
        color: #ea712e;
    }
    .sdirbnbp-staus .process {
        color: #390ABC;
    }
    .sdirbnbp-staus .done {
        color: #fba822;
    }

    .sdirbnbr-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .sdirbnbr-cnt {
        padding: 0 0 50rpx 80rpx;
        border-left: 1px dotted #CCC;
        margin-left: 60rpx;
    }
    .sdirbnbr-cnt .adidci-doc-block {
        margin-bottom: 20rpx;
    }
    .sdirbnbrc-time {
        font-size: 24rpx;
        color: #999;
    }

    .sdirb-end {
        display: flex;
        align-items: flex-start;
        font-size: 28rpx;
        color: #999;
        padding-bottom: 30rpx;
        padding-left: 62rpx;
        padding-top: 6rpx;
    }
    .sdirbe-circle {
        width: 30rpx;
        height: 30rpx;
        border-radius: 100%;
        border: 1px solid #CCC;
        margin-top: 4rpx;
    }
    .sdirbe-txt {
        padding-left: 60rpx;
    }
    .sdirbe-txt text {
        display: block;
        font-size: 24rpx;
        padding-top: 10rpx;
    }


    /* 底部菜单 */
    .sdi-foot-box {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 10;
    }
    .adifb-main {
        display: flex;
        align-items: center;
        margin: 20rpx;
    }
    .adifbm-todo-btn {
        flex: 1;
        height: 100rpx;
        text-align: center;
        border: 1px solid #390ABC;
        background-color: #f3f3ff;
        color: #090abc;
        font-weight: bold;
        border-radius: 12rpx;
        line-height: 100rpx;
    }
    .adifbm-process {
        flex: 1;
        height: 100rpx;
        text-align: center;
        background-color: #F3F3FF;
        color: #999;
        border-radius: 12rpx;
        line-height: 100rpx;
    }
    .adifbm-more-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 100rpx;
        border-radius: 12rpx;
        margin-left: 20rpx;
    }
    .adifbm-more-btn image {
        width: 32rpx;
        height: 32rpx;
    }
    .sdi-foot-eb {
        height: 140rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }


    /* 支付信息模块 */
    .sdirbnbrc-pay-info {
        background-color: #F3F3FF;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-bottom: 20rpx;
    }
    .sdirbnbrcpi-item {
        padding-bottom: 30rpx;
    }
    .sdirbnbrcpi-item:last-child {
        padding-bottom: 0;
    }
    .sdirbnbrcpi-tit {
        font-size: 24rpx;
        color: #999;
        padding-bottom: 10rpx;
    }
    .sdirbnbrcpi-txt {
        font-size: 28rpx;
    }
    .sdirbnbrcpi-img {
    }
    .sdirbnbrcpi-img image {
        width: 200rpx;
        height: 200rpx;
        border-radius: 12rpx;
        vertical-align: top;
    }


    /* 资料折叠展示模块 */
    .data-fold-item {
        border-top: 1px solid #F3F3FF;
        margin: 0 30rpx;
    }
    .dfi-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
    }
    .dfit-r {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 160rpx;
    }
    .dfit-r image {
        width: 32rpx;
        height: 32rpx;
    }
    .dfi-cnt {
        display: none;
        padding: 0 0 20rpx;
    }
    .dfitr-txt {
        color: #999;
        font-size: 24rpx;
    }
    .dfic-txt {
        padding: 20rpx;
        background-color: #F3F3FF;
        border-radius: 12rpx;
        line-height: 1.6;
    }


    .unfold .dfit-r image {
        transform: scaleY(-1);
    }
    .unfold .dfi-cnt {
        display: block;
    }
</style>