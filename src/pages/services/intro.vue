<template>
    <view class="content">
        <view class="service-intro-cnt" >
            <mp-html :content="intro" />
        </view>

        <view class="sic-foot-eb"></view>
        <view class="sic-foot">
            <view class="sicf-btn" :class="{disabled: disabledSubmit}" @click="submitDemand">
                <text>提交需求</text>
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert} from "@/lib/utils";
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

export default {
    data() {
        return {
            id: 0,
            intro: '',
            disabledSubmit: false,
        }
    },
    components: {
        mpHtml
    },
    onLoad(e) {
        this.id = e.id
        this.initData()
    },
    methods: {

        initData() {
            api.get(`ers/projects/${this.id}`).then(res => {
                this.intro = res.intro
                if (!res.is_bind_category) {
                    this.getForm()
                }
            }).catch(err => alert(err.message))
        },
        getForm() {
            let data = {
                project_id: this.id,
                industry_id: 0,
                enterprise_id: 0,
            }
            api.get(`ers/form/preview`, data).then(res => {
               if (!res.desc.length) {
                   this.disabledSubmit = true
               }
            }).catch(err => alert(err.message))
        },
        submitDemand() {
            if (this.disabledSubmit) {
                return
            }
            api.get("ers/projects/"+ this.id).then(res => {
                uni.redirectTo({
                    url: "/pages/services/form/form?sid=0&flow_step_id=&project_id=" + this.id + "&back=2"
                })
            }).catch(err => alert(err.message))
        },
        addOrder() {

            api.post("ers/orders", {project_id: this.id}).then(res => {
                let module = res.flows.find( res => res.status == 1);
                uni.redirectTo({
                    url: '/pages/services/form/form?sid=' + res.sid + "&flow_step_id=" + module.id + "&project_id=" + this.id + "&back=2"
                })
            }).catch(err => alert(err.message))
        }
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .content {
        position: relative;
    }
    .sic-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 20rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        z-index: 10;
    }
    .sicf-btn {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390abc;
        color: #fff;
        border-radius: 12rpx;
        margin-bottom: env(safe-area-inset-bottom);
    }
    .sicf-btn image {
        width: 36rpx;
        height: 36rpx;
    }
    .sicf-btn text {
        font-weight: bold;
    }
    .sicf-btn.disabled {
        background-color: #999;
    }
    .sic-foot-eb {
        height: 148rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
</style>