<template>
    <view class="info-tips-box" v-if="detail?.status == 0">
        <view class="info-tips">正在审核中，请稍候，您现在看到的是预览页面</view>
    </view>
    <view class="content">
        <view class="exp-info-main" v-if="detail">
            <view class="eim-teacher">
                <image :src="detail.photo_url" mode="widthFix" @click="preview([detail.photo_url], detail.photo_url)"></image>
                <text>{{ detail.name }}</text>
            </view>
            <view class="eim-together">
                <view class="eimt-item" v-if="detail.education">
                    <view class="eimti-tit">最高学历</view>
                    <view class="eimti-txt">{{ detail.education }}</view>
                </view>
                <view class="eimt-item" v-if="detail.occupation">
                    <view class="eimti-tit">职称/职务</view>
                    <view class="eimti-txt">{{ detail.occupation }}</view>
                </view>
                <view class="eimt-item" v-if="detail.residence">
                    <view class="eimti-tit">常住地</view>
                    <view class="eimti-txt">{{ detail.residence }}</view>
                </view>
                <view class="eimt-item" v-if="detail.industry">
                    <view class="eimti-tit">从事行业</view>
                    <view class="eimti-txt">{{ detail.industry }}</view>
                </view>
                <view class="eimt-item" v-if="detail.work_year">
                    <view class="eimti-tit">从事年限</view>
                    <view class="eimti-txt">{{ detail.work_year }}年</view>
                </view>
                <view class="eimt-item" v-if="detail.fields.length">
                    <view class="eimti-tit">擅长领域和行业</view>
                    <view class="eimti-txt">{{ detail.fields.join() }}</view>
                </view>
            </view>
            <view class="eim-part" v-if="detail.course_scopes">
                <view class="eimp-tit">安全服务方向</view>
                <text class="eimp-cnt">
                    {{ detail.services.join() }}
                </text>
            </view>
            <view class="eim-part" v-if="detail.course_scopes">
                <view class="eimp-tit">授课范围</view>
                <text class="eimp-cnt">
                    {{ detail.course_scopes }}
                </text>
            </view>
            <view class="eim-part" v-if="detail.typical_cases">
                <view class="eimp-tit">安全咨询与培训典型案例</view>
                <text class="eimp-cnt">
                    {{ detail.typical_cases }}
                </text>
            </view>
            <view class="eim-part" v-if="detail.scene_photo_urls.length">
                <view class="eimp-tit">现场照片</view>
                <view class="eimp-cnt"></view>
                <view class="eimp-img" v-for="(item, index) in detail.scene_photo_urls" :key="index">
                    <image :src="item" mode="widthFix" @click="preview(detail.scene_photo_urls, item)"></image>
                </view>
            </view>
            <view class="eim-part" v-if="detail.serve_customers">
                <view class="eimp-tit">服务客户</view>
                <text class="eimp-cnt">
                    {{ detail.serve_customers }}
                </text>
            </view>
            <view class="eim-part" v-if="detail.teaching_styles">
                <view class="eimp-tit">教学风格</view>
                <text class="eimp-cnt">
                    {{ detail.teaching_styles }}
                </text>
            </view>
            <view class="eim-part" v-if="detail.remark">
                <view class="eimp-tit">备注</view>
                <text class="eimp-cnt">
                    {{ detail.remark }}
                </text>
            </view>
            <view class="eim-part" v-if="detail.cert_urls.length">
                <view class="eimp-tit">资格证书</view>
                <view class="eimp-cnt"></view>
                <view class="eimp-img" v-for="(item, index) in detail.cert_urls" :key="index">
                    <image :src="item" mode="widthFix" @click="preview(detail.cert_urls, item)"></image>
                </view>
            </view>
        </view>

        <!-- 底部导航 -->
        <view class="sic-foot-eb" v-if="detail?.status > 0 && myDetail?.id != detail?.id"></view>
        <view class="sic-foot" v-if="detail?.status > 0 && myDetail?.id != detail?.id">
            <navigator url="/pages/services/index" class="sicf-btn">
                <image src="../../images/icon/calendar-schedule-line.png"></image>
                <text>预约专家</text>
            </navigator>
            <view class="sicf-btn contact" @click="makeCall">
                <image src="../../images/icon/phone-line.png"></image>
                <text>联系专家</text>
            </view>
        </view>
    </view>
</template>

<script>
import {alert, getFormatType, showDelayLoading} from "@/lib/utils";
import api from "@/lib/api";
import { useUserStore } from "@/store/user";
import {mapState} from "pinia";

export default {
    onLoad(e) {
        if (!e.id) {
            uni.showModal({
                content: "参数错误",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        uni.navigateBack()
                    }
                }
            })
        }
        this.id = e.id
        this.getConfig()
        this.getDetail()
        this.getMyDetail()
    },
    onShareAppMessage(res) {
        if (res.from === 'button') {// 来自页面内分享按钮
            console.log(res.target)
        }
        let path = "/pages/expert/detail?id=" + this.detail.id

        if (this.user) {
            path += "&ref=" + this.user.uuid;
        }

        return {
            title: this.detail.title,
            path
        };
    },
    data() {
        return {
            id: undefined,
            config: {},
            detail: undefined,
            myDetail: undefined
        }
    },
    computed: {
        ...mapState(useUserStore, ['user'])
    },
    methods: {
        getDetail() {
            const hideLoading = showDelayLoading('加载中', 200)
            api.get(`expert/${this.id}`).then(res => {
                this.detail = res
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        getMyDetail() {
            if (!this.user) {
              return
            }
            const hideLoading = showDelayLoading('加载中', 200)
            api.get(`expert/my-show`).then(res => {
                this.myDetail = res
            }).catch(err => {
              if (err.code != 404) {
                alert(err.message)
              }
            }).finally(() => {
                hideLoading()
            })
        },
        getConfig() {
            const hideLoading = showDelayLoading('加载中', 200)
            api.get(`expert/form-config`).then(res => {
                this.config = res
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        makeCall() {
            uni.makePhoneCall({
                phoneNumber: this.config.phone
            })
        },
        getFormatType(type) {
            return getFormatType(type);
        },
        preview(urls, currentUrl) {
            uni.previewImage({
                urls: urls,
                current: currentUrl,
            });
        }
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .content {
        padding: 30rpx;
    }

    .eim-teacher {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 12rpx;
        padding: 20rpx 20rpx 40rpx;
    }
    .eim-teacher image {
        width: 200rpx;
        border-radius: 12rpx;
    }
    .eim-teacher text {
        font-size: 36rpx;
        font-weight: bold;
        padding-top: 20rpx;
    }

    .eim-together {
        display: grid;
        grid-template-columns: 1fr 1fr;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        gap: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        margin-bottom: 30rpx;
    }
    .eimti-tit {
        font-size: 28rpx;
        padding-bottom: 4rpx;
        font-weight: bold;
    }
    .eimti-txt {
        font-size: 28rpx;
        line-height: 1.6;
    }
    .eim-part {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        margin-bottom: 30rpx;
    }
    .eimp-tit {
        font-size: 28rpx;
        padding-bottom: 4rpx;
        font-weight: bold;
    }
    .eimp-cnt {
        font-size: 28rpx;
        line-height: 1.6;
    }
    .eimp-img {
        padding-top: 20rpx;
    }
    .eimp-img image {
        width: 100%;
        border-radius: 12rpx;
        vertical-align: top;
    }

    .info-tips-box {
        height: 80rpx;
    }
    .info-tips {
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        background-color: #EAF8E3;
        color: #52c41a;
        font-size: 28rpx;
        text-align: center;
        height: 80rpx;
        line-height: 80rpx;
    }
    /* 底部按钮 */
    .sic-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 15rpx 15rpx env(safe-area-inset-bottom);
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .sicf-btn {
        flex: 1;
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390abc;
        color: #fff;
        border-radius: 12rpx;
        border: 1px solid transparent;
    }
    .sicf-btn image {
        width: 36rpx;
        height: 36rpx;
    }
    .sicf-btn text {
        font-weight: bold;
        padding-left: 10rpx;
    }
    .sicf-btn.contact {
        margin-left: 10rpx;
        background-color: #f3f3ff;
        border: 1px solid #390ABC;
        color: #390ABC;
    }
    .sic-foot-eb {
        height: 100rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
</style>