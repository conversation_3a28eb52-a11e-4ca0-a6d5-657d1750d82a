<template>
    <view class="content">
        <uv-skeletons v-if="!loaded" :loading="loading" :skeleton="skeleton"></uv-skeletons>

        <view v-else class="me-together-box">
                <view class="me-index-top" v-if="user">
                    <view class="mit-l">
                        <image :src="user?.avatar"></image>
                        <text>{{user ? user.nickname : "未登录"}}</text>
                    </view>
                    <navigator url="/pages/me/setting" class="mit-setting">
                        <image src="../../images/icon/settings-line.png"></image>
                    </navigator>
                </view>

                <view class="no-login" v-else>
                    <view class="nl-left">
                        <view class="nll-t">您尚未登录</view>
                        <view class="nll-p">登录后体验完整功能</view>
                    </view>
                    <view class="nl-right" @click="goLogin">去登录</view>
                </view>
                <template v-if="user">
                    <view class="me-section">
                        <view class="ms-item" @click="goPage('/pages/me/order')" >
                            <image src="../../images/coloricon/dingdan.png"></image>
                            <text>订单</text>
                        </view>
                        <view class="ms-item" @click="goPage('/pages/me/favorite')" >
                            <image src="../../images/coloricon/aixin.png"></image>
                            <text>收藏夹</text>
                        </view>
                        <view class="ms-item" @click="goPage('/pages/me/qa')" >
                            <image src="../../images/coloricon/wenda.png"></image>
                            <text>我的动态</text>
                            <view v-if="answerTip" class="nomal-badge"></view>
                        </view>
                    </view>

                    <view class="me-link-part">
                        <navigator class="mlp-item" url="/pages/me/course-package">
                            <view class="mlpi-l">
                                <image src="../../images/icon/archive-2-fill.png" mode=""></image>
                                <text>已购课程包</text>
                            </view>
                            <view class="mlpi-r">
                                <image src="../../images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </navigator>
                        <navigator class="mlp-item" url="/pages/me/course">
                            <view class="mlpi-l">
                                <image src="../../images/coloricon/kecheng.png" mode=""></image>
                                <text>已购课程</text>
                            </view>
                            <view class="mlpi-r">
                                <image src="../../images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </navigator>
                        <navigator class="mlp-item" url="/pages/me/download">
                            <view class="mlpi-l">
                                <image src="../../images/coloricon/download_icon.png" mode=""></image>
                                <text>已购资料</text>
                            </view>
                            <view class="mlpi-r">
                                <image src="../../images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </navigator>
                    </view>
                </template>
        </view>

        <view class="my-integral-box" v-if="user && loaded">
            <view class="mib-warp">
                <view class="mib-top" v-if="user?.balance_show">
                    <view class="mibt-tit">钱包余额</view>
                    <view class="mibt-cnt">
                        <view class="mibt-num"><text>￥</text>{{ balanceArr[0] }}<text>.{{ balanceArr[1] }}</text></view>
                        <view class="mibt-r">
                            <navigator class="mibtr-detail" url="/pages/me/balance-detail">查看明细</navigator>
                        </view>
                    </view>
                </view>
                <view class="mib-top">
                    <view class="mibt-tit">我的积分</view>
                    <view class="mibt-cnt">
                        <view class="mibt-num">{{ user?.credit }}</view>
                        <view class="mibt-r">
                            <view class="mibtr-detail" @click="goPage('/pages/me/integral-detail')">积分明细</view>
                            <navigator class="mibtr-btn" url="/pages/me/recharge">充值积分</navigator>
                        </view>
                    </view>
                </view>
            </view>

            <view class="mib-task">
                <view class="mibt-tit">积分任务</view>
                <view class="mibt-list">
                    <view class="mibt-l-item" @click="goPage(item.url, item.isTabBar)" v-for="(item, index) in creditTask" :key="index">
                        <view class="mibtlt-l">
                            <view class="mibtltl-tit">{{ item.intro[0] }}</view>
                            <view class="mibtltl-txt">{{ item.intro[1] }}</view>
                        </view>
                        <view class="mibtlt-r">{{ item.go }}</view>
                    </view>
                </view>
            </view>
        </view>

        <view class="me-section-box" v-if="loaded">
            <view class="ms-item" @click="help">
                <image src="../../images/coloricon/fankui.png"></image>
                <text>意见反馈</text>
            </view>
            <view class="ms-item" @click="goPage('/pages/me/service')">
                <image src="../../images/coloricon/lianxikefu.png"></image>
                <text>联系客服</text>
            </view>
            <view class="ms-item" @click="goPage('/pages/about/index')">
                <image src="../../images/coloricon/guanyuwom.png"></image>
                <text>关于我们</text>
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import { tabBarPageLoad } from "@/lib/tabbar.js";
import { useUserStore } from "@/store/user";
import {mapActions, mapState} from "pinia";

export default {
    data() {
        return{
            creditTask:[],
            bot:[],
            loading: true,
            answerTip: false,
            skeleton: [{
                type: 'flex',
                num: 1,
                children: [{
                    type: 'avatar',
                    num: 1,
                    style: 'marginRight: 10rpx;'
                }, {
                    type: 'line',
                    num: 3,
                    gap: '30rpx',
                    style: ['width: 200rpx;', null, 'width:400rpx;']
                }]
            }]
        }
    },

    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),

        balanceArr() {
            return this.user?.balance.toString().split('.')
        }
    },
    onLoad(query) {
        this.initData();
        uni.removeTabBarBadge({
            index: 3, // 人脉页面在底部菜单栏的索引
        });

        tabBarPageLoad();
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.initData()
        this.reload()
    },
    methods: {
        ...mapActions(useUserStore,['reload']),

        initData(){
            api.get("pages/me").then(res => {
                this.creditTask = res.credit_task
                this.answerTip = res.answer_tip
                uni.stopPullDownRefresh();

            })
        },
        goPage(url, isTabBar = false){
            if (url == "/pages/me/qa"){
                if (this.answerTip){
                    url = url + "?is_tip=1"
                }
                this.answerTip = false
            }
            if (!isTabBar){
                uni.navigateTo({
                    url: url
                })
            }else {
                uni.switchTab({
                    url: url
                })
            }

        },
        goLogin(){
            uni.navigateTo({
                url: '/pages/login/index'
            })
        },
        help() {
            //#ifdef  MP-WEIXIN
            uni.openEmbeddedMiniProgram({
                appId: "wx8abaf00ee8c3202e",
                extraData: {
                    id: "643612"
                },
                success(res) {
                    // 打开成功
                }
            });
        }
    },
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .me-together-box {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
    }
    .me-index-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 120rpx;
        border-bottom: 1px solid #F3F3FF;
    }
    .mit-l {
        display: flex;
        align-items: center;
        padding-left: 30rpx;
    }
    .mit-l image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
    }
    .mit-l text {
        padding-left: 20rpx;
        font-weight: bold;
    }
    .mit-setting {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 120rpx;
        height: 120rpx;
    }
    .mit-setting image {
        width: 36rpx;
        height: 36rpx;
    }

    .me-section {
        display: flex;
        align-items: center;
    }
    .me-section-box {
        display: flex;
        align-items: center;background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ms-item {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40rpx 0;
    }
    .ms-item image {
        width: 64rpx;
        height: 64rpx;
    }
    .ms-item text {
        font-size: 28rpx;
        padding-top: 14rpx;
    }

    .me-link-part {
        border-top: 1px solid #F3F3FF;
    }
    .mlp-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #F3F3FF;
    }
    .mlp-item:last-child {
        border-bottom: none;
    }
    .mlpi-l {
        display: flex;
        align-items: center;
        padding-left: 30rpx;
    }
    .mlpi-l image {
        width: 48rpx;
        height: 48rpx;
    }
    .mlpi-l text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }
    .mlpi-r {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        width: 100rpx;
    }
    .mlpi-r image {
        width: 32rpx;
        height: 32rpx;
    }

    .my-integral-box {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
    }
    .mib-warp {
        background-color: #ff731c;
        border-radius: 12rpx 12rpx 0 0;
    }
    .mib-top {
        color: #fff;
        padding: 30rpx;
        border-bottom: 1px solid rgba(255,255,255,.2);
    }
    .mibt-tit {
        font-size: 24rpx;
        color: #fff;
    }
    .mib-task .mibt-tit {
        color: #999;
    }
    .mibt-cnt {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 10rpx;
    }
    .mibt-num {
        font-size: 68rpx;
        font-weight: bold;
        height: 100rpx;
        line-height: 100rpx;
    }
    .mibt-num text {
        font-size: 28rpx;
    }
    .mibt-r {
        display: flex;
        align-items: center;
        font-size: 24rpx;
    }
    .mibtr-detail {
        color: #FFF;
        padding: 0 20rpx;
        margin-right: 20rpx;
        height: 80rpx;
        line-height: 80rpx;
    }
    .mibtr-btn {
        background-color: #fbf4d3;
        color: #333;
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 40rpx;
        border-radius: 60rpx;
        font-size: 28rpx;
        font-weight: bold;
    }

    .mib-task {
        padding: 30rpx;
    }
    .mibt-l-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx 0 0;
    }
    .mibtltl-tit {
        font-weight: bold;
        font-size: 28rpx;
    }
    .mibtltl-txt {
        font-size: 24rpx;
        color: #999;
        padding-top: 8rpx;
    }
    .mibtlt-r {
        background-color: #fef7ed;
        border: 1px solid #ff731c;
        color: #ff731c;
        height: 60rpx;
        line-height: 60rpx;
        padding: 0 30rpx;
        border-radius: 60rpx;
        font-size: 24rpx;
    }


    .no-login {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-bottom: 1px solid #F3F3FF;
    }
    .nll-p {
        font-size: 28rpx;
        color: #999;
        padding-top: 10rpx;
    }
    .nl-right {
        background-color: #fef7ed;
        border: 1px solid rgb(254, 145, 0);
        color: #fe9100;
        height: 70rpx;
        line-height: 70rpx;
        padding: 0 40rpx;
        border-radius: 60rpx;
        font-size: 24rpx;
    }


    .nomal-badge {
        margin-top: -50rpx;
        margin-left: 50rpx;
    }
</style>