<template>
    <view class="content">
        <view class="top-fixed-box">
            <view class="tab-nomal">
                <view class="tab-item p-rel" :class="{cur: type == 1}" @click="type = 1">我发表的<text v-if="isTip" class="nomal-badge"></text></view>
                <view class="tab-item p-rel" :class="{cur: type == 2}" @click="type = 2">我的评论</view>
            </view>
        </view>

        <view class="my-question-list" v-if="type == 1">
            <view class="mql-item" @click="goDetailQuestion(item)" v-for="(item, index) in questionList" :key="index">
                <view class="mqli-tit">
                    {{item.content}}
                </view>
                <view class="mqli-foot">
                    <view class="mqlif-l">
                        <view class="mqlifl-time"><uni-dateformat :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></view>
                        <view class="mqlifl-data">
                            <text v-if="item.isAnswer" ></text>{{ item.answer_count }}评论
                        </view>
                    </view>
                    <view class="mqlif-r" @click.stop="delQuestion(item.sid, index)">
                        <view class="mqlifr-btn">
                            <image src="../../images/icon/delete-bin-line.png"></image>
                            <text>删除</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>


        <!-- 我的回答 -->
        <view class="my-answer-list" v-if="type == 2">
            <view class="mal-item" @click="goDetail(item.question.sid)" v-for="(item, index) in answerList" :key="index">
                <view class="mali-tit">{{item.question.content}}</view>
                <view class="mali-cnt">
                    {{item.content}}
                </view>
                <view class="mqli-foot">
                    <view class="mqlif-l">
                        <view class="mqlifl-time"><uni-dateformat :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></view>
                        <view class="mqlifl-data">{{ item.like_count }}点赞</view>
                    </view>
                    <view class="mqlif-r" @click.stop="delAnswers(item.sid, index)">
                        <view class="mqlifr-btn">
                            <image src="../../images/icon/delete-bin-line.png"></image>
                            <text>删除</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="no-data-nomal-box" v-if="type == 2 && !answerList.length ">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无回答</text>
        </view>
        <view class="no-data-nomal-box" v-if="type == 1 && !questionList.length && !loading">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无问题</text>
        </view>

    </view>
</template>

<script>
import api from "../../lib/api";
import {showDelayLoading, alert} from "../../lib/utils";

export default {
    data() {
        return {
            type: 1,
            questionList: [],
            answerList: [],
            questionCursor: '',
            answerCursor: '',
            isTip: false,
            loading: true
        }
    },
    onLoad(query) {
        if (query.is_tip){
            this.isTip = query.is_tip
        }
        this.initQuestionList()
        this.initAnswerList()
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.questionCursor && this.type == 1){
            this.initQuestionList();
        }
        if (this.answerCursor && this.type == 2){
            this.initAnswerList();
        }
    },
    methods: {

        initQuestionList(){
            let data ={
                cursor: this.questionCursor
            }
            const hideLoading = showDelayLoading("请稍后", 200)

            this.loading = true
            api.get('qa/my-questions', data).then(res => {
                this.questionList.push(...res.data)
                this.questionCursor = res.next_cursor
                hideLoading()
            }).catch(err => {
                hideLoading()
                alert(err.message)
            }).finally(() => this.loading = false)
        },
        initAnswerList(){
            let data ={
                cursor: this.answerCursor
            }
            api.get('qa/my-answers', data).then(res => {
                this.answerList.push(...res.data)
                this.answerCursor = res.next_cursor
            }).catch(err =>  {
                alert(err.message)
            })
        },
        goDetail(sid){

            uni.navigateTo({
                url:'/pages/ask/n-detail?sid=' + sid
            })
        },
        goDetailQuestion(data){
            data.isAnswer = false
            let isAnswer = false
            this.questionList.forEach(res => {
                if (res.isAnswer){
                    isAnswer = true
                }
            })
            if (isAnswer){
                this.isTip = true
            }else {
                this.isTip = false
            }

            uni.navigateTo({
                url:'/pages/ask/n-detail?sid=' + data.sid
            })
        },
        delQuestion(sid, index){
            uni.showModal({
                title: '提示',
                content: '确定删除该问题？',
                success: (res) => {
                    if (res.confirm) {
                        api.delete('qa/questions/'+ sid).then(res => {
                            this.questionList.splice(index, 1)
                            uni.showToast({
                                title: '删除成功',
                                icon: 'success',
                                duration: 2000
                            })
                        }).catch(err => alert(err.message))
                    }
                }
            })

        },
        delAnswers(sid, index) {
            uni.showModal({
                title: '提示',
                content: '确定删除该回答？',
                success: (res) => {
                    if (res.confirm) {
                        api.delete('qa/answers/' + sid).then(res => {
                            this.answerList.splice(index, 1)
                            uni.showToast({
                                title: '删除成功',
                                icon: 'success',
                                duration: 2000
                            })
                        }).catch(err => alert(err.message))
                    }
                }
            })
        }
    },
}
</script>

<style>
page {
    background-color: #f3f3ff;
}

.top-fixed-box {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f3f3ff;
    height: 100rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.tfb-eb {
    height: 100rpx;
}

/* tab样式 */
.tab-nomal {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 30rpx;
}

.tab-item {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 30rpx;
    color: #999;
}

.tab-item.cur {
    position: relative;
    font-weight: 700;
    color: #090abc;
}
.tab-item.cur::after {
    content: '';
    position: absolute;
    height: 8rpx;
    width: 20rpx;
    background-color: #090abc;
    left: 50%;
    transform: translateX(-50%);
    bottom: 0;
}

.my-question-list,
.my-answer-list {
    padding: 30rpx;
}
.mql-item {
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin-bottom: 30rpx;
    padding: 30rpx 0 0;
}

.mqli-tit {
    padding: 0 30rpx;
    padding: 0 30rpx;
    font-size: 32rpx;
    max-height: 80rpx;
    line-height: 40rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    margin-bottom: 20rpx;
}

.mqli-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    font-size: 24rpx;
    border-top: 1px solid #f3f3ff;
}

.mqlif-l {
    display: flex;
    align-items: center;
    color: #999;
}

.mqlif-r {
    padding: 10rpx 0;
}

.mqlifr-btn {
    display: flex;
    align-items: center;
    height: 70rpx;
    line-height: 70rpx;
    color: #000;
}

.mqlifr-btn image {
    width: 32rpx;
    height: 32rpx;
}

.mqlifr-btn text {
    padding-left: 10rpx;
}

.mqlifl-data {
    display: flex;
    align-items: center;
    padding: 0 30rpx;
}

.mqlifl-data text {
    width: 20rpx;
    height: 20rpx;
    border-radius: 100%;
    background-color: red;
    margin-right: 6rpx;
}


.mal-item {
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin-bottom: 30rpx;
    padding: 30rpx 0 0;
}

.mali-tit {
    padding: 0 30rpx;
    font-size: 28rpx;
    max-height: 80rpx;
    line-height: 40rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    margin-bottom: 20rpx;
    color: #999;
}

.mali-cnt {
    padding: 0 30rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    margin-bottom: 30rpx;
}
</style>