<template>
    <view class="content">
        <view class="setting-item">
            <view class="si-l">手机号码</view>
            <view class="si-r">{{ user.phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3") }}</view>
        </view>

        <view class="setting-item-group">
            <view class="setting-item">
                <view class="si-l">头像</view>
                <button class="uib-item" hover-class="hover-class" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
                    <image :src="user?.avatar" class="head-pic" mode="aspectFill"></image>
                    <image src="../../images/icon/arrow-right-s-line.png" class="icon-pic"></image>
                </button>
            </view>
            <navigator :url="'/pages/me/change-name?nickname='+ this.user?.nickname" class="setting-item">
                <view class="si-l">昵称</view>
                <view class="si-r">
                    <text>{{ user?.nickname }}</text>
                    <image src="../../images/icon/arrow-right-s-line.png" class="icon-pic"></image>
                </view>
            </navigator>
        </view>

        <view class="setting-item" v-for="(item, index) in binds" :key="index" >
            <view class="si-l">{{ item.labels }}</view>
            <view class="si-r">
                绑定于  <uni-dateformat :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat>
            </view>
        </view>

        <view class="setting-quit" @click="goLogout">退出登录</view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert} from "@/lib/utils";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store/user";

export default {
    data(){
        return{
            uploadForm:{},
            binds:[]
        }
    },
    computed: {
        ...mapState(useUserStore, ['user']),
    },
    onLoad(query) {
        this.initData()
    },
    methods: {
        ...mapActions(useUserStore,['reload', 'logout']),

        initData(){
            api.get('pages/user/profile').then(res => {
                this.uploadForm = res
            }).catch(err => alert(err.message))
            api.get('binds').then(res => {
                this.binds = res
            }).catch(err => alert(err.message))
        },
        onChooseAvatar(e) {
            let {
                avatarUrl
            } = e.detail;
            this.uploadImg(avatarUrl);
        },
        uploadImg(avatarUrl) {
            uni.showLoading({
                title: '保存中'
            });
            uni.uploadFile({
                url: this.uploadForm.url,
                filePath: avatarUrl,
                formData: this.uploadForm.form_params,
                name: this.uploadForm.name,
                success: uploadFileRes => {
                    // 注意：这里返回的uploadFileRes.data 为JSON 需要自己去转换
                    let data = JSON.parse(uploadFileRes.data)
                    if (data?.key){
                        this.updateAvatar(data.key)
                    }else {
                        alert('上传失败')
                    }

                },
                fail: (error) => {
                    console.log(error)
                    uni.showToast({
                        title: error,
                        duration: 2000
                    });
                },
                complete: () => {
                    uni.hideLoading();
                }
            });
        },
        updateAvatar(key){
            let data = {
                key: key
            }
            api.put('users/avatar', data).then(res => {
                this.reload()
                uni.showToast({
                    title: '保存成功',
                    icon: 'success',
                    duration: 2000
                });
            }).catch(err => {
                alert(err.message)
            })
        },
        goLogout(){
            uni.showModal({
                title: '温馨提示',
                content: '确认当前用户退出登录吗？',
                confirmText: '确认',
                cancelText: '取消',
                success: (c) => {
                    if (c.confirm) {
                        this.logout()
                        uni.switchTab({
                            url:'/pages/index/index'
                        })
                    }
                }
            });

        },
    },
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .setting-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        height: 100rpx;
        border-radius: 12rpx;
        padding: 0 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .si-l {
        font-weight: bold;
    }
    .si-r {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        color: #999;
    }

    .uib-item {
        display: flex;
        align-items: center;
    }


    .head-pic {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
        vertical-align: top;
    }
    .icon-pic {
        width: 32rpx;
        height: 32rpx;
        margin-left: 20rpx;
    }

    .setting-item-group {
        border-radius: 12rpx;
        background-color: #fff;
        overflow: hidden;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .setting-item-group .setting-item {
        margin-bottom: 0;
        border-radius: 0;
        border-bottom: 1px solid #f3f3f3;
        box-shadow: none;
    }

    .setting-quit {
        position: fixed;
        left: 30rpx;
        right: 30rpx;
        bottom: 30rpx;
        background-color: rgb(255,51,51);
        color: #fff;
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        border-radius: 12rpx;
        margin-bottom: env(safe-area-inset-bottom);
        font-weight: bold;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
</style>