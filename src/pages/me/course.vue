<template>
    <view class="content">
        <view class="video-list-box">
            <view class="vlb-pane">
                <view @click="clickCourseData(item)" class="video-item" :class="{timeout: item.is_expiration}" v-for="(item, index) in list" :key="index">
                    <image :src="item.cover_src"></image>
                    <view class="vi-info">
                        <view class="vii-tit">{{item.title}}</view>

                        <view class="vii-foot">
                            <view class="vii-teacher">讲师：{{ item.resource.teacher_name }}</view>
                            <view class="viif-l">{{ item.resource.learning_count }}人在学</view>

                        </view>
                        <view class="vii-foot">
                            <view class="viif-l">有效期至：{{ item.expiration }}</view>
                        </view>
                    </view>
                    <view class="timeout-tips"  v-if="item.is_expiration">已过期</view>
                </view>
            </view>
        </view>

        <view class="no-data-nomal-box" v-if="!loading && !list.length">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无数据</text>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {showDelayLoading, alert, getDetailUrl} from "@/lib/utils";

export default {
    data() {
        return {
            list: [],
            loading: true
        }
    },
    onLoad(query) {
        this.initData()
    },
    methods: {
        initData(){
            let loading = showDelayLoading("加载中", 200)
            api.get("cms/own-contents/course").then(res => {
                this.list = res
                loading()
            }).catch(err => {
                loading()
                alert(err.message)
            }).finally(() =>{
                this.loading = false
            })
        },
        clickCourseData(data){
            let url = getDetailUrl("course", data.sid)
            uni.navigateTo({
                url
            })
        }
    },
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .vlb-tab {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        display: flex;
        align-items: center;
        background-color: #F3F3FF;
        padding: 0 30rpx;
    }
    .vlbt-eb {
        height: 100rpx;
    }

    .vlbt-item {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 40rpx;
        color: #999;
        font-size: 32rpx;
    }
    .vlbt-item.cur {
        position: relative;
        font-weight: bold;
        color: #333;
    }
    .vlbt-item.cur::after {
        content: '';
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 20rpx;
        width: 20rpx;
        height: 8rpx;
        border-radius: 8rpx;
        background-color: #390abc;
    }
    .video-item {
        display: flex;
        align-items: center;
        background: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .video-item image {
        width: 200rpx;
        height: 200rpx;
        border-radius: 12rpx;
    }
    .vi-info {
        flex: 1;
        padding-left: 20rpx;
    }
    .vii-tit {
        height: 80rpx;
        line-height: 40rpx;
        font-weight: bold;
        font-size: 28rpx;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    .vii-teacher {
        padding: 10rpx 0;
        font-size: 28rpx;
        color: #999;
    }
    .vii-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24rpx;
        padding-top: 8rpx;
    }
    .viif-l {
        color: #999;
    }
    .viif-r {
        color: red;
        font-weight: bold;
    }


    /* 已过期样式 */
    .video-item.timeout {
        position: relative;
    }
    .timeout-tips {
        display: none;
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        background-color: #999999;
        color: #FFFFFF;
        padding: 10rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
    }
    .video-item.timeout .timeout-tips {
        display: inline-block;
    }
    .video-item.timeout .vii-tit {
        color: #999;
    }
</style>