/**
 * 应用全局上下文模块
 * 贯穿于应用完整生命周期的相关基础功能
 */

const state = {
    afterLoginSuccess: null,
    afterLoginCancel: null,
    //当前全局监听事件数据对象
    events: {},
    //邀请来源用户 uuid
    referral: ""
};

/**
 * 获取应用的名称
 * @return {String}
 */
export function getAppName() {
    return uni.getAppBaseInfo().appName;
}

export default {
    /**
     * 注册事件
     *
     * @param {String} event 事件名称
     * @param {Function} callback 事件函数
     * @param {String} [uniqid] 注册事件唯一ID，当指定了唯一ID后，对开相同的ID注册只会替换之前已有的相同ID事件，而不会重复注册
     * 采用 uniqid 机制可以避免某些情况下卸载事件不触发导致的多次注册和触发事件的问题，但标准的用法不应该使用 uniqid。
     * @param {Boolean} [once] 是否只被触发一次，如果为true，则触发后即被移除
     */
    on(event, callback, uniqid, once) {
        const e = state.events;

        if (!e[event]) {
            e[event] = [];
        }

        if (uniqid) {
            for (let i = 0, obj; (obj = e[event][i]); i++) {
                if (obj.uniqid == uniqid) {
                    Object.assign(e[event][i], {
                        callback,
                        once: !!once
                    });
                    return;
                }
            }
        }

        e[event].push({
            callback,
            uniqid,
            once: !!once
        });
    },

    /**
     * 卸载事件
     *
     * @param {String} event 事件名称
     * @param {Function|String} [callback] 指定要卸载的事件或唯一ID
     * 指定要卸载事件时，只有完全相同的事件（===判断）才会被卸载;
     * 如果指定的是字符串（uniqid），那么相同的 uniqid 才会被卸载;
     * 如果不指定任何值，那么所有 event 事件都会被卸载，使用此选项要注意;
     */
    off(event, callback) {
        const e = state.events;

        if (e[event]) {
            if (callback == undefined) {
                delete e[event];
            } else {
                for (let i = 0, obj; (obj = e[event][i]); i++) {
                    if (obj.callback === callback) {
                        e[event].splice(i--, 1);
                    } else if (typeof callback == "string" && obj.uniqid == callback) {
                        e[event].splice(i--, 1);
                        break;
                    }
                }

                if (e[event].length == 0) {
                    delete e[event];
                }
            }
        }
    },

    /**
     * 触发事件
     *
     * @param {String} event 触发事件的名称
     * @param {Object|String|Array|Number|Integer} [res] 触发事件携带的参数
     */
    trigger(event, res) {
        const e = state.events;

        if (e[event]) {
            for (let i = 0, obj; (obj = e[event][i]); i++) {
                if (obj.once) {
                    e[event].splice(i--, 1);
                }

                obj.callback(res);
            }

            if (e[event].length == 0) {
                delete e[event];
            }
        }

        console.log("event triggered: " + event, res);
    }
};

/**
 * 获取本次启动的 referral（无论冷启还是热启）
 * @return {String|undefined}
 */
function getEnterReferral() {
    let o = uni.getLaunchOptionsSync();

    if (!o.query.ref) {
        o = uni.getEnterOptionsSync();
    }

    return o.query.ref || "";
}

/**
 * 初始化邀请来源
 * 邀请来源可能会来自任何页面，并且用户离开后下次再进来源也生效
 */
export function initReferral() {
    const key = 'referral';

    let ref = uni.getStorageSync(key) || "";

    if (!ref) {
        const r = getEnterReferral()
        if (r) {
            ref = r;
            uni.setStorage({
                key,
                data: ref
            });
        }
    }

    state.referral = ref;
}

/**
 * 获取邀请来源（邀请者的 uuid）
 *
 * @param {Boolean} enter 是否仅获取本次进入的 ref 参数（默认否），默认情况下获取的是初次记录下来的 ref
 * @return {String} 空字符串代表没有来源
 */
export function getReferral(enter=false) {
    if (enter) {
        return getEnterReferral();
    } else {
        return state.referral;
    }
}
