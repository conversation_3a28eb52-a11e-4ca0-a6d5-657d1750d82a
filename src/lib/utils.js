import { tabBar } from '@/pages.json';
import qs from 'qs';
import {useUserStore} from "@/store/user";

const debounceTimer = {};

/**
 * 获取url参数
 * @param {String} url
 * @returns
 */
export function getUrlParams(url) {
    return new URL(url).searchParams;
}

/**
 * 去抖动功能
 *
 * 当不断调用时，key 相同的事件如果未到执行时间则会被清除重设
 * 一般用于键盘输入事件，为了防止频繁触发执行使用延迟事件处理
 *
 * @param {String} key
 * @param {Function} callable
 * @param {Number} ms 延迟毫秒数，默认 300
 * @returns {Number}
 */
export function debounce(key, callable, ms) {
    if (debounceTimer[key]) {
        clearTimeout(debounceTimer[key]);
    }
    if (ms === undefined) {
        ms = 300;
    }
    debounceTimer[key] = setTimeout(() => {
        delete debounceTimer[key];
        callable();
    }, ms);
}

/**
 * 单纯的提示框
 * @param {String} message
 */
export function alert(message) {
    uni.showModal({
        title: "温馨提示",
        content: message,
        showCancel: false,
        success: res => {
            if (res.confirm && (message === '您的账号已在其他设备登录' || message === '您尚未登录')) {
                const userStore = useUserStore()
                userStore.logout()
                uni.switchTab({
                    url:'/pages/index/index'
                })
            }
        }
    });
}

export function alertOrg(message) {
    uni.showModal({
        title: "温馨提示",
        content: message,
        showCancel: false,
        success: res => {
            if (res.confirm && (message === '您的账号已在其他设备登录' || message === '您尚未登录')) {
                const userStore = useUserStore()

                userStore.logout()
                uni.reLaunch({
                    url: '/pages/organization/login/index'
                })
            }
        }
    });
}


export function showToast(res) {
    uni.hideLoading();
    if (res.code != 400) {
        alert(res.message);
    } else {
        uni.showToast({
            title: res.message,
            icon: "none",
            mask: true,
            success:res =>{

            }
        });
    }
}

/**
 * 延迟展示一个加载框
 * 在网络情况较好时，如在延迟时间调用返回的闭包，则不会展示加载框，若网络情况较慢加载超出了延迟时间，则在取消前会显示加载框.
 * 尤其适用于根据 vuex 中 user 模块中的 getUser() 的结果来确定接下来行为的场景。
 * @param {String} title 标题
 * @param {Number} delay 延迟显示时间
 * @return {Function} 返回一个用于取消的闭包
 */
export function showDelayLoading(title, delay=200) {
    //1: showing, 2: canceled
    let state = 0;

    const t = setTimeout(() => {
        state |= 1;
        uni.showLoading({
            title,
            mask: true,
            success: () => {
                if ((state & 2) > 0) {
                    uni.hideLoading();
                }
            }
        });
    }, delay);

    return () => {
        state |= 2;
        if ((state & 1) > 0) {
            uni.hideLoading();
        } else {
            clearTimeout(t);
        }
    };
}

/**
 * 使用 WebView 打开指定页面
 *
 * 注意要打开的页面必须在业务域名的白名单内
 *
 * @param {String} url 网址
 * @param {String} [title] 标题，默认可以不给
 */
export function openWebView(url, title) {
    uni.navigateTo({
        url: getWebViewUrl(url, title)
    });
}

/**
 * 获取到指定网页的 WebView 地址
 *
 * 注意要打开的页面必须在业务域名的白名单内
 *
 * @param {String} url 网址
 * @param {String} [title] 标题，默认可以不给
 */

export function getWebViewUrl(url, title) {
    url = "/pages/index/webview?url=" + encodeURIComponent(url);

    if (title) {
        url += "&title=" + encodeURIComponent(title);
    }

    return url;
}

/**
 * 复制内容
 * @param {Object} _this
 * @param {String} text
 * @param {Function} callback
 */
export function setClipboard(_this, text, callback) {
    const cb = () => {
        if (typeof callback !== "function") {
            uni.showToast({
                title: "复制成功",
                icon: "none"
            });
        } else {
            callback();
        }
    };
    //#ifdef H5
    _this.$copyText(text).then(() => {
        cb();
    });
    // #endif
    //#ifdef MP-WEIXIN || APP-PLUS
    uni.setClipboardData({
        data: text,
        success: () => {
            cb();
        }
    });
    // #endif
}

/**
 * 获取资料类型图片
 *
 * @param {String} format
 * @returns {string}
 */
export function getImageByFormat(format) {
    switch (format) {
        case "word":
            return "/images/icon/file-word-fill.png"
        case "excel":
            return "/images/icon/file-excel-fill.png"
        case "ppt":
            return "/images/icon/file-ppt-fill.png"
        case "pdf":
            return "/images/icon/file-pdf-2-fill.png"
        default:
            return "/images/icon/file-word-fill.png"
    }
}
/**
 * 获取资料类型名称
 *
 * @param {String} format
 * @returns {string}
 */
export function getFormatType(filename) {
    let type = ''
    if (filename.includes('pdf')) {
        type =  'pdf'
    } else if (filename.includes('xlsx') || filename.includes('xls')) {
        type = 'excel'
    }else if (filename.includes('doc') || filename.includes('docx')) {
        type = 'word'
    }else if (filename.includes('ppt') ||　filename.includes('pptx')) {
        type = 'ppt'
    }else if (filename.includes('mp4')) {
        type = 'video'
    }else if (filename.includes('zip')) {
        type = 'zip'
    } else {
        type = 'image'
    }
    return type
}
/**
 * 获取内容详情地址
 *
 * @param {String} type
 * @param {String} sid
 * @returns {string}
 */
export function getDetailUrl(type, sid) {
    switch (type) {
        case 'doc': return "/pages/document/detail?sid=" + sid;
        case 'course': return "/pages/training/video/detail?sid=" + sid;
        case 'rich_text': return "/pages/article/detail?sid=" + sid;
        case 'video': return "/pages/article/video?sid=" + sid;
        default: return "";
    }
}

/**
 * 获取文件大小
 *
 * @param {number} bytes
 * @returns {string}
 */
export function getFileSize(bytes) {
    const fileSizeUnits = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const base = 1024;
    if (bytes) {
        let i = 0;
        if (bytes >= base) {
            let a = bytes;
            while (1) {
                a /= base;
                i++;
                if (a < base) break;
            }
        }
        return (bytes / Math.pow(base, i)).toFixed(2) + ' ' + fileSizeUnits[i];
    } else {
        return '0 KB';
    }
}

/**
 * 打开任意 URL
 *
 * 自动根据当前平台和 URL 地址决定跳转、WebView 打开、内部导航或是 switchTab。
 * 适用于后台展位等自定义 URL 等处调用。
 *
 * URL 支持以 $ 开头的参数触发调用时分发事件，如 ?$notify=nick:123,name:456 代表会使用 uni.$emit() 分发一个名为 notify 的事件，事件数据为 {nick:123,name:456}
 *
 * @param {String} url
 */
export function openUrlAnywhere(url) {
    if (!url) {
        return;
    }
    if (/https?:/.test(url)) {
        //#ifdef H5
            window.location.href = url;
        //#endif
        //#ifdef  MP-WEIXIN || APP-PLUS
            openWebView(url);
        //#endif
    } else {
        //如果是 Tab 页需使用 switchTab 跳转
        for (let i=0,tab; tab=tabBar.list[i]; i++) {
            if (url.startsWith("/" + tab.pagePath)) {
                let query = null;

                //解析 QueryString 以供后用
                if (url.includes("?")) {
                    const parse = url.split("?");
                    url = parse[0];
                    query = qs.parse(parse[1]);
                }

                uni.switchTab({
                    url,
                    success() {
                        //query 中带有 $ 开头的属性代表要分发该事件，为了确保目标页面能够加载完成后接收事件，此处延迟了 200 毫秒
                        //要分发的参数格式如： name:value,name2:value2
                        if (query) {
                            setTimeout(() => {
                                for (let k in query) {
                                    if (query.hasOwnProperty(k) && k.startsWith("$")) {
                                        const params = {};
                                        query[k].split(",").forEach(r => {
                                            const [name, value] = r.split(":");
                                            params[name] = value;
                                        });
                                        uni.$emit(k.slice(1), params);
                                    }
                                }
                            }, 50);
                        }
                    }
                });
                return;
            }
        }
        uni.navigateTo({ url });
    }
}


/**
 * 移除url指定参数
 *
 * @param url
 * @param parameter
 * @returns {*}
 */
export function removeURLParameter(url, parameter) {
    let urlParts = url.split('?');
    if (urlParts.length >= 2) {
        let prefix = encodeURIComponent(parameter) + '=';
        let pars = urlParts[1].split(/[&;]/g);

        for (let i = pars.length; i-- > 0;) {
            if (pars[i].lastIndexOf(prefix, 0) !== -1) {
                pars.splice(i, 1);
            }
        }

        url = urlParts[0] + (pars.length > 0 ? '?' + pars.join('&') : '');
        return url;
    } else {
        return url;
    }
}

/**
 * 获取设备信息
 *
 * @returns {{system: string, platform: string}}
 */
export function getDeviceInfo() {
    let deviceInfo = uni.getDeviceInfo()
    let platform = (deviceInfo.deviceType == 'phone' || deviceInfo.deviceType == 'pad') ? 'mobile' : 'pc'
    let system = deviceInfo.model + ' - ' + deviceInfo.system

    return {platform: platform, system: system}
}
