/**
 * 启动模块
 * 启动模块包含了那些必须完成后整个程序才能开始正常工作的流程
 */
import api, { hasToken } from "./api.js";
import { useUserStore } from "@/store/user.js";

let booted = false;
const bootWaitQueue = [];

/**
 * 启动
 */
export async function boot() {
    if (booted) {
        return;
    }

    try {
        await Promise.all([
            bootVisitorToken(),
            bootUser()
        ]);

        booted = true;

        let callback;
        while (callback = bootWaitQueue.pop()) {
            callback();
        }
    } catch (e) {
        uni.showToast({
            title: "应用启动失败：" + e.message,
            icon: "none"
        });
        console.error(e);
    }
}

/**
 * 应用是否启动完成
 *
 * @return {Boolean}
 */
export function isBooted() {
    return booted;
}

/**
 * 注册应用启动完成回调
 *
 * 某些业务需要等 App 预定的启动事件完成后才能开始执行，比如接口的请求需要 visitor 初始化后（这个流程在启动中）才能开始使用，
 * 则使用 onBooted() 注册自己的回调函数，在回调函数执行后代表自己能够执行。
 *
 * @param {Function} callback 启动后的回调函数
 */
export function onBooted(callback) {
    if (booted) {
        callback();
    } else {
        bootWaitQueue.push(callback);
    }
}

let visitorToken = null;

/**
 * 初始化访客 Token
 * 访客 Token 必须在所有业务接口中传递，所以访客 Token 必须在业务接口开始请求前完成
 */
async function bootVisitorToken() {
    const key = 'visitor_token';
    visitorToken = uni.getStorageSync(key);

    if (visitorToken) {
        //已有 Token 的情况下不阻塞流程，这种情况下 Token 一般不会变，异步处理
        api.request("visitor", {waitBoot: false}).then(res => {
            if (visitorToken != res.data.token) {
                visitorToken = res.data.token;
                uni.setStorage({
                    key,
                    data: visitorToken
                });
            }
        }, e => {
            console.error(e);
        });
    } else {
        //没有的情况下需要阻塞，确保 Token 生成
        const res = await api.request("visitor", {waitBoot: false});
        visitorToken = res.data.token;

        uni.setStorage({
            key,
            data: visitorToken
        });
    }
}

/**
 * 初始化登录用户
 * 用户信息在大多数页面中都需要，如果到页面中才触发初始化，loading 的过程会影响页面的展示时机和逻辑，所以尽早初始化
 */
async function bootUser() {
    const userStore = useUserStore();
    if (hasToken()) {
        await userStore.reload();
    }
    userStore.loaded = true;
}

/**
 * 获取访问 Token
 * @return {String|null}
 */
export function getVisitorToken() {
    return visitorToken;
}
