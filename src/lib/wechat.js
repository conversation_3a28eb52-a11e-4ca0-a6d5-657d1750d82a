import api from "@/lib/api";
import {showDelayLoading, showToast} from "@/lib/utils";

export function checkOpenid() {
    const hideLoading = showDelayLoading("", 200)
    api.get("mp/account").then(res => {
        if (!res.mp_account || !res.mp_account.open_id) {
            uni.login({
                provider: "weixin",
                success: res => {
                    const code = res.code;
                    const hideLoading = showDelayLoading("", 200)
                    api.post("login/wechat-mini-app/openid", {code}).then(res => {
                        const hideLoading = showDelayLoading("", 200)
                        api.post("mp/account", {open_id: res.openid}).then(() => {}).catch(err => {
                            alert(err.message)
                        }).finally(() => {
                            hideLoading()
                        })
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                }
            })
        }
    }).catch(err => {
        alert(err.message)
    }).finally(() => {
        hideLoading()
    })
}

export function setMpSubscribeNotice() {
    const hideLoading = showDelayLoading("", 200)
    api.get("mp/account").then(res => {
        if (!res.mp_account || !res.mp_account.open_id) {
            uni.login({
                provider: "weixin",
                success: res => {
                    const code = res.code;
                    const hideLoading = showDelayLoading("", 200)
                    api.post("login/wechat-mini-app/openid", {code}).then(res => {
                        const hideLoading = showDelayLoading("", 200)
                        api.post("mp/account", {open_id: res.openid}).then(res => {
                            subscribeMessage()
                        }).catch(err => {
                            subscribeMessageError(err.message)
                        }).finally(() => {
                            hideLoading()
                        })
                    }).catch(err => {
                        subscribeMessageError(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                }
            })
        } else {
            subscribeMessage()
        }
    }).catch(err => {
        subscribeMessageError(err.message)
    }).finally(() => {
        hideLoading()
    })
}
export function subscribeMessage() {
    // api.get('wechat/notice-templates').then(res => {
        let tempIds = ['FY4re6FqhaDqR4XAHmQqt9WXrsKN-rR14IKxRM35dTE']
        uni.requestSubscribeMessage({
            tmplIds: tempIds,
            success: (res) => {
                if (res.errMsg === 'requestSubscribeMessage:ok') {
                    const acceptedTemplates = tempIds.filter(
                        tmplId => res[tmplId] === 'accept'
                    )

                    if (acceptedTemplates.length > 0) {
                        uni.showToast({
                            title: '订阅成功',
                            icon: 'success'
                        })
                    }
                }
                uni.$emit("mp-subscribe-message")
            },
            fail: (err) => {
                subscribeMessageError(err.errMsg)
            }
        })
    // }).catch(err => {
    //     subscribeMessageError(err.message)
    // })
}

function alert(message) {
    uni.showModal({
        content: message,
        showCancel: false
    })
}

function subscribeMessageError(message) {
    uni.showModal({
        content: message,
        showCancel: false,
        success: res => {
            uni.$emit("mp-subscribe-message")
        }
    })
}
