<?php

namespace Database\Factories\Cms;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cms\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => Str::random(10),
            'intro' => Str::random(10),
            'logo' => '',
            'pid' => 0,
            'path' => '',
            'classify' => '',
            'visible' => 1,
            'allow_types' => Str::random(10),
            'sort' => 0
        ];
    }
}
