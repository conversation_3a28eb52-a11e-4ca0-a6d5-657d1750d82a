<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('experts', function (Blueprint $table) {
            $table->string('residence', 128)->default('')->comment('常住地')->after('gender');
            $table->string('major', 32)->default('')->comment('毕业所学专业')->after('residence');
            $table->text('safety_work_experience')->comment('安全从业经历')->after('major');
        });

        Schema::table('user_own_contents', function (Blueprint $table) {
            $table->dateTime('expired_at')->nullable()->comment('到期时间')->after('classify');
        });

        \Illuminate\Support\Facades\DB::table("user_own_contents")->whereIn('classify', ["course_pack", "course"])->update(['expired_at' => "2099-01-01 00:00:00"]);

        Schema::table("open_course_records", function (Blueprint $table) {
            $table->json('course_pack_ids')->comment('课程包ID')->after('topic_ids');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('experts', function (Blueprint $table) {
            $table->dropColumn(['residence', 'major', 'safety_work_experience']);
        });
        Schema::table('user_own_contents', function (Blueprint $table) {
            $table->dropColumn(['expired_at']);
        });

        Schema::table('open_course_records', function (Blueprint $table) {
            $table->dropColumn(['course_pack_ids']);
        });
    }
};
