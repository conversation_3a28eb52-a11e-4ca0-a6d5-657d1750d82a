<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qa_tags', function(Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->string('name', 60)->comment('标签名称')->unique('name');
            $table->boolean('system')->default(0)->comment('是否系统标签');
            $table->timestamp('recommend_at')->nullable()->comment('推荐时间')->index('recommend_at');
            $table->unsignedMediumInteger('ref_count')->default(0)->comment('引用计数');
            $table->timestamps();

            $table->comment('QA标签');
        });

        Schema::create('qa_tag_relations', function(Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->unsignedInteger('tag_id')->comment('标签ID')->index('tag_id');
            $table->unsignedInteger('question_id')->comment('问题ID')->index('question_id');
            $table->timestamp('created_at');
        });

        Schema::table('qa_questions', function(Blueprint $table) {
            $table->json('images')->nullable()->comment('图片')->after('content');
            $table->unsignedInteger('views')->default(0)->comment('浏览量')->after('answer_count');
            $table->unsignedInteger('views_add')->default(0)->after('answer_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropColumns('qa_questions', 'views_add');
        Schema::dropColumns('qa_questions', 'images');
        Schema::dropColumns('qa_questions', 'views');
        Schema::dropIfExists('qa_tag_relations');
        Schema::dropIfExists('qa_tags');
    }
};
