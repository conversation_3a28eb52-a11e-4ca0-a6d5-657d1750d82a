<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('stat_daily_overviews', function (Blueprint $table) {
            $table->unsignedInteger('platform_content')->default(0)->comment('平台内容总数量')->after('content_download');
            $table->unsignedInteger('platform_content_material')->default(0)->comment('平台资料总数量')->after('platform_content');
            $table->unsignedInteger('platform_content_course')->default(0)->comment('平台课程总数量')->after('platform_content_material');
            $table->unsignedInteger('platform_content_news')->default(0)->comment('平台资讯总数量')->after('platform_content_course');
        });

        Schema::table('stat_daily_promoters', function (Blueprint $table) {
            $table->unsignedInteger('platform_content')->default(0)->comment('平台内容总数量')->after('content_download');
            $table->unsignedInteger('platform_content_material')->default(0)->comment('平台资料总数量')->after('platform_content');
            $table->unsignedInteger('platform_content_course')->default(0)->comment('平台课程总数量')->after('platform_content_material');
            $table->unsignedInteger('platform_content_news')->default(0)->comment('平台资讯总数量')->after('platform_content_course');
        });

        Schema::table('cms_contents', function (Blueprint $table) {
            $table->index(['release_at'], 'idx_release_at');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->index(['payment_at'], 'idx_payment_at');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->index(['created_at'], 'idx_created_at');
            $table->index(['last_active_at'], 'idx_last_active_at');
        });

        Schema::table('user_favorites', function (Blueprint $table) {
            $table->index(['created_at'], 'idx_created_at');
        });

        Schema::table('train_tests', function (Blueprint $table) {
            $table->index(['created_at'], 'idx_created_at');
        });

        Schema::table('qa_questions', function (Blueprint $table) {
            $table->index(['created_at'], 'idx_created_at');
        });

        Schema::table('qa_answers', function (Blueprint $table) {
            $table->index(['created_at'], 'idx_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stat_daily_overviews', function (Blueprint $table) {
            $table->dropColumn(['platform_content', 'platform_content_material', 'platform_content_course', 'platform_content_news']);
        });

        Schema::table('stat_daily_promoters', function (Blueprint $table) {
            $table->dropColumn(['platform_content', 'platform_content_material', 'platform_content_course', 'platform_content_news']);
        });

        Schema::table('cms_contents', function (Blueprint $table) {
            $table->dropIndex('idx_release_at');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_payment_at');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_created_at');
            $table->dropIndex('idx_last_active_at');
        });

        Schema::table('user_favorites', function (Blueprint $table) {
            $table->dropIndex('idx_created_at');
        });

        Schema::table('train_tests', function (Blueprint $table) {
            $table->dropIndex('idx_created_at');
        });

        Schema::table('qa_questions', function (Blueprint $table) {
            $table->dropIndex('idx_created_at');
        });

        Schema::table('qa_answers', function (Blueprint $table) {
            $table->dropIndex('idx_created_at');
        });
    }
};
