<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sms_records', function (Blueprint $table) {
            $table->increments('id');
            $table->string('phone',  20)->comment('手机号');
            $table->string('type', 50)->comment('类型');
            $table->string('ip', 20)->default('')->comment('ip');
            $table->unsignedTinyInteger('platform')->default(1)->comment('平台');
            $table->string('content', 255)->default('')->comment('内容');
            $table->timestamps();

            $table->index('ip', 'idx_ip');
            $table->index('created_at', 'idx_created_at');
            $table->index(['phone', 'type'], 'idx_phone_type');

            $table->comment('短信记录表');
        });

        Schema::create('wechat_notice_records', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->string('open_id', 32)->default('')->comment('openID');
            $table->string('template_id', 50)->default('')->comment('模板ID');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态 1 成功，2 失败');
            $table->string("type", 20)->default('')->comment('消息通知类型');
            $table->text('content')->comment('发送内容');
            $table->string('error_message', 255)->comment('错误消息');
            $table->timestamp('created_at');

            $table->index('user_id', 'idx_user_id');
            $table->index('open_id', 'idx_open_id');
            $table->index('status', 'idx_status');
            $table->index('type', 'idx_type');
            $table->index('created_at', 'idx_created_at');

            $table->comment('微信模板消息发送记录表');
        });

        Schema::create('user_mp_accounts', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->string('platform', 20)->default('')->comment('平台');
            $table->string('open_id', 32)->default('')->comment('openID');
            $table->timestamps();

            $table->index('user_id', 'idx_user_id');
            $table->index('platform', 'idx_platform');
            $table->index('open_id', 'idx_open_id');

            $table->comment('用户平台信息');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sms_records');
        Schema::dropIfExists('wechat_notice_records');
        Schema::dropIfExists('user_mp_accounts');
    }
};
