<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms_contents', function(Blueprint $table) {
            $table->unsignedMediumInteger('views_add')->default(0)->after('views');
        });

        Schema::table('cms_content_courses', function(Blueprint $table) {
            $table->unsignedMediumInteger('learning_count_add')->default(0)->after('learning_count');
        });

        Schema::table('cms_content_docs', function(Blueprint $table) {
            $table->unsignedMediumInteger('download_count_add')->default(0)->after('download_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropColumns('cms_content_docs', 'download_count_add');
        Schema::dropColumns('cms_content_courses', 'learning_count_add');
        Schema::dropColumns('cms_contents', 'views_add');
    }
};
