<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orgs', function (Blueprint $table) {
            $table->string('merchant_config')->nullable()->after('enable_enroll')->comment('机构自己的微信商户数据');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orgs', function (Blueprint $table) {
            Schema::table('orgs', function (Blueprint $table) {
                $table->dropColumn('merchant_config');
            });
        });
    }
};
