<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stat_daily_overviews', function (Blueprint $table) {
            $table->comment('每日统计表');
            $table->increments('id');
            $table->date('date')->unique('uniq_date')->comment('日期');
            $table->unsignedInteger('user_register')->default(0)->comment('注册用户数');
            $table->unsignedInteger('user_active')->default(0)->comment('活跃用户数');
            $table->unsignedInteger('payment_order')->default(0)->comment('支付订单数量');
            $table->unsignedDecimal('payment_amount', 10)->default(0)->comment('支付金额');
            $table->unsignedInteger('payment_user')->default(0)->comment('支付用户数');
            $table->unsignedInteger('payment_credit_order')->default(0)->comment('支付积分订单数量');
            $table->unsignedDecimal('payment_credit_amount', 10)->default(0)->comment('支付积分金额');
            $table->unsignedInteger('payment_credit_user')->default(0)->comment('支付积分用户数');
            $table->unsignedInteger('payment_course_order')->default(0)->comment('支付课程订单数量');
            $table->unsignedDecimal('payment_course_amount', 10)->default(0)->comment('支付课程金额');
            $table->unsignedInteger('payment_course_user')->default(0)->comment('支付课程用户数');
            $table->unsignedInteger('payment_topic_order')->default(0)->comment('支付题库订单数量');
            $table->unsignedDecimal('payment_topic_amount', 10)->default(0)->comment('支付题库金额');
            $table->unsignedInteger('payment_topic_user')->default(0)->comment('支付题库用户数');
            $table->unsignedInteger('consume_credit')->default(0)->comment('消费积分');
            $table->unsignedInteger('content')->default(0)->comment('内容总数量');
            $table->unsignedInteger('content_material')->default(0)->comment('资料数量');
            $table->unsignedInteger('content_course')->default(0)->comment('课程数量');
            $table->unsignedInteger('content_news')->default(0)->comment('资讯数量');
            $table->unsignedInteger('content_view')->default(0)->comment('浏览总数（资料、资讯、课程总浏览数）');
            $table->unsignedInteger('content_download')->default(0)->comment('资料下载数');
            $table->unsignedInteger('practise')->default(0)->comment('做题次数');
            $table->unsignedInteger('question')->default(0)->comment('提问数');
            $table->unsignedInteger('answer')->default(0)->comment('回答数');
            $table->unsignedInteger('search')->default(0)->comment('搜索次数');
            $table->unsignedInteger('collect')->default(0)->comment('收藏数');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stat_daily_overviews');
    }
};
