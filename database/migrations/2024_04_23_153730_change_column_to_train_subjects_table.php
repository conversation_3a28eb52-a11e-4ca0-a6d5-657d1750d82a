<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('train_subjects', function (Blueprint $table) {
            $table->text('intro')->nullable()->change();

            $table->dropColumn(['image']);
        });

        Schema::table('train_subject_options', function (Blueprint $table) {
            $table->text('name')->nullable()->change();

            $table->dropColumn(['image']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
