<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_menus', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->unsignedSmallInteger('parent_id')->default(0);
            $table->unsignedTinyInteger('type')->default(0)->comment('类型 0.菜单 1.事件');
            $table->string('name', 30)->comment('菜单名称');
            $table->string('icon')->default('')->comment('菜单图标');
            $table->string('method', 10)->comment('请求方式 GET POST PUT DELETE');
            $table->string('route_name', 100)->default('')->comment('路由名称');
            $table->string('route_path', 100)->default('')->comment('路由地址');
            $table->string('component', 100)->default('')->comment('页面组件');
            $table->unsignedSmallInteger('sort')->default(0);
            $table->unsignedTinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->timestamps();

            $table->comment('菜单表');
        });

        Schema::create('admin_roles', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->string('name', 20)->comment('角色名称');
            $table->string('code', 30)->comment('角色编码');
            $table->string('desc')->default('')->comment('角色描述');
            $table->string('permissions')->default('')->comment('权限');
            $table->unsignedTinyInteger('system')->default(0)->comment('系统');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态 0:禁用 1:启用');
            $table->timestamps();

            $table->unique(['code'], 'uniq_code');
            $table->comment('权限表');
        });

        Schema::create('admin_role_menus', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->unsignedSmallInteger('role_id');
            $table->unsignedSmallInteger('menu_id');
            $table->unsignedTinyInteger('checked')->default(0);
            $table->timestamp('created_at')->nullable();

            $table->comment('权限菜单表');
        });

        Schema::create('admin_user_roles', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->unsignedSmallInteger('admin_id');
            $table->unsignedSmallInteger('role_id');
            $table->timestamp('created_at')->nullable();

            $table->comment('用户权限表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_menus');
        Schema::dropIfExists('admin_roles');
        Schema::dropIfExists('admin_role_menus');
        Schema::dropIfExists('admin_user_roles');
    }
};
