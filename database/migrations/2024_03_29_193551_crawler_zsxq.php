<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('crawler_zsxq', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedBigInteger('topic_id');
            $table->unsignedBigInteger('file_id')->index('file_id');
            $table->string('name', 128)->comment('文件名');
            $table->string('create_time', 28)->comment('该内容的创建时间，也是翻页的基础')->index('create_time');
            $table->unsignedInteger('content_id')->comment('保存的内容 ID');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('crawler_zsxq');
    }
};
