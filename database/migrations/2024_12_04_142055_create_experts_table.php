<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('experts', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id');
            $table->string('name', 10)->comment('姓名');
            $table->unsignedTinyInteger('gender')->default(0)->comment('性别 1:男 2:女');
            $table->string('phone', 11)->comment('联系电话');
            $table->string('photo')->comment('个人照片');
            $table->string('education', 10)->comment('学历');
            $table->string('occupation', 30)->comment('职务');
            $table->string('industry', 30)->comment('从事行业');
            $table->unsignedTinyInteger('work_year')->comment('工作年限');
            $table->json('fields')->comment('擅长领域');
            $table->json('services')->comment('安全服务方向');
            $table->json('certs')->comment('资格证书');
            $table->json('scene_photos')->comment('现场照片');
            $table->text('course_scopes')->comment('授课范围');
            $table->text('typical_cases')->comment('典型案例');
            $table->text('serve_customers')->comment('服务客户');
            $table->text('teaching_styles')->comment('教学风格');
            $table->text('remark')->comment('备注信息');
            $table->string('reason')->default('')->comment('拒绝原因');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态');
            $table->timestamp('pass_at')->nullable()->comment('通过时间');
            $table->timestamps();

            $table->index(['user_id'], 'user_id');
            $table->index(['phone'], 'phone');
            $table->index(['status'], 'status');
            $table->index(['created_at'], 'created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('experts');
    }
};
