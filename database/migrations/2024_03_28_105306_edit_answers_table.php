<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('qa_answers',function (Blueprint $table){
            $table->unsignedTinyInteger('anonymous')->after('content')->default(0)->comment('是否匿名  0 否，1 是');
        });
        Schema::table('user_properties',function (Blueprint $table){
            $table->timestamp('nickname_edited_at')->after('current_topic_id')->nullable()->comment('昵称修改时间');
            $table->timestamp('avatar_edited_at')->after('current_topic_id')->nullable()->comment('头像修改时间');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropColumns('qa_answers', ['anonymous']);
        Schema::dropColumns('user_properties', ['nickname_edited_at', 'avatar_edited_at']);
    }
};
