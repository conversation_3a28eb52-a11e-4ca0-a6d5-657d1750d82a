<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 删除现有的全文索引
        DB::statement('ALTER TABLE experts DROP INDEX ft_all');

        // 重新创建全文索引，增加major字段
        DB::statement('
            ALTER TABLE experts
            ADD FULLTEXT INDEX ft_all (
                name,
                residence,
                major,
                education,
                occupation,
                industry,
                safety_work_experience,
                course_scopes,
                typical_cases,
                serve_customers,
                teaching_styles,
                extra_text
            ) WITH PARSER ngram
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 回滚时删除新的全文索引
        DB::statement('ALTER TABLE experts DROP INDEX ft_all');

        // 重新创建旧的全文索引（不包含 major）
        DB::statement('
            ALTER TABLE experts
            ADD FULLTEXT INDEX ft_all (
                name,
                residence,
                education,
                occupation,
                industry,
                safety_work_experience,
                course_scopes,
                typical_cases,
                serve_customers,
                teaching_styles,
                extra_text
            ) WITH PARSER ngram
        ');
    }
};
