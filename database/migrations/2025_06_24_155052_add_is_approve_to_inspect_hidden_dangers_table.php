<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inspect_hidden_dangers', function (Blueprint $table) {
            $table->unsignedTinyInteger('is_approve')->default(0)->after('is_public')->comment('是否需要审批，0-不需要，1-需要');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inspect_hidden_dangers', function (Blueprint $table) {
            $table->dropColumn('is_approve');
        });
    }
};
