<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('search_stats', function (Blueprint $table) {
            $table->increments('id');
            $table->date('date')->comment('日期');
            $table->string('keyword', 50)->default('')->comment('关键词');
            $table->unsignedInteger('search_count')->default(0)->comment('搜索次数');

            $table->unique(['date', 'keyword'], 'uniq_date_keyword');
            $table->index('search_count');

            $table->comment('搜索统计表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('search_stats');
    }
};
