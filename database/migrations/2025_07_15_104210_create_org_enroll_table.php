<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('org_enroll_config', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('org_id')->index('idx_org_id')->comment('机构ID');
            $table->string('title')->comment('报名课程名称');
            $table->decimal('amount')->comment('报名价格');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
            $table->comment('机构报名配置表');
        });

        Schema::create('org_enrolls', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('student_id')->comment('学员ID');
            $table->unsignedInteger('enroll_config_id')->comment('报名配置ID');
            $table->unsignedInteger('order_id')->nullable()->comment('订单ID');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 待审核，1 已驳回，2 待支付，3 已支付，4 退款中，5 退款关闭，6 退款成功');
            $table->timestamps();
            $table->comment('机构报名表');
        });

        Schema::create('org_enroll_operate_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('enroll_id')->comment('报名ID');
            $table->unsignedInteger('admin_id')->comment('操作人ID，用户提交为0');
            $table->unsignedTinyInteger('type')->comment('操作类型 1 信息提交，2 信息审核，3 缴费，4 申请退款，5 退款审核， 6 退款成功， 7 取消退款');
            $table->string('remark')->comment('审核备注');
            $table->timestamps();
            $table->comment('机构报名操作记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('org_enroll_config');
        Schema::dropIfExists('org_enrolls');
        Schema::dropIfExists('org_enroll_operate_records');
    }
};
