<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stat_daily_promoters', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedSmallInteger('admin_id');
            $table->date('date')->comment('日期');
            $table->unsignedInteger('consume_credit')->default(0)->comment('消费积分');
            $table->unsignedInteger('payment_order')->default(0)->comment('支付订单数量');
            $table->unsignedDecimal('payment_amount', 10)->default(0)->comment('支付金额');
            $table->unsignedInteger('payment_user')->default(0)->comment('支付用户数');
            $table->unsignedInteger('payment_course_order')->default(0)->comment('支付课程订单数量');
            $table->unsignedDecimal('payment_course_amount', 10)->default(0)->comment('支付课程金额');
            $table->unsignedInteger('payment_course_user')->default(0)->comment('支付课程用户数');
            $table->unsignedInteger('content')->default(0)->comment('内容总数量');
            $table->unsignedInteger('content_material')->default(0)->comment('资料数量');
            $table->unsignedInteger('content_course')->default(0)->comment('课程数量');
            $table->unsignedInteger('content_news')->default(0)->comment('资讯数量');
            $table->unsignedInteger('content_view')->default(0)->comment('浏览总数（资料、资讯、课程总浏览数）');
            $table->unsignedInteger('content_download')->default(0)->comment('资料下载数');
            $table->timestamps();

            $table->comment('推广员每日统计表');

            $table->unique(['admin_id', 'date'], 'uniq_admin_id_date');
        });

        Schema::table('user_credit_logs', function (Blueprint $table) {
            $table->index(['created_at'], 'idx_created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stat_daily_promoters');

        Schema::table('user_credit_logs', function (Blueprint $table) {
            $table->dropIndex('idx_created_at');
        });
    }
};
