<?php

use App\Models\Cms\ContentCourse;
use App\Models\Cms\ContentCourseChapter;
use App\Models\Cms\ContentCoursePackList;
use App\Models\Cms\ContentCourseSection;
use App\Models\Org\Course;
use App\Models\Org\CoursePack;
use App\Models\Org\CourseSub;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * org_courses 表增加 lessons, duration 字段代表该课程启⽤的有效学时及时⻓
     */
    public function up(): void
    {
        $tableName = 'org_courses';
        Schema::table('org_courses', function (Blueprint $table) use ($tableName) {
            if (!Schema::hasColumn($tableName, 'duration')) {
                $table->unsignedSmallInteger('duration')->default(0)->after('hour')->comment('课程视频时长（单位：秒）');
            }

            if (!Schema::hasColumn($tableName, 'lessons')) {
                $table->json('lessons')->nullable()->after('duration')->comment('课程章/节信息');
            }
        });


        Course::query()->select(['id', 'org_id', 'course_id', 'lessons'])->chunkById(500, function ($orgCourses) {
            $grouped = $orgCourses->groupBy('org_id');

            // 处理同一机构的课程
            foreach ($grouped as $orgId => $group) {
                $courseIds = $group->pluck('course_id')->toArray();

                // 平台课程信息们
                $contentCourses = ContentCourse::query()
                    ->whereIn('content_id', $courseIds)
                    ->get()
                    ->keyBy('content_id');

                // 机构课程的章/节信息, 得是显示的
                $courseResources = CourseSub::query()
                    ->where('org_id', $orgId)
                    ->whereIn('course_id', $courseIds)
                    ->with('resource', fn($query) => $query->where('status', 1))
                    ->get()
                    ->groupBy('course_id');

                foreach ($courseResources as $courseId => $resources) {
                    // 平台课程信息, 仅用来计算学时
                    $contentCourse = $contentCourses->get($courseId);

                    $items = $resources->pluck('resource')->sortBy([
                        ['sort', 'desc'],
                        ['id', 'asc']
                    ]);

                    // 机构课程所有的章/节
                    $sectionIds = $items->filter(fn($item) => $item instanceof ContentCourseSection)->pluck('id')->toArray();
                    $chapterIds = $items->filter(fn($item) => $item instanceof ContentCourseChapter)->pluck('id')->toArray();

                    // 平台课程所有的章/节
                    $chapters = ContentCourseChapter::query()
                        ->with([
                            'sections' => function ($query) {
                                $query->where('status', ContentCourseSection::STATUS_SHOW)->orderByRaw('sort desc,id asc');
                            },
                            'sections.video'
                        ])
                        ->where('content_id', $courseId)
                        ->where('status', ContentCourseChapter::STATUS_SHOW)
                        ->orderByRaw('sort desc,id asc')
                        ->get();

                    $formattedChapters = $chapters->map(function($chapter) use ($contentCourse, $sectionIds, $chapterIds) {
                        $chapterSections = $chapter->sections->map(function($section) use ($contentCourse, $sectionIds) {
                            return [
                                'id' => $section->id,
                                'enabled' => in_array($section->id, $sectionIds),
                                'hour' => in_array($section->id, $sectionIds) ? $contentCourse->studyHour($section->actual_duration) : 0,
                                'duration' => $section->actual_duration ?? 0
                            ];
                        })->values()->toArray();

                        // 启用section的duration总和
                        $totalSectionDuration = array_reduce($chapterSections, function($carry, $section) {
                            return $section['enabled'] ? $carry + intval($section['duration']) : $carry;
                        }, 0);

                        $totalHour = $contentCourse->studyHour($totalSectionDuration);

                        return [
                            'id' => $chapter->id,
                            'enabled' => in_array($chapter->id, $chapterIds),
                            'hour' => in_array($chapter->id, $chapterIds) ? (string)$totalHour : 0,
                            'duration' => (string)$totalSectionDuration,
                            'sections' => $chapterSections
                        ];
                    })->values()->toArray();

                    $totalDuration = array_reduce($formattedChapters, function($carry, $chapter) {
                        return $chapter['enabled'] ? $carry + intval($chapter['duration']) : $carry;
                    }, 0);

                    $course = $group->firstWhere('course_id', $courseId);
                    $course->duration = $totalDuration;
                    $course->hour = $contentCourse->studyHour($totalDuration, false);
                    $course->lessons = json_encode($formattedChapters);
                    $course->save();
                }
            }
        }, 'id');

        // 更新课程包的duration和hour
        $coursePacks = CoursePack::query()->select(['org_id', 'course_pack_id'])->get();
        foreach ($coursePacks as $coursePack) {
            $courseIds = ContentCoursePackList::query()
                ->where('content_id', $coursePack->course_pack_id)
                ->pluck('course_id')
                ->toArray();
            
            $contentCourses = ContentCourse::query()
                ->select(['hour_per_minutes', 'content_id'])
                ->whereIn('content_id', $courseIds)
                ->get()
                ->keyBy('content_id');

            $map = Course::query()
                ->where('org_id', $coursePack->org_id)
                ->whereIn('course_id', $courseIds)
                ->get()
                ->keyBy('course_id')
                ->map(function ($item) {
                    return $item->duration;
                })
                ->toArray();

            $studyHour = 0;
            $studyDuration = 0;

            foreach ($courseIds as $courseId) {
                $studyHour += $contentCourses->get($courseId)->studyHour($map[$courseId] ?? 0);
                $studyDuration += $map[$courseId] ?? 0;
            }

            CoursePack::query()
                ->where('org_id', $coursePack->org_id)
                ->where('course_pack_id', $coursePack->course_pack_id)
                ->update([
                    'duration' => $studyDuration,
                    'hour' => $studyHour
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('org_courses', function (Blueprint $table) {
            $table->dropColumn('duration');
            $table->dropColumn('lessons');
        });

        CoursePack::query()->update([
            'duration' => 0,
            'hour' => 0
        ]);
    }
};