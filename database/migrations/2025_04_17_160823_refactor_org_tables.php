<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE org_classes MODIFY COLUMN type ENUM('course', 'topic', 'course_pack') NOT NULL DEFAULT 'course';");

        DB::statement("ALTER TABLE org_enrollments MODIFY COLUMN type ENUM('course', 'topic', 'course_pack') NOT NULL DEFAULT 'course';");

        Schema::table('org_enrollments', function (Blueprint $table) {
            $table->dropColumn([
                'name', 'phone', 'photo', 'id_card_number', 'id_card_front', 'id_card_back',
                'extra', 'latest'
            ]);

            $table->unsignedInteger('student_id')->default(0)->comment('学员id')->after('class_id');
            $table->unsignedTinyInteger('archived')->default(0)->comment('是否存档')->after('hour_cert');
        });

        Schema::create('org_students', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('所属机构ID');
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->string('name', 20)->comment('姓名');
            $table->string('phone', 20)->comment('手机号码');
            $table->string('photo')->default('')->comment('照片');
            $table->char('id_card_number', 18)->comment('身份证号');
            $table->string('id_card_front')->comment('身份证正面图');
            $table->string('id_card_back')->comment('身份证反面图');
            $table->json('extra')->nullable()->comment('报名扩展信息');
            $table->timestamp('latest_start_at')->nullable()->comment('最近开课时间');
            $table->timestamp('latest_enroll_at')->nullable()->comment('最近报名时间');
            $table->timestamps();
            $table->comment('学员表');

            $table->unique(['org_id', 'user_id'], 'unq_org_user_id');
            $table->index(['user_id'], 'idx_user_id');
            $table->index(['phone'], 'idx_phone');
            $table->index(['id_card_number'], 'idx_id_card_number');
            $table->index(['latest_enroll_at'], 'idx_latest_enroll_at');
        });

        Schema::create('org_enroll_courses', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('所属机构ID');
            $table->unsignedInteger('enroll_id');
            $table->unsignedInteger('course_id');
            $table->unsignedInteger('learned_duration')->default(0)->comment('已学学时（秒数）');
            $table->tinyInteger('learn_finished')->default(0)->comment('是否已学完');
            $table->timestamps();
            $table->comment('学习的课程表');

            $table->index(['org_id'], 'idx_org_id');
            $table->index(['enroll_id'], 'enroll_id');
            $table->index(['course_id'], 'course_id');
        });

        Schema::create('org_enroll_archives', function (Blueprint $table) {
            $table->increments('enroll_id');
            $table->string('name', 20)->comment('姓名');
            $table->string('phone', 20)->comment('手机号码');
            $table->string('photo')->default('')->comment('照片');
            $table->char('id_card_number', 18)->comment('身份证号');
            $table->string('id_card_front')->comment('身份证正面图');
            $table->string('id_card_back')->comment('身份证反面图');
            $table->json('extra')->nullable()->comment('报名扩展信息');
            $table->timestamp('created_at')->nullable();
            $table->comment('班级学员存档表');
        });

        Schema::create('org_course_packs', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('org_id')->comment('机构ID');
            $table->unsignedInteger('course_pack_id')->comment('课程包ID');
            $table->unsignedTinyInteger('status')->default(1)->comment('状态:0=隐藏,1=显示');
            $table->decimal('price_original', 10)->default(0)->comment('原价');
            $table->decimal('price_sell', 10)->default(0)->comment('售价');
            $table->timestamps();
            $table->comment('机构课程包表');

            $table->index(['org_id'], 'idx_org_id');
            $table->index(['course_pack_id'], 'idx_course_pack_id');
        });

        Schema::table('cms_content_course_packs', function (Blueprint $table) {
            $table->unsignedInteger('topic_id')->default(0)->comment('关联题库')->after('content_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('org_enrollments', function (Blueprint $table) {
            $table->dropColumn(['student_id', 'archived']);

            $table->string('name', 20)->default('')->comment('姓名');
            $table->string('phone', 20)->default('')->comment('手机号码');
            // $table->string('photo')->default('')->comment('照片');
            $table->char('id_card_number', 18)->default('')->comment('身份证号');
            $table->string('id_card_front')->default('')->comment('身份证正面图');
            $table->string('id_card_back')->default('')->comment('身份证反面图');
            $table->json('extra')->nullable()->comment('报名扩展信息');
            $table->unsignedTinyInteger('latest')->default(0);
        });

        Schema::dropIfExists('org_students');

        Schema::dropIfExists('org_enroll_courses');

        Schema::dropIfExists('org_enroll_archives');

        Schema::dropIfExists('org_course_packs');

        Schema::table('cms_content_course_packs', function (Blueprint $table) {
            $table->dropColumn(['topic_id']);
        });
    }
};
