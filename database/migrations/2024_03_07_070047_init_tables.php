<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_operates', function (Blueprint $table) {
            $table->comment('操作记录表');
            $table->increments('id');
            $table->unsignedInteger('admin_id')->index('idx_admin_id')->comment('管理员ID');
            $table->string('remark',255)->comment('备注');
            $table->text('context');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('admins', function (Blueprint $table) {
            $table->comment('管理员账号表');
            $table->increments('id');
            $table->string('username', 21)->default('')->unique('uniq_username')->comment('用户名');
            $table->string('password', 128)->default('')->comment('密码');
            $table->string('real_name', 30)->default('')->comment('姓名');
            $table->string('phone', 20)->default('')->comment('手机号');
            $table->string('email', 32)->default('')->comment('邮箱');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 正常，1 禁用');
            $table->unsignedTinyInteger('is_admin')->default(0)->comment('是否超管 0 否，1 是');
            $table->timestamp('last_logged_at')->nullable()->comment('最近登录时间');
            $table->string('last_logged_ip', 15)->default('')->comment('最近登录IP');
            $table->timestamp('last_active_at')->nullable()->comment('最近活跃时间');
            $table->string('last_active_ip', 15)->default('')->comment('最近活跃IP');
            $table->timestamps();
        });

        Schema::create('qa_answers', function (Blueprint $table) {
            $table->comment('回答表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('question_id')->default(0)->index('idx_question_id')->comment('问题ID');
            $table->text('content')->nullable()->comment('内容');
            $table->unsignedInteger('like_count')->default(0)->comment('赞数量');
            $table->unsignedInteger('dislike_count')->default(0)->comment('踩数量');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态  0 待审核，1 正常，2 已拒绝');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('async_tasks', function (Blueprint $table) {
            $table->comment('异步任务表');
            $table->increments('id');
            $table->unsignedInteger('type')->default(0)->comment('任务类型 0 七牛视频转码，1 WPS开放平台文档转图片');
            $table->string('task_id', 64)->default('')->comment('开放平台任务ID');
            $table->string('business_type', 20)->default('')->comment('业务类型  Cms.material 资料，Cms.course 课程，Cms.news 咨询，question 提问，answer 回答，special 专题');
            $table->unsignedInteger('business_id')->default(0)->comment('业务ID');
            $table->text('task')->comment('序列化的任务实例');
            $table->json('callback_data')->nullable()->comment('任务回调数据');
            $table->json('result')->nullable()->comment('任务的本地处理结果');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态 0 进行中，1 已完成，2 失败');
            $table->string('description')->default('')->comment('任务状态描述');
            $table->timestamps();

            $table->index(['business_type', 'business_id'], 'idx_business_type_id');
            $table->index(['task_id', 'type'], 'idx_task');
        });

        Schema::create('attachment_relations', function (Blueprint $table) {
            $table->comment('附件文件引用表');
            $table->increments('id');
            $table->unsignedInteger('file_id')->index('idx_file_id')->comment('文件ID');
            $table->unsignedInteger('target_id')->comment('引用方ID');
            $table->string('target_type', 30)->comment('引用方类型');
            $table->timestamp('created_at')->nullable();

            $table->index(['target_id', 'target_type'], 'idx_target_id_type');
        });

        Schema::create('attachment_files', function (Blueprint $table) {
            $table->comment('附件文件表');
            $table->increments('id');
            $table->string('disk', 64)->default('')->comment('存储空间名称');
            $table->string('path')->default('')->comment('存储路径');
            $table->string('filename')->default('')->comment('文件名');
            $table->string('mime', 128)->default('')->comment('文件类型');
            $table->string('etag', 40)->default('')->unique('uniq_etag')->comment('文件哈希');
            $table->unsignedInteger('filesize')->default(0)->comment('文件大小');
            $table->unsignedSmallInteger('width')->default(0)->comment('图片宽度');
            $table->unsignedSmallInteger('height')->default(0)->comment('图片高度');
            $table->timestamp('created_at')->nullable()->index('idx_created_at');
        });

        Schema::create('cms_categories', function (Blueprint $table) {
            $table->comment('内容分类表');
            $table->increments('id');
            $table->string('name', 20)->default('')->comment('名称');
            $table->string('intro', 255)->default('')->comment('简介');
            $table->string('logo')->default('')->comment('图标');
            $table->unsignedInteger('pid')->default(0)->index('idx_pid')->comment('父ID');
            $table->string('path', 128)->default('')->index('idx_path')->comment('路径索引');
            $table->string('classify', 20)->default('')->index('idx_classify')->comment('大的归类，而不是业务类型 material 资料，course 课程，news 资讯');
            $table->unsignedTinyInteger('visible')->default(1)->comment('是否展示  0 否，1 是');
            $table->string('allow_types', 128)->default('')->comment('允许的内容类型  为空继承上级、或者指定多个类型');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
        });

        Schema::create('cms_content_course_chapters', function (Blueprint $table) {
            $table->comment('内容课程章表');
            $table->increments('id');
            $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->comment('内容ID');
            $table->string('name', 128)->default('')->comment('名称');
            $table->unsignedSmallInteger('sections_count')->default(0)->comment('学时数量');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态  0 隐藏, 1 显示');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
        });

        Schema::create('cms_content_course_progresses', function (Blueprint $table) {
            $table->comment('内容课程-小节进度表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->comment('内容ID');
            $table->unsignedInteger('chapter_id')->default(0)->index('idx_chapter_id')->comment('章ID');
            $table->unsignedInteger('section_id')->default(0)->index('idx_section_id')->comment('节ID');
            $table->unsignedSmallInteger('duration')->default(0)->comment('观看时长（单位：秒）');
            $table->unsignedSmallInteger('pos')->default(0)->comment('目前时间位置（单位：秒）');
            $table->unsignedTinyInteger('finished')->default(0)->comment('是否已看完  0 否，1 是（观看时长大于视频时长的60%，并且视频看到了最后10秒内则认为看完）');
            $table->timestamps();
        });

        Schema::create('cms_content_course_sections', function (Blueprint $table) {
            $table->comment('内容课程-小节表');
            $table->increments('id');
            $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->comment('内容ID');
            $table->unsignedInteger('chapter_id')->default(0)->index('idx_chapter_id')->comment('章ID');
            $table->string('name', 128)->default('')->comment('名称');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 隐藏, 1 显示');
            $table->string('filepath', 255)->default('')->comment('视频文件');
            $table->unsignedInteger('filesize')->default(0)->comment('文件大小');
            $table->json('extend')->nullable()->comment('扩展信息');
            $table->unsignedInteger('play_count')->default(0)->comment('播放次数');
            $table->unsignedInteger('play_complete_count')->default(0)->comment('播放完成次数');
            $table->unsignedSmallInteger('duration')->default(0)->comment('视频时长（单位：秒）');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
        });

        Schema::create('cms_content_courses', function (Blueprint $table) {
            $table->comment('内容课程表');
            $table->increments('content_id')->comment('内容ID');
            $table->string('teacher_name', 10)->default('')->comment('讲师名称');
            $table->unsignedInteger('learning_count')->default(0)->comment('在学人数');
            $table->unsignedSmallInteger('sections_count')->default(0)->comment('课程、学时数量');
            $table->unsignedTinyInteger('try_view_count')->default(0)->comment('试看课程节数');
            $table->timestamps();
        });

        Schema::create('cms_content_docs', function (Blueprint $table) {
            $table->comment('内容文档表');
            $table->increments('content_id')->comment('内容ID');
            $table->string('format', 10)->default('')->comment('格式  Word, Excel, PPT, PDF');
            $table->smallInteger('page_count')->default(0)->comment('页数');
            $table->unsignedInteger('filesize')->default(0)->comment('文件大小');
            $table->string('filepath', 255)->default('')->comment('文件路径');
            $table->string('filename', 128)->default('')->comment('文件名称');
            $table->unsignedInteger('download_count')->default(0)->comment('下载次数');
            $table->json('preview_images')->nullable()->comment('文档预览图列表');
            $table->timestamps();
        });

        Schema::create('cms_content_rich_texts', function (Blueprint $table) {
            $table->comment('内容富文本表');
            $table->increments('content_id')->comment('内容ID');
            $table->mediumText('content')->nullable()->comment('内容');
            $table->timestamps();
        });

        Schema::create('cms_content_relations', function (Blueprint $table) {
            $table->comment('内容关联表');
            $table->increments('id');
            $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->comment('内容ID');
            $table->unsignedInteger('related_id')->default(0)->index('idx_related_id')->comment('关联内容ID');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('cms_content_videos', function (Blueprint $table) {
            $table->comment('内容视频表');
            $table->increments('content_id')->comment('内容ID');
            $table->string('filepath')->default('')->comment('视频文件');
            $table->unsignedInteger('filesize')->default(0)->comment('文件大小');
            $table->unsignedSmallInteger('duration')->default(0)->comment('视频时长（单位：秒）');
            $table->json('extend')->nullable()->comment('扩展信息');
            $table->timestamps();
        });

        Schema::create('cms_contents', function (Blueprint $table) {
            $table->comment('内容主题表');
            $table->increments('id');
            $table->unsignedInteger('category_id')->default(0)->index('idx_category_id')->comment('分类ID');
            $table->string('title', 128)->default('')->comment('标题');
            $table->unsignedSmallInteger('type')->default(0)->index('idx_type')->comment('类型  1 文档，2 富文本，3 视频，4 章节视频(课程)');
            $table->string('cover')->default('')->comment('缩略图');
            $table->string('intro', 255)->default('')->comment('简介/描述');
            $table->unsignedTinyInteger('view_limit')->default(0)->comment('观看限制  0 免费，1 积分，2 收费');
            $table->unsignedSmallInteger('charge_credit')->default(0)->comment('收取积分');
            $table->decimal('charge_amount', 10)->default(0)->comment('收取价格');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 草稿，1 处理中，2 正常，3 隐藏');
            $table->string('status_desc', 128)->default('')->comment('状态描述');
            $table->unsignedInteger('views')->default(0)->comment('浏览次数');
            $table->string('source', 20)->default('')->comment('内容来源');
            $table->timestamp('release_at')->nullable()->comment('发布时间');
            $table->timestamp('recommend_at')->nullable()->index('idx_recommend_at')->comment('推荐时间');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('invitations', function (Blueprint $table) {
            $table->comment('邀请记录表');
            $table->increments('id');
            $table->unsignedInteger('referral_id')->default(0)->index('idx_referral_id')->comment('推荐人');
            $table->unsignedInteger('invitee_id')->default(0)->index('idx_invitee_id')->comment('被邀请人');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 已邀请，1 已奖励');
            $table->unsignedInteger('credit')->default(0)->comment('赠送的积分');
            $table->timestamp('send_at')->nullable()->comment('积分发放时间');
            $table->timestamps();
        });

        Schema::create('order_refunds', function (Blueprint $table) {
            $table->comment('退款表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('order_id')->default(0)->index('idx_order_id')->comment('订单ID');
            $table->unsignedInteger('payment_id')->default(0)->index('idx_payment_id')->comment('支付ID');
            $table->string('refund_no', 32)->default('')->index('uniq_refund_no')->comment('退款唯一编号');
            $table->decimal('refund_amount', 10)->unsigned()->default(0)->comment('退款金额');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态  0 退款中，1 已退款');
            $table->string('remark')->default('')->comment('退款理由');
            $table->timestamp('finished_at')->nullable()->comment('退款完成时间');
            $table->timestamps();
        });

        Schema::create('orders', function (Blueprint $table) {
            $table->comment('订单表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->string('order_no', 22)->default('')->unique('uniq_order_no')->comment('订单编号');
            $table->decimal('total_amount', 10)->unsigned()->default(0)->comment('订单金额');
            $table->decimal('payment_amount', 10)->unsigned()->default(0)->comment('支付金额');
            $table->string('title', 128)->default('')->comment('订单标题');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 待支付，1 已付款，2 已发起退款，3 退款处理中，4，已退款');
            $table->string('business_type', 20)->default('')->comment('业务类型');
            $table->unsignedInteger('business_id')->default(0)->comment('业务ID');
            $table->json('extend')->nullable()->comment('订单扩展信息');
            $table->unsignedInteger('payment_id')->default(0)->comment('支付ID');
            $table->timestamp('payment_at')->nullable()->comment('支付时间');
            $table->timestamp('created_at')->nullable()->index('idx_created_at');
            $table->timestamp('updated_at')->nullable();
        });

        Schema::create('order_payments', function (Blueprint $table) {
            $table->comment('支付表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('order_id')->default(0)->index('idx_order_id')->comment('订单ID');
            $table->string('out_trade_no', 25)->default('')->unique('uniq_out_trade_no')->comment('商户（我们自己）支付单号（使用订单编号作为前缀）');
            $table->string('transaction_no', 64)->nullable()->unique('uniq_trade_no')->comment('支付平台交易单号');
            $table->string('platform', 20)->default('')->comment('平台  alipay 支付宝支付，wechat 微信支付');
            $table->string('client', 20)->default('')->comment('客户端  mp 小程序');
            $table->decimal('amount', 10)->unsigned()->default(0)->comment('实际支付金额');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 待支付，1 已付款，2 已退款');
            $table->timestamp('payment_at')->nullable()->comment('支付时间');
            $table->timestamps();
        });

        Schema::create('qa_questions', function (Blueprint $table) {
            $table->comment('问题表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->string('title', 256)->default('')->comment('标题');
            $table->text('content')->comment('内容');
            $table->unsignedTinyInteger('anonymous')->default(0)->comment('是否匿名  0 否，1 是');
            $table->unsignedInteger('answer_count')->default(0)->comment('回答数量');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态  0 待审核，1 正常，2 已拒绝');
            $table->timestamp('recommend_at')->nullable()->comment('推荐时间（默认NULL代表不推荐，推荐按时间倒排）');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('cms_special_contents', function (Blueprint $table) {
            $table->comment('专题内容表');
            $table->increments('id');
            $table->unsignedInteger('special_id')->default(0)->index('idx_special_id')->comment('专题ID');
            $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->comment('内容ID');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('cms_specials', function (Blueprint $table) {
            $table->comment('专题表');
            $table->increments('id');
            $table->string('name', 128)->default('')->comment('名称');
            $table->string('intro', 512)->default('')->comment('简介');
            $table->string('cover')->default('')->comment('封面');
            $table->unsignedInteger('charge_credit')->default(0)->comment('积分价格（0代表免费）');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 草稿，1 正常，2 隐藏');
            $table->timestamp('recommend_at')->nullable()->comment('推荐时间（默认NULL代表不推荐，推荐按时间倒排）');
            $table->timestamps();
        });

        Schema::create('setting_booths', function (Blueprint $table) {
            $table->comment('系统位表');
            $table->increments('id');
            $table->unsignedTinyInteger('type')->default(0)->index('idx_type')->comment('0 幻灯，1 金刚区');
            $table->string('name', 20)->default('')->comment('名称');
            $table->string('image')->default('')->comment('图片');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->string('url')->default('')->comment('目标地址');
            $table->unsignedTinyInteger('enable')->default(0)->index('idx_enable')->comment('是否启用 0 否，1 是');
            $table->timestamps();
        });

        Schema::create('train_topics', function (Blueprint $table) {
            $table->comment('题库');
            $table->increments('id');
            $table->unsignedInteger('course_category_id')->default(0)->index('idx_course_category_id')->comment('关联讲解内容分类（默认为0）');
            $table->unsignedInteger('course_content_id')->default(0)->index('idx_course_content_id')->comment('关联讲解内容分类（默认为0）');
            $table->string('name', 512)->default('')->comment('名称');
            $table->decimal('amount', 10)->unsigned()->default(0)->comment('题库金额');
            $table->timestamp('next_exam_at')->nullable()->comment('下次考试时间');
            $table->unsignedSmallInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
        });

        Schema::create('train_test_subjects', function (Blueprint $table) {
            $table->comment('做题记录表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('topic_id')->default(0)->index('idx_topic_id')->comment('题库ID');
            $table->unsignedInteger('test_id')->default(0)->index('idx_test_id')->comment('测试ID');
            $table->unsignedInteger('subject_id')->default(0)->index('idx_subject_id')->comment('题目ID');
            $table->string('option_id')->default('0')->index('idx_option_id')->comment('选项ID（判断题默认为0，多选题多个ID逗号相连）');
            $table->unsignedTinyInteger('correct')->default(0)->comment('是否正确 0 否，1 是');
            $table->unsignedTinyInteger('wrong_removed')->default(0)->comment('是否移出错题记录 0 否，1 是');
            $table->timestamps();
        });

        Schema::create('train_tests', function (Blueprint $table) {
            $table->comment('考试表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('topic_id')->default(0)->index('idx_topic_id')->comment('题库ID');
            $table->unsignedInteger('current_subject_id')->default(0)->index('idx_current_subject_id')->comment('当前题目ID');
            $table->unsignedTinyInteger('type')->default(0)->comment('题型  0 顺序，1 模拟（随机抽取题目），2 专项训练-单选，3 专项训练-多选');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 进行中，1 已结束');
            $table->timestamp('end_at')->nullable()->comment('考试结束时间');
            $table->unsignedInteger('subject_count')->default(0)->comment('总题数（当时的总题数）');
            $table->unsignedInteger('subject_completed_count')->default(0)->comment('已做题数');
            $table->unsignedInteger('subject_correct_count')->default(0)->comment('正确题数');
            $table->decimal('score', 3, 1)->default(0)->comment('成绩');
            $table->timestamps();
        });

        Schema::create('train_subject_options', function (Blueprint $table) {
            $table->comment('题目选项表');
            $table->increments('id');
            $table->unsignedInteger('subject_id')->default(0)->index('idx_topic_id')->comment('题目ID');
            $table->unsignedSmallInteger('sn')->default(0)->comment('序号（同时用来排序）');
            $table->string('name', 512)->default('')->comment('选项名称');
            $table->string('image')->default('')->comment('图片');
            $table->unsignedTinyInteger('is_correct')->default(0)->comment('是否正确答案  0 否，1 是');
            $table->timestamps();
        });

        Schema::create('train_subjects', function (Blueprint $table) {
            $table->comment('题目');
            $table->increments('id');
            $table->unsignedInteger('topic_id')->default(0)->index('idx_topic_id')->comment('题库ID');
            $table->string('intro', 512)->default('')->comment('描述');
            $table->string('image')->default('')->comment('图片');
            $table->unsignedTinyInteger('type')->default(0)->comment('题型  1 单选，2 多选，3 判断');
            $table->unsignedTinyInteger('judge_correct')->default(0)->comment('判断题对错  1 错，2 对');
            $table->text('analysis')->nullable()->comment('解析');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('user_attitudes', function (Blueprint $table) {
            $table->comment('用户表态表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->string('business_type', 20)->default('')->comment('业务类型  Cms.material 资料，Cms.course 课程，Cms.news 咨询，question 提问，answer 回答，special 专题');
            $table->unsignedInteger('business_id')->default(0)->comment('业务ID');
            $table->unsignedInteger('attitude')->default(1)->comment('态度  1 赞，2 踩');
            $table->timestamp('created_at')->nullable();

            $table->index(['business_type', 'business_id'], 'idx_business_type_id');
        });

        Schema::create('user_binds', function (Blueprint $table) {
            $table->comment('三方账号绑定表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->string('platform', 20)->default('')->index('idx_platform')->comment('平台');
            $table->string('open_id', 40)->default('')->index('idx_open_id')->comment('OPEN_ID');
            $table->string('union_id', 40)->default('')->index('idx_union_id')->comment('UNION_ID');
            $table->string('access_token', 512)->default('')->comment('三方accessToken');
            $table->string('nickname', 64)->default('')->comment('昵称');
            $table->string('avatar', 255)->default('')->comment('头像');
            $table->timestamp('last_logged_at')->nullable()->comment('最后登录时间');
            $table->timestamps();
        });

        Schema::create('user_favorites', function (Blueprint $table) {
            $table->comment('用户收藏表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->string('business_type', 20)->default('')->comment('业务类型  Cms.material 资料，Cms.course 课程，Cms.news 咨询，question 提问，answer 回答，special 专题');
            $table->unsignedInteger('business_id')->default(0)->comment('业务ID');
            $table->timestamp('created_at')->nullable();

            $table->index(['business_type', 'business_id'], 'idx_business_type_id');
        });

        Schema::create('user_credit_logs', function (Blueprint $table) {
            $table->comment('用户积分记录表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->unsignedInteger('origin_credit')->default(0)->comment('变更前积分');
            $table->integer('change_credit')->default(0)->comment('最初变更积分数（正为加，负为减）');
            $table->integer('real_change_credit')->default(0)->comment('实际变更数量（如果退了为0，没退则与预计保持一致）');
            $table->enum('type', ['recharge', 'consume'])->comment('变更类型 recharge 收入，consume 减少');
            $table->string('business_type', 20)->default('')->comment('业务类型  Cms.material 资料，Cms.course 课程，Cms.news 咨询，question 提问，answer 回答，special 专题');
            $table->unsignedInteger('business_id')->default(0)->comment('业务ID');
            $table->string('remark')->default('')->comment('备注');
            $table->timestamps();

            $table->index(['business_type', 'business_id'], 'idx_business_type_id');
            $table->index(['user_id', 'type'], 'idx_user_id_type');
        });

        Schema::create('user_own_topics', function (Blueprint $table) {
            $table->comment('用户题库表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('topic_id')->default(0)->index('idx_topic_id')->comment('题库ID');
            $table->timestamp('expired_at')->nullable()->comment('题库到期时间');
            $table->timestamp('created_at')->nullable();
            $table->softDeletes();
        });

        Schema::create('user_content_downloads', function (Blueprint $table) {
            $table->comment('用户资料下载记录表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->comment('资料ID');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('user_own_contents', function (Blueprint $table) {
            $table->comment('用户资料表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('content_id')->default(0)->index('idx_content_id')->comment('资料ID');
            $table->string('classify', 20)->default('')->comment('归类 material 资料，course 课程，news 资讯');
            $table->timestamp('created_at')->nullable();
            $table->softDeletes();
        });

        Schema::create('user_properties', function (Blueprint $table) {
            $table->comment('用户属性表');
            $table->increments('user_id')->comment('内容ID');
            $table->text('tips')->nullable()->comment('提示标记');
            $table->unsignedInteger('current_topic_id')->default(0)->index('idx_current_topic_id')->comment('当前题库ID');
            $table->timestamp('info_edited_at')->nullable()->comment('账号修改时间');
            $table->timestamps();
        });

        Schema::create('visitors', function (Blueprint $table) {
            $table->comment('用户设备token表');
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->index('idx_user_id')->comment('用户ID');
            $table->char('token', 21)->default('')->comment('设备token')->index('token');
            $table->timestamp('last_active_at')->nullable()->comment('最后活跃时间');
            $table->timestamps();
        });

        Schema::create('users', function (Blueprint $table) {
            $table->comment('用户表');
            $table->increments('id');
            $table->string('uuid', 21)->default('')->unique('uniq_uuid')->comment('UUID');
            $table->string('nickname', 30)->default('')->comment('昵称');
            $table->string('avatar')->default('')->comment('头像');
            $table->string('phone', 20)->default('')->unique('uniq_phone')->comment('手机号');
            $table->string('password', 128)->default('')->comment('密码');
            $table->unsignedInteger('credit')->default(0)->comment('积分');
            $table->unsignedTinyInteger('status')->default(1)->index('idx_status')->comment('状态');
            $table->timestamp('last_logged_at')->nullable()->comment('最近登录时间');
            $table->string('last_logged_ip', 15)->default('')->comment('最近登录IP');
            $table->timestamp('last_active_at')->nullable()->comment('最近活跃时间');
            $table->string('last_active_ip', 15)->default('')->comment('最近活跃IP');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_operates');
        Schema::dropIfExists('admins');
        Schema::dropIfExists('qa_answers');
        Schema::dropIfExists('async_tasks');
        Schema::dropIfExists('attachment_relations');
        Schema::dropIfExists('attachment_files');
        Schema::dropIfExists('cms_categories');
        Schema::dropIfExists('cms_content_course_chapters');
        Schema::dropIfExists('cms_content_course_progresses');
        Schema::dropIfExists('cms_content_course_sections');
        Schema::dropIfExists('cms_content_courses');
        Schema::dropIfExists('cms_content_docs');
        Schema::dropIfExists('cms_content_rich_texts');
        Schema::dropIfExists('cms_content_relations');
        Schema::dropIfExists('cms_content_videos');
        Schema::dropIfExists('cms_contents');
        Schema::dropIfExists('invitations');
        Schema::dropIfExists('order_refunds');
        Schema::dropIfExists('orders');
        Schema::dropIfExists('order_payments');
        Schema::dropIfExists('qa_questions');
        Schema::dropIfExists('cms_special_contents');
        Schema::dropIfExists('cms_specials');
        Schema::dropIfExists('setting_booths');
        Schema::dropIfExists('train_topics');
        Schema::dropIfExists('train_test_subjects');
        Schema::dropIfExists('train_tests');
        Schema::dropIfExists('train_subject_options');
        Schema::dropIfExists('train_subjects');
        Schema::dropIfExists('user_attitudes');
        Schema::dropIfExists('user_binds');
        Schema::dropIfExists('user_favorites');
        Schema::dropIfExists('user_credit_logs');
        Schema::dropIfExists('user_own_topics');
        Schema::dropIfExists('user_content_downloads');
        Schema::dropIfExists('user_own_contents');
        Schema::dropIfExists('user_properties');
        Schema::dropIfExists('visitors');
        Schema::dropIfExists('users');
    }
};
