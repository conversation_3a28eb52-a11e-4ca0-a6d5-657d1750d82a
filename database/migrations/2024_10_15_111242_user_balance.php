<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('balance', 10, 2)->default(0)->comment('用户现金余额')->after('credit');
        });

        Schema::create('user_balance_records', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->index('user_id');
            $table->enum('type', ['income', 'expenses']);
            $table->decimal('origin_balance', 10, 2, true)->comment('原始余额');
            $table->decimal('amount', 10)->comment('消费金额');
            $table->string('business_type', 20)->default('')->comment('业务类型');
            $table->unsignedInteger('business_id')->default(0)->comment('业务ID');
            $table->string('remark', 255)->default('')->comment('描述');
            $table->timestamps();

            $table->index(['business_type', 'business_id'], 'business_type_id');

            $table->comment('用户余额消费记录');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->enum('payment_method', ['payment', 'balance'])->nullable()->comment('支付方式')->after('extend');
        });

        \App\Models\Order\Order::query()->whereNotNull('payment_at')->update(['payment_method'=>'payment']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropColumns('orders', 'payment_method');
        Schema::drop('user_balance_records');
        Schema::dropColumns('users', 'balance');
    }
};
