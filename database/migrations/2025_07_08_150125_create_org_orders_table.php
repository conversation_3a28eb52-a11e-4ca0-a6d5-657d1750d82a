<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('org_orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('org_id')->index('idx_org_id')->comment('机构ID');
            $table->unsignedInteger('user_id')->index('idx_user_id')->comment('用户ID');
            $table->unsignedInteger('student_id')->comment('学生ID');
            $table->unsignedInteger('source_id')->comment('源数据ID');
            $table->string('source_table', 50)->comment('源数据表名');
            $table->string('business_type', 20)->default('')->comment('业务类型');
            $table->string('name', 32)->default('')->comment('学生姓名');
            $table->string('phone', 11)->default('')->comment('学生手机号');
            $table->string('title', 128)->default('')->comment('订单标题');
            $table->string('order_no', 50)->nullable()->unique('uniq_order_no')->comment('订单编号');
            $table->string('transaction_no', 64)->nullable()->unique('uniq_trade_no')->comment('支付平台交易单号');
            $table->decimal('total_amount', 10, 2)->unsigned()->default(0)->comment('订单总金额');
            $table->decimal('payment_amount', 10, 2)->unsigned()->default(0)->comment('实际支付金额');
            $table->unsignedTinyInteger('payment_channel')->comment('支付渠道: 1线上, 2线下');
            $table->unsignedTinyInteger('status')->default(0)->index('idx_status')->comment('状态  0 待支付，1 已付款，2 已退款');
            $table->timestamp('payment_at')->nullable()->comment('支付时间');
            $table->timestamp('expired_at')->nullable()->comment('课程/题库到期时间');
            $table->timestamps();
            $table->unique(['payment_channel', 'source_id'], 'idx_channel_source');
            $table->comment("机构订单表，数据来自机构开课和用户自行购买记录");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('org_orders');
    }
};
