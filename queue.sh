#!/bin/sh

LARAVEL_PATH=$(dirname $(realpath $0))
PROCESS_COUNT=2
PID_FILE="$LARAVEL_PATH/storage/framework/queue-workers.pid"

check_running() {
  if [ -f "$PID_FILE" ]; then
    while read pid; do
      PID_EXISTS=$(ps aux | awk '{print $1}' | grep -w "$pid")
      if [ "$PID_EXISTS" ]; then
        echo "错误：队列进程已经在运行。请先停止它们。"
        exit 1
      fi
    done < "$PID_FILE"
  fi
}

start_workers() {
  check_running
  echo "启动 $PROCESS_COUNT 个 Laravel 队列 work 进程..."
  > "$PID_FILE"
  i=1
  while [ $i -le $PROCESS_COUNT ]; do
    php "$LARAVEL_PATH/artisan" queue:work &
    echo $! >> "$PID_FILE"
    echo "启动进程 $i，PID: $!"
    i=$((i + 1))
  done
  echo "所有队列进程已启动。"
}

stop_workers() {
  echo "停止 Laravel 队列 work 进程..."
  if [ -f "$PID_FILE" ]; then
    while read pid; do
      PID_EXISTS=$(ps aux | awk '{print $1}' | grep -w "$pid")
      if [ -n "$PID_EXISTS" ]; then
        kill "$pid"
        echo "已停止进程 PID: $pid"
      else
        echo "进程 PID: $pid 不存在"
      fi
    done < "$PID_FILE"
    rm "$PID_FILE"
    echo "所有队列进程已停止。"
  else
    echo "PID 文件不存在，没有队列进程需要停止。"
  fi
}

restart_workers() {
  stop_workers
  sleep 2
  start_workers
}

case "$1" in
  start)
    start_workers
    ;;
  stop)
    stop_workers
    ;;
  restart)
    restart_workers
    ;;
  *)
    echo "用法: $0 {start|stop|restart}"
    exit 1
    ;;
esac

exit 0
