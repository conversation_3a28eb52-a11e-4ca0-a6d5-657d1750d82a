<?php
declare(strict_types=1);

use ElasticAdapter\Indices\Mapping;
use ElasticAdapter\Indices\Settings;
use ElasticMigrations\Facades\Index;
use ElasticMigrations\MigrationInterface;

final class AddQuestionTags implements MigrationInterface
{
    /**
     * Run the migration.
     */
    public function up(): void
    {
        Index::putMapping('qa_questions', function(Mapping $mapping) {
            $mapping->integer('tags');
            $mapping->integer('views');
        });
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
    }
}
