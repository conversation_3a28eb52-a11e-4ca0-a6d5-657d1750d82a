<?php
declare(strict_types=1);

use ElasticMigrations\Facades\Index;
use ElasticMigrations\MigrationInterface;

final class CreateQaQuestionIndex implements MigrationInterface
{
    /**
     * Run the migration.
     */
    public function up(): void
    {
        $mapping = [
            'properties' => [
                'id' => [
                    'type' => 'unsigned_long'
                ],
                'user_id' => [
                    'type' => 'unsigned_long'
                ],
                'answer_count' => [
                    'type' => 'integer'
                ],
                'status' => [
                    'type' => 'integer'
                ],
                'nickname' => [
                    'type' => 'text'
                ],
                'title' => [
                    'type' => 'text',
                    'analyzer' => 'ik_max_word',
                    'search_analyzer' => "ik_smart"
                ],
                'content' => [
                    'type' => 'text',
                    'analyzer' => 'ik_max_word',
                    'search_analyzer' => "ik_smart"
                ],
                'created_at' => [
                    'type' => 'date',
                    "format" => "yyyy-MM-dd HH:mm:ss"
                ],
                'updated_at' => [
                    'type' => 'date',
                    "format" => "yyyy-MM-dd HH:mm:ss"
                ],
            ]
        ];
        $settings = [
            'max_result_window' => 10000000
        ];

        Index::createIfNotExistsRaw('qa_questions', $mapping, $settings);
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
        Index::dropIfExists('qa_questions');
    }
}
