<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'wps_converter' => [
        'app_id' => 'AK20240403SMLPOT',
        'app_secret' => 'iyzuGMUeZBYxUNFByVGHXBAoKECoSFHP'
    ],

    // 华为相关配置
    'huawei' => [
        'moderRation' => [
            'ak' => 'CIEY80GEB4ZMPQAZ83ME',
            'sk' => '6vTzsz7CaLxlCgSGaMKemOtVhvYUVLKEDNySiddv',
            'projectId' => '0f1804dfe90090ab2f28c0196a6abdc9'
        ]
    ],

    'chat' => [
        'ERNIE_Bot_4' => [
            'api_key' => "RqQdIzI7jIv28kvBgyN06FpV",
            'api_secret' => "U1kJ6mEkxvQkk0Xm1p6PUfaiio6rrjo9"
        ]
    ]
];
