<?php

/**
 * 合规宝业务配置
 */
return [
    /**
     * PC 端网页基础地址
     */
    'pc_url' => env('PC_URL'),
    /**
     * 注册设置
     */
    'register' => [
        /**
         * 新用户注册奖励积分
         * 0 代表不奖励
         */
        'reward_credit' => 50
    ],
    /**
     * 邀请奖励配置
     */
    'invitation' => [
        /**
         * 单个用户每天邀请的用户能获取奖励的数量
         * 0 代表不限制
         */
        'limit_per_day' => 0,
        /**
         * 每一个有奖励的邀请奖励多少积分
         * 0 代表不奖励
         */
        'reward_credit' => 50
    ],
    /**
     * 分享奖励配置
     */
    'share' => [
        /**
         * 单个用户每天分享能获取奖励的数量
         * 0 代表不限制
         */
        'limit_per_day' => 4,
        /**
         * 每一个有奖励的邀请奖励多少积分
         * 0 代表不奖励
         */
        'reward_credit' => 5
    ],
    /**
     * 修改用户信息奖励
     */
    'update_nickname_avatar' => [
        /**
         * 修改用户信息奖励
         * 0 代表不奖励
         */
        'reward_credit' => 10
    ],
    /**
     * 发动态奖励积分
     */
    'question' => [
        /**
         * 动态奖励积分
         * 0 代表不奖励
         */
        'reward_credit' => 2,
        /**
         * 单个用户每天邀请的用户能获取奖励的数量
         * 0 代表不限制
         */
        'limit_per_day' => 5
    ],
    /**
     * 存储区域配置
     */
    'storage' => [
        // 本地存储，用于程序读取，处理
        'local' => 'public',
        // 公共存储
        'pub' => env('FILESYSTEM_DISK', 'public'),
        // 私密存储，存储需要鉴权才能访问的数据
        'priv' => env('FILESYSTEM_DISK_PRIV')
    ],
    //充值配置
    'recharge' => [
        //充值每一元对应的积分倍数
        'credit_multiple' => 10
    ],
    /**
     * 测试用户
     * 测试用户在某些场景拥有特殊的权限，比如充值金额，系统维护时的访问权
     */
    'test_user_ids' => [1, 2, 3, 4, 5, 10, 6620, 8223],
    'hidden_videos' => false
];
