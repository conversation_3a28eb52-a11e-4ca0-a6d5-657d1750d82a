<?php

/**
 * Loki Stack Factory 配置
 */
return [
    /**
     * 在 LokiStackFactory 中是否启用
     */
    'enable' => env('LOKI_ENABLE', false),
    /**
     * 是否同时写本地文件日志
     */
    'with_file_handler' => env('LOKI_WITH_FILE', true),
    /**
     * 默认携带的 labels，最少要有一个
     */
    'default_labels' => [
        'project' => 'heguibao-server'
    ],
    /**
     * 使用的 Redis Push 链接，为 null 则使用默认
     */
    'connection' => 'loki'
];
